#!/usr/bin/env python3
"""
交易数据收集器 (Trading Data Collector)

专门负责收集和存储每日交易数据，包括：
1. 智能体输入数据（市场信号、分析输入、决策因子）
2. 智能体输出数据（交易决策、推荐、置信度分数）
3. 每日盈亏记录（每个智能体和整体系统）
4. 时间戳和市场条件记录

作者: AI Assistant
创建时间: 2025-07-04
"""

import os
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
import uuid

from .comprehensive_storage_manager import (
    ComprehensiveStorageManager, 
    TradingSessionData
)

@dataclass
class AgentInputData:
    """智能体输入数据结构"""
    agent_id: str
    market_signals: Dict[str, Any]
    analysis_inputs: Dict[str, Any]
    decision_factors: Dict[str, Any]
    timestamp: str
    metadata: Dict[str, Any]

@dataclass
class AgentOutputData:
    """智能体输出数据结构"""
    agent_id: str
    trading_decisions: Dict[str, Any]
    recommendations: List[Dict[str, Any]]
    confidence_scores: Dict[str, float]
    reasoning: str
    timestamp: str
    metadata: Dict[str, Any]

@dataclass
class MarketConditions:
    """市场条件数据结构"""
    timestamp: str
    stock_prices: Dict[str, Dict[str, float]]  # {symbol: {open, high, low, close, volume}}
    market_indicators: Dict[str, float]
    volatility_metrics: Dict[str, float]
    news_sentiment: Dict[str, Any]
    trading_volume: Dict[str, float]
    metadata: Dict[str, Any]

class TradingDataCollector:
    """
    交易数据收集器
    
    负责收集、验证和存储每日交易数据
    """
    
    def __init__(self, 
                 storage_manager: ComprehensiveStorageManager,
                 logger: Optional[logging.Logger] = None):
        """
        初始化交易数据收集器
        
        参数:
            storage_manager: 存储管理器实例
            logger: 日志记录器
        """
        self.storage_manager = storage_manager
        self.logger = logger or self._create_default_logger()
        
        # 当前交易会话数据
        self.current_session_id = None
        self.current_session_data = {}
        self.agent_inputs = {}
        self.agent_outputs = {}
        self.market_conditions = None
        self.profit_loss_records = {}
        
        self.logger.info("交易数据收集器初始化完成")
    
    def _create_default_logger(self) -> logging.Logger:
        """创建默认日志记录器"""
        logger = logging.getLogger(f"{__name__}.TradingDataCollector")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def start_trading_session(self, session_name: Optional[str] = None) -> str:
        """
        开始新的交易会话
        
        参数:
            session_name: 会话名称，如果为None则自动生成
            
        返回:
            会话ID
        """
        timestamp = datetime.now().isoformat()
        self.current_session_id = session_name or f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{uuid.uuid4().hex[:8]}"
        
        # 重置会话数据
        self.current_session_data = {
            "session_id": self.current_session_id,
            "start_time": timestamp,
            "status": "active"
        }
        self.agent_inputs = {}
        self.agent_outputs = {}
        self.market_conditions = None
        self.profit_loss_records = {}
        
        self.logger.info(f"开始交易会话: {self.current_session_id}")
        return self.current_session_id
    
    def collect_agent_input(self, input_data: AgentInputData) -> bool:
        """
        收集智能体输入数据
        
        参数:
            input_data: 智能体输入数据
            
        返回:
            是否收集成功
        """
        if not self.current_session_id:
            self.logger.error("没有活跃的交易会话，请先调用start_trading_session()")
            return False
        
        try:
            # 验证数据
            if not self._validate_agent_input(input_data):
                return False
            
            # 存储输入数据
            self.agent_inputs[input_data.agent_id] = asdict(input_data)
            
            self.logger.debug(f"收集智能体输入数据: {input_data.agent_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"收集智能体输入数据失败: {e}")
            return False
    
    def collect_agent_output(self, output_data: AgentOutputData) -> bool:
        """
        收集智能体输出数据
        
        参数:
            output_data: 智能体输出数据
            
        返回:
            是否收集成功
        """
        if not self.current_session_id:
            self.logger.error("没有活跃的交易会话，请先调用start_trading_session()")
            return False
        
        try:
            # 验证数据
            if not self._validate_agent_output(output_data):
                return False
            
            # 存储输出数据
            self.agent_outputs[output_data.agent_id] = asdict(output_data)
            
            self.logger.debug(f"收集智能体输出数据: {output_data.agent_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"收集智能体输出数据失败: {e}")
            return False
    
    def collect_market_conditions(self, market_data: MarketConditions) -> bool:
        """
        收集市场条件数据
        
        参数:
            market_data: 市场条件数据
            
        返回:
            是否收集成功
        """
        if not self.current_session_id:
            self.logger.error("没有活跃的交易会话，请先调用start_trading_session()")
            return False
        
        try:
            # 验证数据
            if not self._validate_market_conditions(market_data):
                return False
            
            # 存储市场条件数据
            self.market_conditions = asdict(market_data)
            
            self.logger.debug("收集市场条件数据完成")
            return True
            
        except Exception as e:
            self.logger.error(f"收集市场条件数据失败: {e}")
            return False
    
    def record_profit_loss(self, agent_id: str, pnl: float, details: Optional[Dict[str, Any]] = None) -> bool:
        """
        记录智能体盈亏
        
        参数:
            agent_id: 智能体ID
            pnl: 盈亏金额
            details: 详细信息
            
        返回:
            是否记录成功
        """
        if not self.current_session_id:
            self.logger.error("没有活跃的交易会话，请先调用start_trading_session()")
            return False
        
        try:
            self.profit_loss_records[agent_id] = {
                "pnl": pnl,
                "timestamp": datetime.now().isoformat(),
                "details": details or {}
            }
            
            self.logger.debug(f"记录智能体盈亏: {agent_id} = {pnl}")
            return True
            
        except Exception as e:
            self.logger.error(f"记录智能体盈亏失败: {e}")
            return False
    
    def calculate_system_pnl(self) -> float:
        """
        计算系统总盈亏
        
        返回:
            系统总盈亏
        """
        total_pnl = 0.0
        for agent_id, record in self.profit_loss_records.items():
            total_pnl += record.get("pnl", 0.0)
        
        return total_pnl
    
    def end_trading_session(self, session_summary: Optional[Dict[str, Any]] = None) -> bool:
        """
        结束当前交易会话并保存数据
        
        参数:
            session_summary: 会话摘要信息
            
        返回:
            是否保存成功
        """
        if not self.current_session_id:
            self.logger.error("没有活跃的交易会话")
            return False
        
        try:
            # 计算系统总盈亏
            system_pnl = self.calculate_system_pnl()
            
            # 准备交易决策数据
            trading_decisions = {}
            for agent_id, output in self.agent_outputs.items():
                trading_decisions[agent_id] = output.get("trading_decisions", {})
            
            # 准备盈亏数据
            pnl_data = {}
            for agent_id, record in self.profit_loss_records.items():
                pnl_data[agent_id] = record["pnl"]
            pnl_data["system_total"] = system_pnl
            
            # 创建交易会话数据
            session_data = TradingSessionData(
                session_id=self.current_session_id,
                timestamp=datetime.now().isoformat(),
                agent_inputs=self.agent_inputs,
                agent_outputs=self.agent_outputs,
                trading_decisions=trading_decisions,
                market_conditions=self.market_conditions or {},
                profit_loss=pnl_data,
                metadata={
                    "start_time": self.current_session_data.get("start_time"),
                    "end_time": datetime.now().isoformat(),
                    "session_summary": session_summary or {},
                    "total_agents": len(self.agent_outputs),
                    "system_pnl": system_pnl
                }
            )
            
            # 保存到存储管理器
            success = self.storage_manager.store_trading_session(session_data)
            
            if success:
                self.logger.info(f"交易会话结束并保存: {self.current_session_id} (系统盈亏: {system_pnl:.2f})")
                
                # 重置会话状态
                self.current_session_id = None
                self.current_session_data = {}
                self.agent_inputs = {}
                self.agent_outputs = {}
                self.market_conditions = None
                self.profit_loss_records = {}
                
                return True
            else:
                self.logger.error("保存交易会话数据失败")
                return False
                
        except Exception as e:
            self.logger.error(f"结束交易会话失败: {e}")
            return False
    
    def _validate_agent_input(self, data: AgentInputData) -> bool:
        """验证智能体输入数据"""
        try:
            if not data.agent_id or not data.timestamp:
                return False
            
            if not isinstance(data.market_signals, dict):
                return False
            
            if not isinstance(data.analysis_inputs, dict):
                return False
            
            if not isinstance(data.decision_factors, dict):
                return False
            
            # 验证时间戳格式
            datetime.fromisoformat(data.timestamp.replace('Z', '+00:00'))
            
            return True
            
        except Exception as e:
            self.logger.error(f"智能体输入数据验证失败: {e}")
            return False
    
    def _validate_agent_output(self, data: AgentOutputData) -> bool:
        """验证智能体输出数据"""
        try:
            if not data.agent_id or not data.timestamp:
                return False
            
            if not isinstance(data.trading_decisions, dict):
                return False
            
            if not isinstance(data.recommendations, list):
                return False
            
            if not isinstance(data.confidence_scores, dict):
                return False
            
            # 验证时间戳格式
            datetime.fromisoformat(data.timestamp.replace('Z', '+00:00'))
            
            return True
            
        except Exception as e:
            self.logger.error(f"智能体输出数据验证失败: {e}")
            return False
    
    def _validate_market_conditions(self, data: MarketConditions) -> bool:
        """验证市场条件数据"""
        try:
            if not data.timestamp:
                return False
            
            if not isinstance(data.stock_prices, dict):
                return False
            
            if not isinstance(data.market_indicators, dict):
                return False
            
            # 验证时间戳格式
            datetime.fromisoformat(data.timestamp.replace('Z', '+00:00'))
            
            return True
            
        except Exception as e:
            self.logger.error(f"市场条件数据验证失败: {e}")
            return False
