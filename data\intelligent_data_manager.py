#!/usr/bin/env python3
"""
智能数据管理器

基于现有的EnhancedDataManager，实现智能缓存策略的市场数据下载系统
支持增量更新、断点续传、数据质量检查和智能重试机制
"""

import os
import sys
import json
import sqlite3
import time
import hashlib
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Tuple, Set
import pandas as pd
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from experiment_config import (
    DATA_CACHE_CONFIG, DATA_QUALITY_CONFIG, ERROR_HANDLING_CONFIG,
    get_trading_calendar
)
from tools.enhanced_data_manager import EnhancedDataManager
from data.get_all_data import manage_stock_data
from utils.comprehensive_logger import SimpleLogger

class IntelligentDataManager:
    """
    智能数据管理器
    
    提供智能缓存、增量更新、断点续传等高级数据管理功能
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None, logger: Optional[SimpleLogger] = None):
        """
        初始化智能数据管理器

        Args:
            config: 配置字典，默认使用experiment_config中的配置
            logger: 日志记录器
        """
        if config is None:
            self.config = {
                "cache": DATA_CACHE_CONFIG,
                "quality": DATA_QUALITY_CONFIG,
                "error_handling": ERROR_HANDLING_CONFIG,
                "concurrency": {"max_workers": 1},  # 禁用并发
            }
        else:
            self.config = config

        self.logger = logger or SimpleLogger("IntelligentDataManager")
        
        # 初始化缓存目录
        self.cache_dir = self.config["cache"]["cache_dir"]
        os.makedirs(self.cache_dir, exist_ok=True)
        
        # 初始化状态跟踪
        self.download_status = {}
        self.data_quality_cache = {}
        self.retry_counts = {}
        
        # 线程锁
        self._lock = threading.RLock()
        
        # 初始化基础数据管理器
        self.base_manager = EnhancedDataManager(
            force_refresh=False,
            verbose=True
        )
        
        self.logger.info("智能数据管理器初始化完成")
    
    def download_comprehensive_dataset(self, 
                                     stocks: List[str], 
                                     start_date: str, 
                                     end_date: str,
                                     force_refresh: bool = False) -> Dict[str, bool]:
        """
        下载综合数据集
        
        Args:
            stocks: 股票代码列表
            start_date: 开始日期
            end_date: 结束日期
            force_refresh: 是否强制刷新
            
        Returns:
            每个股票的下载状态
        """
        self.logger.info(f"开始下载综合数据集: {len(stocks)}只股票, {start_date} 到 {end_date}")
        
        # 检查缓存状态
        cache_status = self._check_cache_status(stocks, start_date, end_date)
        
        # 确定需要下载的股票
        stocks_to_download = []
        for stock in stocks:
            if force_refresh or not cache_status.get(stock, {}).get("is_valid", False):
                stocks_to_download.append(stock)
            else:
                self.logger.info(f"{stock}: 使用缓存数据")
        
        if not stocks_to_download:
            self.logger.info("所有数据都是最新的，无需下载")
            return {stock: True for stock in stocks}
        
        self.logger.info(f"需要下载/更新的股票: {stocks_to_download}")
        
        # 并行下载
        results = self._parallel_download(stocks_to_download, start_date, end_date)
        
        # 合并结果
        final_results = {}
        for stock in stocks:
            if stock in results:
                final_results[stock] = results[stock]
            else:
                final_results[stock] = True  # 使用缓存的股票
        
        # 更新缓存状态
        self._update_cache_status(stocks, start_date, end_date, final_results)
        
        success_count = sum(1 for success in final_results.values() if success)
        self.logger.info(f"数据下载完成: {success_count}/{len(stocks)} 成功")
        
        return final_results
    
    def _check_cache_status(self, stocks: List[str], start_date: str, end_date: str) -> Dict[str, Dict[str, Any]]:
        """检查缓存状态"""
        cache_status = {}
        max_age = timedelta(days=self.config["cache"]["max_cache_age_days"])
        
        for stock in stocks:
            cache_info = self._get_cache_info(stock)
            
            # 检查缓存有效性
            is_valid = False
            if cache_info.get("last_cache_update"):
                try:
                    last_update = datetime.fromisoformat(cache_info["last_cache_update"])
                    is_valid = (datetime.now() - last_update) < max_age
                    
                    # 检查数据范围覆盖
                    if is_valid:
                        ohlcv_range = cache_info.get("ohlcv_range", {})
                        if (ohlcv_range.get("start") and ohlcv_range.get("end")):
                            cache_start = datetime.strptime(ohlcv_range["start"], "%Y-%m-%d")
                            cache_end = datetime.strptime(ohlcv_range["end"], "%Y-%m-%d")
                            req_start = datetime.strptime(start_date, "%Y-%m-%d")
                            req_end = datetime.strptime(end_date, "%Y-%m-%d")
                            
                            is_valid = cache_start <= req_start and cache_end >= req_end
                except:
                    is_valid = False
            
            cache_status[stock] = {
                "is_valid": is_valid,
                "cache_info": cache_info,
                "needs_update": not is_valid
            }
        
        return cache_status
    
    def _parallel_download(self, stocks: List[str], start_date: str, end_date: str) -> Dict[str, bool]:
        """并行下载数据"""
        results = {}
        
        for stock in stocks:
            try:
                success = self._download_single_stock(stock, start_date, end_date)
                results[stock] = success
                
                if success:
                    self.logger.info(f"✅ {stock} 数据下载成功")
                else:
                    self.logger.warning(f"❌ {stock} 数据下载失败")
                    
            except Exception as e:
                self.logger.error(f"❌ {stock} 下载过程中发生异常: {e}")
                results[stock] = False
        
        return results
    
    def _download_single_stock(self, stock: str, start_date: str, end_date: str) -> bool:
        """下载单个股票的数据"""
        max_retries = self.config["error_handling"]["max_retries"]
        retry_delay = self.config["error_handling"]["retry_delay"]
        
        for attempt in range(max_retries + 1):
            try:
                self.logger.info(f"下载 {stock} 数据 (尝试 {attempt + 1}/{max_retries + 1})")
                
                # 使用现有的数据管理函数
                success = manage_stock_data(stock, start_date, end_date, verbose=False)
                
                if success:
                    # 验证数据质量
                    if self._validate_data_quality(stock, start_date, end_date):
                        self._update_stock_cache_info(stock, start_date, end_date)
                        return True
                    else:
                        self.logger.warning(f"{stock} 数据质量验证失败")
                        if attempt < max_retries:
                            continue
                
            except Exception as e:
                self.logger.error(f"{stock} 下载失败 (尝试 {attempt + 1}): {e}")
                
                if attempt < max_retries:
                    self.logger.info(f"等待 {retry_delay} 秒后重试...")
                    time.sleep(retry_delay)
                    continue
            
            # 如果是最后一次尝试，记录失败
            if attempt == max_retries:
                self.logger.error(f"{stock} 数据下载最终失败")
                return False
        
        return False
    
    def _validate_data_quality(self, stock: str, start_date: str, end_date: str) -> bool:
        """验证数据质量"""
        if not self.config["quality"]["enable_data_validation"]:
            return True
        
        try:
            # 获取数据库路径
            db_path = os.path.join("data", "tickers", stock, f"{stock}_data.db")
            if not os.path.exists(db_path):
                return False
            
            # 连接数据库
            conn = sqlite3.connect(db_path)
            
            # 检查OHLCV数据完整性
            trading_days = get_trading_calendar(start_date, end_date)
            expected_days = len(trading_days)
            
            cursor = conn.cursor()
            cursor.execute("""
                SELECT COUNT(*) FROM ohlcv 
                WHERE ticker = ? AND trade_date BETWEEN ? AND ?
            """, (stock, start_date, end_date))
            
            actual_days = cursor.fetchone()[0]
            completeness = actual_days / expected_days if expected_days > 0 else 0
            
            conn.close()
            
            # 检查完整性阈值
            min_completeness = self.config["quality"]["min_data_completeness"]
            is_valid = completeness >= min_completeness
            
            if not is_valid:
                self.logger.warning(f"{stock} 数据完整性不足: {completeness:.2%} < {min_completeness:.2%}")
            
            return is_valid
            
        except Exception as e:
            self.logger.error(f"数据质量验证失败 {stock}: {e}")
            return False
    
    def _get_cache_info(self, stock: str) -> Dict[str, Any]:
        """获取缓存信息"""
        cache_file = os.path.join(self.cache_dir, f"{stock}_cache_info.json")
        
        if os.path.exists(cache_file):
            try:
                with open(cache_file, 'r') as f:
                    return json.load(f)
            except:
                pass
        
        return {
            "stock": stock,
            "ohlcv_range": {"start": None, "end": None},
            "news_range": {"start": None, "end": None},
            "fundamental_last_update": None,
            "last_cache_update": None,
            "data_quality_score": None,
        }
    
    def _update_stock_cache_info(self, stock: str, start_date: str, end_date: str) -> None:
        """更新单个股票的缓存信息"""
        cache_info = self._get_cache_info(stock)
        
        # 更新OHLCV范围
        current_start = cache_info["ohlcv_range"]["start"]
        current_end = cache_info["ohlcv_range"]["end"]
        
        if not current_start or start_date < current_start:
            cache_info["ohlcv_range"]["start"] = start_date
        if not current_end or end_date > current_end:
            cache_info["ohlcv_range"]["end"] = end_date
        
        # 更新时间戳
        cache_info["last_cache_update"] = datetime.now().isoformat()
        
        # 保存缓存信息
        cache_file = os.path.join(self.cache_dir, f"{stock}_cache_info.json")
        with open(cache_file, 'w') as f:
            json.dump(cache_info, f, indent=2)
    
    def _update_cache_status(self, stocks: List[str], start_date: str, end_date: str, results: Dict[str, bool]) -> None:
        """更新缓存状态"""
        for stock in stocks:
            if results.get(stock, False):
                self._update_stock_cache_info(stock, start_date, end_date)
    
    def get_data_summary(self, stocks: List[str]) -> Dict[str, Any]:
        """获取数据摘要"""
        summary = {
            "total_stocks": len(stocks),
            "cached_stocks": 0,
            "valid_cache_stocks": 0,
            "stocks_status": {},
            "cache_size_mb": 0,
        }
        
        for stock in stocks:
            cache_info = self._get_cache_info(stock)
            has_cache = bool(cache_info.get("last_cache_update"))
            
            if has_cache:
                summary["cached_stocks"] += 1
                
                # 检查缓存是否有效
                try:
                    last_update = datetime.fromisoformat(cache_info["last_cache_update"])
                    max_age = timedelta(days=self.config["cache"]["max_cache_age_days"])
                    is_valid = (datetime.now() - last_update) < max_age
                    
                    if is_valid:
                        summary["valid_cache_stocks"] += 1
                except:
                    is_valid = False
            else:
                is_valid = False
            
            summary["stocks_status"][stock] = {
                "has_cache": has_cache,
                "is_valid": is_valid,
                "last_update": cache_info.get("last_cache_update"),
                "ohlcv_range": cache_info.get("ohlcv_range"),
            }
        
        # 计算缓存大小
        try:
            cache_size = sum(
                os.path.getsize(os.path.join(self.cache_dir, f))
                for f in os.listdir(self.cache_dir)
                if os.path.isfile(os.path.join(self.cache_dir, f))
            )
            summary["cache_size_mb"] = cache_size / (1024 * 1024)
        except:
            summary["cache_size_mb"] = 0
        
        return summary
    
    def cleanup_cache(self, max_age_days: Optional[int] = None) -> int:
        """清理过期缓存"""
        max_age = timedelta(days=max_age_days or self.config["cache"]["max_cache_age_days"] * 2)
        cleaned_count = 0
        
        try:
            for filename in os.listdir(self.cache_dir):
                filepath = os.path.join(self.cache_dir, filename)
                if os.path.isfile(filepath):
                    file_age = datetime.now() - datetime.fromtimestamp(os.path.getmtime(filepath))
                    if file_age > max_age:
                        os.remove(filepath)
                        cleaned_count += 1
                        self.logger.info(f"清理过期缓存文件: {filename}")
        except Exception as e:
            self.logger.error(f"清理缓存时发生错误: {e}")
        
        return cleaned_count

if __name__ == "__main__":
    # 测试智能数据管理器
    manager = IntelligentDataManager()
    
    # 测试数据下载
    test_stocks = ["AAPL", "GOOGL"]
    test_start = "2024-01-01"
    test_end = "2024-01-31"
    
    print("测试智能数据管理器")
    print("=" * 50)
    
    # 获取数据摘要
    summary = manager.get_data_summary(test_stocks)
    print(f"数据摘要: {summary}")
    
    # 下载数据
    results = manager.download_comprehensive_dataset(test_stocks, test_start, test_end)
    print(f"下载结果: {results}")
