#!/usr/bin/env python3
"""
多智能体贡献度评估执行脚本

本脚本是多智能体贡献度评估系统的用户入口，提供命令行接口来运行
完整的四阶段贡献度评估流程，并输出详细的Shapley值计算结果。

使用方法:
    python run_contribution_assessment.py [选项]

示例:
    # 运行快速测试
    python run_contribution_assessment.py --quick-test
    
    # 运行完整评估
    python run_contribution_assessment.py --start-date 2023-01-01 --end-date 2023-01-31
    
    # 使用LLM进行分析
    python run_contribution_assessment.py --llm-provider zhipuai
    
    # 运行指定智能体的评估
    python run_contribution_assessment.py --agents NAA TAA TRA --max-coalitions 5
"""

import argparse
import sys
import json
import time
from datetime import datetime
from typing import Dict, Any, List, Optional
from dotenv import load_dotenv

# 导入贡献度评估系统
try:
    from contribution_assessment import ContributionAssessor
except ImportError as e:
    print(f"❌ 导入贡献度评估模块失败: {e}", file=sys.stderr)
    print("请确保在项目根目录下运行此脚本", file=sys.stderr)
    sys.exit(1)


def parse_arguments() -> argparse.Namespace:
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="多智能体贡献度评估系统",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  %(prog)s --quick-test                    # 运行快速测试
  %(prog)s --start-date 2023-01-01 --end-date 2023-01-31  # 完整评估
  %(prog)s --weekly-evaluation             # 启用周期性Shapley值计算
  %(prog)s --llm-provider zhipuai           # 使用LLM进行分析
  %(prog)s --agents NAA TAA TRA --max-coalitions 5        # 指定智能体
        """
    )
    
    # 基本配置
    parser.add_argument(
        "--quick-test", 
        action="store_true",
        help="运行快速测试（使用简化配置）"
    )
    
    parser.add_argument(
        "--start-date", 
        type=str, 
        default="2023-01-01",
        help="开始日期 (YYYY-MM-DD，默认: 2023-01-01)"
    )
    
    parser.add_argument(
        "--end-date", 
        type=str, 
        default="2023-01-31",
        help="结束日期 (YYYY-MM-DD，默认: 2023-01-31)"
    )
    
    parser.add_argument(
        "--stocks", 
        nargs="+", 
        default=["AAPL"],
        help="股票代码列表 (默认: AAPL)"
    )
    
    parser.add_argument(
        "--starting-cash", 
        type=float, 
        default=1000000,
        help="初始资金 (默认: 1000000)"
    )
    
    # 智能体与LLM配置
    parser.add_argument(
        "--agents", 
        nargs="+", 
        default=None,
        help="目标智能体列表 (默认: 所有智能体)"
    )
    
    parser.add_argument(
        "--llm-provider",
        type=str,
        default=None,
        help="指定LLM提供商 (例如 'zhipuai')。如果设置，将使用LLM进行分析"
    )
    
    # 周期性评估配置（新增）
    parser.add_argument(
        "--weekly-evaluation",
        action="store_true",
        help="启用周期性Shapley值计算和策略诊断"
    )
    
    parser.add_argument(
        "--trading-days-per-week",
        type=int,
        default=5,
        help="每周交易日数 (默认: 5)"
    )
    
    parser.add_argument(
        "--performance-threshold",
        type=float,
        default=0.8,
        help="性能下降阈值，用于触发策略调整建议 (默认: 0.8)"
    )
    
    parser.add_argument(
        "--daily-llm",
        action="store_true",
        help="启用每日LLM交易模式（每个交易日都调用LLM）"
    )
    
    # 模拟配置
    parser.add_argument(
        "--simulation-days", 
        type=int, 
        default=None,
        help="模拟天数 (默认: 使用日期范围)"
    )
    
    parser.add_argument(
        "--max-coalitions", 
        type=int, 
        default=None,
        help="最大模拟联盟数量 (默认: 模拟所有有效联盟)"
    )
    
    parser.add_argument(
        "--risk-free-rate", 
        type=float, 
        default=0.02,
        help="无风险收益率 (默认: 0.02)"
    )
    
    # 输出配置
    parser.add_argument(
        "--output-file", 
        type=str, 
        default=None,
        help="输出结果到JSON文件"
    )
    
    parser.add_argument(
        "--verbose", 
        action="store_true",
        help="显示详细输出"
    )
    
    parser.add_argument(
        "--quiet", 
        action="store_true",
        help="静默模式（只显示最终结果）"
    )
    
    return parser.parse_args()


def create_config_from_args(args: argparse.Namespace) -> Dict[str, Any]:
    """从命令行参数创建配置字典"""
    config = {
        "start_date": args.start_date,
        "end_date": args.end_date,
        "stocks": args.stocks,
        "starting_cash": args.starting_cash,
        "risk_free_rate": args.risk_free_rate,
        "simulation_days": args.simulation_days,
        "fail_on_large_gaps": False,
        "fill_date_gaps": True,
        "verbose": args.verbose and not args.quiet
    }
    
    return config


def print_header(args: argparse.Namespace) -> None:
    """打印程序头部信息"""
    if args.quiet:
        return
        
    print("=" * 70)
    print("🚀 多智能体贡献度评估系统")
    print("=" * 70)
    print(f"📅 评估期间: {args.start_date} 至 {args.end_date}")
    print(f"📈 股票代码: {', '.join(args.stocks)}")
    print(f"💰 初始资金: ${args.starting_cash:,.0f}")
    
    if args.llm_provider:
        print(f"🧠 LLM提供商: {args.llm_provider}")
    else:
        print("🧠 LLM分析: 未激活")

    if args.agents:
        print(f"🤖 目标智能体: {', '.join(args.agents)}")
    else:
        print("🤖 目标智能体: 所有智能体")
    
    if args.max_coalitions:
        print(f"🔢 最大联盟数: {args.max_coalitions}")
    
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()


def print_results(result: Dict[str, Any], args: argparse.Namespace) -> None:
    """打印评估结果"""
    if not result["success"]:
        print(f"❌ 评估失败: {result.get('error', '未知错误')}")
        return
    
    # 基本信息
    if not args.quiet:
        print("=" * 70)
        print("📊 贡献度评估结果")
        print("=" * 70)
    
    # Shapley值结果
    print("\n🏆 智能体贡献度排名 (Shapley值):")
    shapley_values = result["shapley_values"]
    sorted_agents = sorted(shapley_values.items(), key=lambda x: x[1], reverse=True)
    
    for i, (agent, value) in enumerate(sorted_agents, 1):
        print(f"  {i}. {agent}: {value:.6f}")
    
    # 摘要信息
    if not args.quiet:
        summary = result["summary"]
        print(f"\n📈 评估摘要:")
        print(f"  • 总智能体数: {summary['total_agents']}")
        print(f"  • 生成联盟数: {summary['total_coalitions_generated']}")
        print(f"  • 模拟联盟数: {summary['coalitions_simulated']}")
        print(f"  • 模拟成功率: {summary['simulation_success_rate']:.1f}%")
        print(f"  • 系统总价值: {summary['total_system_value']:.6f}")
        print(f"  • 执行时间: {result['execution_time']:.2f}s")
        
        # 最大贡献者
        top_contributor = summary['top_contributor']
        print(f"  • 最大贡献者: {top_contributor[0]} ({top_contributor[1]:.6f})")
    
    # 详细分析（仅在verbose模式下）
    if args.verbose and not args.quiet:
        analysis = result["shapley_analysis"]
        print(f"\n🔍 详细分析:")
        print(f"  • 平均贡献: {analysis['mean_value']:.6f}")
        print(f"  • 标准差: {analysis['value_distribution']['std']:.6f}")
        print(f"  • 正贡献者: {analysis['positive_contributors']}")
        print(f"  • 负贡献者: {analysis['negative_contributors']}")
        print(f"  • 零贡献者: {analysis['zero_contributors']}")
        
        # 阶段执行时间
        phases = result["phase_results"]
        print(f"\n⏱️  阶段执行时间:")
        for phase_name, phase_result in phases.items():
            if "execution_time" in phase_result:
                print(f"  • {phase_name}: {phase_result['execution_time']:.3f}s")


def save_results(result: Dict[str, Any], output_file: str) -> None:
    """保存结果到JSON文件"""
    try:
        # 深度处理不可序列化的对象
        def make_serializable(obj):
            if isinstance(obj, dict):
                return {str(k): make_serializable(v) for k, v in obj.items()}
            elif isinstance(obj, (list, tuple)):
                return [make_serializable(item) for item in obj]
            elif isinstance(obj, set):
                return list(obj)
            elif isinstance(obj, frozenset):
                return list(obj)
            elif hasattr(obj, '__dict__'):
                return str(obj)
            else:
                return obj

        serializable_result = make_serializable(result)

        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(serializable_result, f, indent=2, ensure_ascii=False, default=str)

        print(f"✅ 结果已保存到: {output_file}")

    except Exception as e:
        print(f"❌ 保存结果失败: {e}")


def create_agent_instances(llm_provider: Optional[str] = None) -> Dict[str, Any]:
    """
    创建实际的智能体实例

    参数:
        llm_provider: LLM提供商名称

    返回:
        智能体实例字典
    """
    try:
        from agents.agent_factory import AgentFactory
        from contribution_assessment.llm_interface import LLMInterface

        # 创建LLM接口
        llm_interface = None
        if llm_provider:
            llm_interface = LLMInterface(provider=llm_provider)
            if not llm_interface.client:
                print(f"⚠️  LLM接口初始化失败，将使用模拟智能体")
                return {}

        # 创建智能体工厂
        agent_factory = AgentFactory(llm_interface=llm_interface)

        # 创建所有智能体实例
        agent_instances = {}
        agent_ids = ["NAA", "TAA", "FAA", "BOA", "BeOA", "NOA", "TRA"]

        for agent_id in agent_ids:
            try:
                agent_instance = agent_factory.create_agent(agent_id)
                if agent_instance:
                    agent_instances[agent_id] = agent_instance
                    print(f"✅ 成功创建智能体: {agent_id}")
                else:
                    print(f"⚠️  创建智能体失败: {agent_id}")
            except Exception as e:
                print(f"❌ 创建智能体 {agent_id} 时出错: {e}")

        return agent_instances

    except ImportError as e:
        print(f"❌ 导入智能体模块失败: {e}")
        print("将使用模拟智能体")
        return {}


def main() -> None:
    """主函数"""
    # 解析命令行参数
    load_dotenv() # 加载 .env 文件中的环境变量
    args = parse_arguments()

    import debugpy
    debugpy.listen(("localhost", 63851))
    print("开始等待调试器连接...")
    debugpy.wait_for_client()
    print("调试器已连接，继续执行...")

    # 打印头部信息
    print_header(args)

    try:
        # 创建配置
        config = create_config_from_args(args)

        # 创建智能体实例（如果指定了LLM提供商）
        agent_instances = {}
        if args.llm_provider:
            if not args.quiet:
                print(f"🤖 正在创建LLM智能体实例 (提供商: {args.llm_provider})...")
            agent_instances = create_agent_instances(args.llm_provider)

            if agent_instances:
                if not args.quiet:
                    print(f"✅ 成功创建 {len(agent_instances)} 个智能体实例")
            else:
                if not args.quiet:
                    print("⚠️  未能创建智能体实例，将使用模拟智能体")
        else:
            if not args.quiet:
                print("ℹ️  未指定LLM提供商，将使用模拟智能体")

        # 创建评估器
        assessor = ContributionAssessor(
            config=config,
            agents=agent_instances,  # 传入智能体实例
            llm_provider=args.llm_provider
        )

        # 运行评估
        if args.quick_test:
            if not args.quiet:
                print("🧪 运行快速测试...")
            result = assessor.run_quick_test()
        else:
            if not args.quiet:
                print("🚀 开始完整贡献度评估...")
            result = assessor.run(
                agents=agent_instances,  # 确保传入智能体实例
                target_agents=args.agents,
                max_coalitions=args.max_coalitions
            )

        # 打印结果
        print_results(result, args)

        # 保存结果（如果指定）
        if args.output_file:
            save_results(result, args.output_file)

        # 成功退出
        if not args.quiet:
            print(f"\n✅ 评估完成")

    except KeyboardInterrupt:
        print("\n⚠️  用户中断评估")
        sys.exit(1)
        
    except Exception as e:
        print(f"\n❌ 评估过程中发生错误: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
