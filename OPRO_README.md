# OPRO集成使用指南

## 概述

本项目已成功集成OPRO（Optimization by PROmpting）方法，实现了基于历史Shapley值的智能体提示词自动优化功能。

## 核心特性

### 🚀 OPRO优化引擎
- **智能提示词生成**: 基于历史Shapley得分生成优化的提示词候选
- **并行评估**: 支持多候选提示词的并行性能评估
- **自动选择**: 自动选择性能最佳的提示词候选

### 📊 历史得分管理
- **数据持久化**: SQLite数据库存储优化历史和得分数据
- **趋势分析**: 智能体性能趋势分析和预测
- **跨智能体比较**: 提供智能体间的性能对比分析

### 🤖 动态智能体系统
- **OPROBaseAgent**: 支持动态提示词更新的基础智能体类
- **版本控制**: 完整的提示词版本历史记录
- **A/B测试**: 支持提示词的A/B测试功能
- **自动回滚**: 性能下降时的自动回滚机制

### 🔄 完整集成流程
- **评估前优化**: 可选的评估前提示词优化
- **实时更新**: 评估后自动更新历史得分
- **评估后优化**: 基于最新得分的后续优化

## 快速开始

### 1. 环境准备

确保已安装所有依赖并设置了LLM API密钥：

```bash
# 设置ZhipuAI API密钥
export ZHIPUAI_API_KEY=your_api_key

# 或者创建.env文件
echo "ZHIPUAI_API_KEY=your_api_key" > .env
```

### 2. 快速验证

运行快速测试验证OPRO功能：

```bash
python quick_opro_test.py --provider zhipuai
```

### 3. 完整功能测试

运行完整的集成测试：

```bash
# 基础系统测试
python test_opro_integration.py --provider zhipuai --test-mode basic

# OPRO完整测试
python test_opro_integration.py --provider zhipuai --test-mode full --enable-opro

# 仅OPRO功能测试
python test_opro_integration.py --provider zhipuai --test-mode opro-only --enable-opro
```

## 使用方法

### 运行模式

#### 1. 标准评估模式（不含OPRO）
```bash
python run_opro_system.py --provider zhipuai --mode evaluation
```

#### 2. OPRO优化模式
```bash
python run_opro_system.py --provider zhipuai --mode optimization --enable-opro
```

#### 3. 集成模式（评估+优化）
```bash
python run_opro_system.py --provider zhipuai --mode integrated --enable-opro
```

#### 4. OPRO仪表板模式
```bash
python run_opro_system.py --provider zhipuai --mode dashboard --enable-opro
```

### 高级选项

#### 指定目标智能体
```bash
python run_opro_system.py --provider zhipuai --mode optimization --enable-opro --agents "TAA,TRA"
```

#### 自定义日期范围
```bash
python run_opro_system.py --provider zhipuai --mode integrated --enable-opro \
  --start-date "2025-01-01" --end-date "2025-01-05"
```

#### 强制优化（忽略时间间隔限制）
```bash
python run_opro_system.py --provider zhipuai --mode optimization --enable-opro --force-optimization
```

#### 导出结果和OPRO数据
```bash
python run_opro_system.py --provider zhipuai --mode integrated --enable-opro \
  --output "results/my_results.json" --export-opro-data
```

## 配置文件

### OPRO配置 (`config/opro_config.json`)

主要配置项说明：

```json
{
  "optimization": {
    "candidates_per_generation": 8,        // 每轮生成的候选数量
    "historical_weeks_to_consider": 10,    // 考虑的历史周数
    "temperature": 1.0,                    // LLM生成温度
    "prompt_length_limit": 500,            // 提示词长度限制
    "min_improvement_threshold": 0.01      // 最小改进阈值
  },
  "storage": {
    "opro_db_path": "results/opro_optimization.db",  // 数据库路径
    "data_retention_days": 90                        // 数据保留天数
  }
}
```

## 智能体集成

### 使用OPROBaseAgent

```python
from agents.opro_base_agent import OPROBaseAgent

# 创建支持OPRO的智能体
agent = OPROBaseAgent(
    agent_id="TAA",
    llm_interface=llm_interface,
    opro_enabled=True
)

# 更新提示词
agent.update_prompt(
    new_prompt="优化后的提示词内容",
    source="opro",
    metadata={"improvement": 0.15}
)

# 运行OPRO优化
optimization_result = agent.optimize_with_opro()
```

### A/B测试功能

```python
# 启动A/B测试
candidates = ["候选提示词1", "候选提示词2", "候选提示词3"]
agent.start_ab_test(candidates, "test_v1")

# 记录测试结果
agent.record_ab_test_result("variant_0", 0.85, {"context": "测试环境"})

# 分析结果并选择最佳变体
analysis = agent.analyze_ab_test_results()
agent.finish_ab_test(adopt_best=True)
```

## API集成

### 直接使用ContributionAssessor

```python
from contribution_assessment.assessor import ContributionAssessor

# 创建启用OPRO的评估器
assessor = ContributionAssessor(
    config=your_config,
    llm_provider="zhipuai",
    enable_opro=True,
    opro_config=opro_config
)

# 运行OPRO优化循环
result = assessor.run_opro_optimization_cycle(
    target_agents=["TAA", "TRA"],
    force_optimization=True
)

# 运行完整集成流程
result = assessor.run_with_opro_integration(
    target_agents=["NAA", "TAA", "FAA", "TRA"],
    run_optimization_after=True
)

# 获取仪表板数据
dashboard = assessor.get_opro_dashboard_data()
```

## 监控和分析

### 系统状态监控

```bash
# 查看OPRO仪表板
python run_opro_system.py --provider zhipuai --mode dashboard --enable-opro
```

### 性能趋势分析

使用历史得分管理器API：

```python
from contribution_assessment.historical_score_manager import HistoricalScoreManager

manager = HistoricalScoreManager()

# 获取智能体得分趋势
trends = manager.get_score_trends("TAA", weeks=20)

# 跨智能体性能比较
comparison = manager.get_cross_agent_comparison()

# 优化效果分析
effectiveness = manager.get_optimization_effectiveness()
```

## 故障排除

### 常见问题

1. **OPRO模块导入失败**
   - 检查所有文件是否正确创建
   - 确认Python路径包含项目目录

2. **LLM连接失败**
   - 验证API密钥设置: `echo $ZHIPUAI_API_KEY`
   - 检查网络连接和API配额

3. **数据库错误**
   - 确保`results/`目录存在
   - 检查文件权限

4. **优化效果不佳**
   - 增加`candidates_per_generation`
   - 调整`temperature`参数
   - 检查历史数据质量

### 调试模式

启用详细日志：

```bash
python run_opro_system.py --provider zhipuai --mode integrated --enable-opro --verbose
```

## 最佳实践

### 1. 渐进式部署
- 从1-2个智能体开始试点
- 验证优化效果后逐步扩展
- 建立性能基线对比

### 2. 定期优化
- 设置周期性优化任务
- 监控性能趋势变化
- 及时调整优化策略

### 3. 数据管理
- 定期备份优化数据
- 清理过期历史记录
- 监控数据库大小

### 4. 安全考虑
- 启用提示词验证
- 设置内容过滤规则
- 记录审计日志

## 扩展功能

### 未来发展方向

1. **多目标优化**: 同时优化Shapley值和风险指标
2. **强化学习**: 集成RL方法进行策略优化
3. **跨智能体协作**: 智能体间的协同优化
4. **动态联盟**: 基于性能的联盟自动调整

### 自定义扩展

参考现有代码结构，可以扩展：
- 新的评估指标
- 自定义优化策略
- 额外的智能体类型
- 高级分析功能

## 支持

如有问题或建议，请：
1. 查看日志文件获取详细错误信息
2. 运行测试脚本进行诊断
3. 检查配置文件设置
4. 参考现有代码示例