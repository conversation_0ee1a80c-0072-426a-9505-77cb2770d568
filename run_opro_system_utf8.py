#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OPRO系统UTF-8兼容启动脚本

这个脚本解决了Windows系统上Unicode字符显示的问题。
它会设置正确的环境变量，然后调用主程序。

使用方法:
    python run_opro_system_utf8.py --analysis-report comprehensive --export-format json
"""

import os
import sys
import subprocess

def setup_utf8_environment():
    """设置UTF-8环境"""
    # 设置Python IO编码
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    
    # 在Windows上设置控制台代码页
    if sys.platform.startswith('win'):
        try:
            # 设置控制台代码页为UTF-8
            subprocess.run(['chcp', '65001'], shell=True, capture_output=True, check=False)
        except Exception:
            pass
        
        # 设置环境变量
        os.environ['PYTHONLEGACYWINDOWSSTDIO'] = '0'

def main():
    """主函数"""
    # 设置UTF-8环境
    setup_utf8_environment()
    
    # 获取命令行参数
    args = sys.argv[1:]  # 排除脚本名称
    
    # 构建命令
    cmd = [sys.executable, 'run_opro_system.py'] + args
    
    try:
        # 运行主程序
        result = subprocess.run(cmd, check=False)
        sys.exit(result.returncode)
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
