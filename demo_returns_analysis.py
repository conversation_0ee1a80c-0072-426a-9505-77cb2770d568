#!/usr/bin/env python3
"""
收益率分析演示脚本 (Returns Analysis Demo)

本脚本演示如何在完成贡献度评估后生成详细的收益率曲线分析。
功能包括：
1. 运行多智能体贡献度评估
2. 自动生成收益率曲线分析
3. 创建可视化图表
4. 生成详细性能报告

使用方法:
    python demo_returns_analysis.py --provider zhipuai --enable-charts
"""

import argparse
import logging
import os
import sys
from datetime import datetime
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from contribution_assessment.assessor import ContributionAssessor
from contribution_assessment.llm_interface import LLMInterface


def setup_logging(verbose: bool = False) -> logging.Logger:
    """设置日志记录"""
    log_level = logging.DEBUG if verbose else logging.INFO
    
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(f'demo_returns_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
        ]
    )
    
    return logging.getLogger(__name__)


def load_demo_config() -> dict:
    """加载演示配置"""
    return {
        "start_date": "2023-01-01",
        "end_date": "2023-01-10",
        "stocks": ["AAPL"],
        "starting_cash": 1000000,
        "simulation_days": 5,
        "verbose": True,
        "enable_concurrent_execution": True
    }


def run_contribution_assessment_with_returns_analysis(args, logger):
    """运行带收益率分析的贡献度评估"""
    
    logger.info("🚀" * 30)
    logger.info("多智能体贡献度评估 + 收益率分析演示")
    logger.info("🚀" * 30)
    
    # 1. 初始化系统
    logger.info("📋 步骤1: 初始化系统")
    config = load_demo_config()
    
    # 创建LLM接口
    llm_interface = LLMInterface(provider=args.provider, logger=logger)
    
    # 创建评估器
    assessor = ContributionAssessor(
        config=config,
        logger=logger,
        llm_provider=args.provider,
        enable_opro=False,  # 专注于收益率分析
        opro_config={}
    )
    
    logger.info("✅ 系统初始化完成")
    
    # 2. 运行贡献度评估
    logger.info("📊 步骤2: 运行贡献度评估")
    
    target_agents = ["NAA", "TAA", "FAA", "TRA"]  # 简化演示
    
    evaluation_result = assessor.run(
        target_agents=target_agents,
        max_coalitions=10  # 限制联盟数量以加快演示
    )
    
    if not evaluation_result.get("success", False):
        logger.error(f"❌ 贡献度评估失败: {evaluation_result.get('error', '未知错误')}")
        return False
    
    logger.info("✅ 贡献度评估完成")
    
    # 3. 展示收益率分析结果
    logger.info("📈 步骤3: 收益率分析结果")
    
    returns_analysis = evaluation_result.get("returns_analysis", {})
    
    if not returns_analysis.get("analysis_completed", False):
        logger.warning(f"⚠️ 收益率分析未完成: {returns_analysis.get('error', '未知原因')}")
        return False
    
    # 显示分析摘要
    logger.info("=" * 60)
    logger.info("收益率分析摘要")
    logger.info("=" * 60)
    
    total_coalitions = returns_analysis.get("total_coalitions_analyzed", 0)
    logger.info(f"📊 分析的联盟数量: {total_coalitions}")
    
    # 最佳表现联盟
    best_coalition = returns_analysis.get("best_coalition", {})
    if best_coalition:
        logger.info(f"🏆 最佳表现联盟: {best_coalition.get('coalition_id', 'N/A')}")
        logger.info(f"   📈 夏普比率: {best_coalition.get('sharpe_ratio', 0):.4f}")
        logger.info(f"   💰 年化收益率: {best_coalition.get('annualized_return', 0):.2%}")
        logger.info(f"   📉 最大回撤: {best_coalition.get('max_drawdown', 0):.2%}")
        logger.info(f"   🎯 胜率: {best_coalition.get('win_rate', 0):.2%}")
        
        members = best_coalition.get('members', [])
        if members:
            logger.info(f"   👥 成员: {', '.join(members)}")
    
    # 整体统计
    summary_stats = returns_analysis.get("summary_statistics", {})
    logger.info(f"")
    logger.info(f"📊 整体统计:")
    logger.info(f"   平均夏普比率: {summary_stats.get('avg_sharpe_ratio', 0):.4f}")
    logger.info(f"   最佳夏普比率: {summary_stats.get('best_sharpe_ratio', 0):.4f}")
    logger.info(f"   策略多样化: {summary_stats.get('strategy_diversification', 0):.4f}")
    
    # 图表和报告路径
    plot_paths = returns_analysis.get("plot_paths", {})
    if plot_paths:
        logger.info(f"")
        logger.info(f"📊 生成的图表:")
        for plot_type, path in plot_paths.items():
            if path:
                logger.info(f"   📈 {plot_type}: {path}")
    
    report_path = returns_analysis.get("performance_report_path", "")
    if report_path:
        logger.info(f"")
        logger.info(f"📄 性能报告: {report_path}")
    
    # 4. Shapley值与收益率的关系分析
    logger.info("🔍 步骤4: Shapley值与收益率关系分析")
    
    shapley_values = evaluation_result.get("shapley_values", {})
    logger.info(f"")
    logger.info(f"📊 各智能体贡献度 (Shapley值):")
    
    for agent_id, shapley_value in shapley_values.items():
        logger.info(f"   🤖 {agent_id}: {shapley_value:.6f}")
    
    # 分析贡献度与表现的关系
    if best_coalition and shapley_values:
        best_members = best_coalition.get('members', [])
        total_contribution = sum(shapley_values.get(member, 0) for member in best_members if member in shapley_values)
        logger.info(f"")
        logger.info(f"🎯 最佳联盟分析:")
        logger.info(f"   联盟总贡献度: {total_contribution:.6f}")
        logger.info(f"   平均每成员贡献: {total_contribution / max(len(best_members), 1):.6f}")
    
    logger.info("=" * 60)
    
    return True


def display_file_recommendations(logger):
    """显示文件查看建议"""
    logger.info("💡 建议查看以下文件:")
    
    # 检查结果目录
    results_dir = Path("results/returns_analysis")
    if results_dir.exists():
        logger.info(f"📁 收益率分析结果目录: {results_dir}")
        
        # 列出图表文件
        for plot_file in results_dir.glob("*.png"):
            logger.info(f"   📊 {plot_file.name}")
        
        # 列出报告文件
        for report_file in results_dir.glob("*.json"):
            logger.info(f"   📄 {report_file.name}")
    
    # 日志文件
    log_files = list(Path(".").glob("demo_returns_*.log"))
    if log_files:
        latest_log = max(log_files, key=lambda x: x.stat().st_mtime)
        logger.info(f"📋 详细日志文件: {latest_log}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="收益率分析演示脚本")
    
    parser.add_argument(
        "--provider",
        type=str,
        default="zhipuai",
        choices=["zhipuai", "openai"],
        help="LLM提供商"
    )
    
    parser.add_argument(
        "--enable-charts",
        action="store_true",
        default=True,
        help="启用图表生成"
    )
    
    parser.add_argument(
        "--verbose",
        action="store_true",
        help="详细日志输出"
    )
    
    args = parser.parse_args()
    
    # 设置日志
    logger = setup_logging(args.verbose)
    
    logger.info(f"参数配置:")
    logger.info(f"  LLM提供商: {args.provider}")
    logger.info(f"  启用图表: {args.enable_charts}")
    logger.info(f"  详细日志: {args.verbose}")
    logger.info("")
    
    try:
        # 运行演示
        success = run_contribution_assessment_with_returns_analysis(args, logger)
        
        if success:
            logger.info("🎉" * 30)
            logger.info("收益率分析演示成功完成！")
            logger.info("🎉" * 30)
            
            # 显示文件建议
            display_file_recommendations(logger)
            
            return 0
        else:
            logger.error("❌ 收益率分析演示失败")
            return 1
            
    except KeyboardInterrupt:
        logger.warning("⚠️ 用户中断演示")
        return 1
        
    except Exception as e:
        logger.error(f"💥 演示过程中发生异常: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())