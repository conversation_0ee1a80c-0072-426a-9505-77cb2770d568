#!/usr/bin/env python3
"""
数据库时间格式迁移脚本

批量处理现有新闻数据，将所有时间格式标准化为ISO 8601格式
包含数据备份、迁移、验证和回滚功能
"""

import os
import sys
import sqlite3
import shutil
from datetime import datetime
from typing import Dict, List, Tuple, Optional

# Add project root to sys.path
current_script_path = os.path.abspath(__file__)
project_root = os.path.dirname(os.path.dirname(current_script_path))
sys.path.append(project_root)

from config import DATA_DIR
from data.time_format_normalizer import TimeFormatNormalizer


class TimeMigrator:
    """时间格式迁移器"""
    
    def __init__(self, ticker: str, verbose: bool = True):
        self.ticker = ticker.upper()
        self.verbose = verbose
        self.database_path = self._get_database_path()
        self.backup_path = None
        self.normalizer = TimeFormatNormalizer()
        
    def _get_database_path(self) -> str:
        """获取数据库路径"""
        ticker_dir = os.path.join(DATA_DIR, "tickers", self.ticker)
        return os.path.join(ticker_dir, f"{self.ticker}_data.db")
    
    def _log(self, message: str) -> None:
        """日志输出"""
        if self.verbose:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            print(f"[{timestamp}] {message}")
    
    def backup_database(self) -> bool:
        """备份数据库"""
        try:
            if not os.path.exists(self.database_path):
                self._log(f"错误: 数据库文件不存在 {self.database_path}")
                return False
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"{self.ticker}_data_backup_{timestamp}.db"
            backup_dir = os.path.join(DATA_DIR, "backups")
            os.makedirs(backup_dir, exist_ok=True)
            
            self.backup_path = os.path.join(backup_dir, backup_filename)
            shutil.copy2(self.database_path, self.backup_path)
            
            self._log(f"数据库备份完成: {self.backup_path}")
            return True
            
        except Exception as e:
            self._log(f"数据库备份失败: {str(e)}")
            return False
    
    def analyze_time_formats(self) -> Dict:
        """分析当前数据库中的时间格式分布"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            # 获取所有时间数据
            cursor.execute("""
                SELECT time_published, COUNT(*) as count
                FROM news 
                WHERE ticker = ? AND time_published IS NOT NULL
                GROUP BY time_published
                ORDER BY count DESC
                LIMIT 1000
            """, (self.ticker,))
            
            time_samples = cursor.fetchall()
            
            # 分析格式分布
            format_stats = {}
            total_records = 0
            
            cursor.execute("SELECT COUNT(*) FROM news WHERE ticker = ?", (self.ticker,))
            total_records = cursor.fetchone()[0]
            
            # 按长度分组统计
            cursor.execute("""
                SELECT LENGTH(time_published) as length, COUNT(*) as count
                FROM news 
                WHERE ticker = ? AND time_published IS NOT NULL
                GROUP BY LENGTH(time_published)
                ORDER BY length
            """, (self.ticker,))
            
            length_stats = cursor.fetchall()
            
            # 检测具体格式
            sample_formats = {}
            for time_str, count in time_samples[:50]:  # 检查前50个样本
                format_type = self.normalizer.detect_format(time_str)
                if format_type not in sample_formats:
                    sample_formats[format_type] = []
                sample_formats[format_type].append((time_str, count))
            
            conn.close()
            
            analysis_result = {
                'total_records': total_records,
                'length_distribution': dict(length_stats),
                'format_samples': sample_formats,
                'needs_migration': any(length != 19 for length, _ in length_stats)
            }
            
            return analysis_result
            
        except Exception as e:
            self._log(f"时间格式分析失败: {str(e)}")
            return {}
    
    def migrate_time_formats(self, batch_size: int = 1000) -> bool:
        """批量迁移时间格式"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            # 添加临时列
            self._log("添加临时时间列...")
            try:
                cursor.execute("ALTER TABLE news ADD COLUMN time_published_normalized TEXT")
                conn.commit()
            except sqlite3.OperationalError as e:
                if "duplicate column name" in str(e).lower():
                    self._log("临时列已存在，继续迁移...")
                else:
                    raise
            
            # 获取需要迁移的记录总数
            cursor.execute("""
                SELECT COUNT(*) FROM news 
                WHERE ticker = ? AND time_published IS NOT NULL 
                AND (time_published_normalized IS NULL OR time_published_normalized = '')
            """, (self.ticker,))
            
            total_to_migrate = cursor.fetchone()[0]
            self._log(f"需要迁移的记录数: {total_to_migrate}")
            
            if total_to_migrate == 0:
                self._log("没有需要迁移的记录")
                conn.close()
                return True
            
            # 分批处理
            processed = 0
            errors = []
            
            while processed < total_to_migrate:
                # 获取一批数据
                cursor.execute("""
                    SELECT article_id, time_published 
                    FROM news 
                    WHERE ticker = ? AND time_published IS NOT NULL 
                    AND (time_published_normalized IS NULL OR time_published_normalized = '')
                    LIMIT ?
                """, (self.ticker, batch_size))
                
                batch_data = cursor.fetchall()
                if not batch_data:
                    break
                
                # 批量转换时间格式
                updates = []
                for article_id, time_published in batch_data:
                    try:
                        normalized_time = self.normalizer.normalize_time_format(time_published)
                        updates.append((normalized_time, article_id))
                    except Exception as e:
                        errors.append((article_id, time_published, str(e)))
                
                # 批量更新
                if updates:
                    cursor.executemany("""
                        UPDATE news 
                        SET time_published_normalized = ? 
                        WHERE article_id = ?
                    """, updates)
                    conn.commit()
                
                processed += len(batch_data)
                self._log(f"已处理: {processed}/{total_to_migrate} ({processed/total_to_migrate*100:.1f}%)")
            
            # 验证迁移结果
            cursor.execute("""
                SELECT COUNT(*) FROM news 
                WHERE ticker = ? AND time_published_normalized IS NOT NULL
            """, (self.ticker,))
            
            migrated_count = cursor.fetchone()[0]
            
            self._log(f"迁移完成: {migrated_count} 条记录")
            if errors:
                self._log(f"迁移错误: {len(errors)} 条记录")
                for article_id, original, error in errors[:10]:  # 只显示前10个错误
                    self._log(f"  错误: {article_id} - {original} -> {error}")
            
            conn.close()
            return len(errors) == 0
            
        except Exception as e:
            self._log(f"时间格式迁移失败: {str(e)}")
            return False
    
    def validate_migration(self) -> bool:
        """验证迁移结果"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            # 检查迁移完整性
            cursor.execute("""
                SELECT 
                    COUNT(*) as total,
                    COUNT(time_published_normalized) as normalized,
                    COUNT(CASE WHEN time_published_normalized IS NULL THEN 1 END) as missing
                FROM news 
                WHERE ticker = ?
            """, (self.ticker,))
            
            stats = cursor.fetchone()
            total, normalized, missing = stats
            
            self._log(f"验证结果: 总记录={total}, 已标准化={normalized}, 缺失={missing}")
            
            # 验证格式正确性
            cursor.execute("""
                SELECT time_published_normalized 
                FROM news 
                WHERE ticker = ? AND time_published_normalized IS NOT NULL
                LIMIT 100
            """, (self.ticker,))
            
            sample_times = [row[0] for row in cursor.fetchall()]
            
            format_errors = 0
            for time_str in sample_times:
                if not self.normalizer.validate_normalized_format(time_str):
                    format_errors += 1
            
            self._log(f"格式验证: {len(sample_times)} 个样本中有 {format_errors} 个格式错误")
            
            conn.close()
            
            # 验证通过条件：没有缺失记录且格式错误少于5%
            validation_passed = (missing == 0 and format_errors / len(sample_times) < 0.05)
            
            if validation_passed:
                self._log("✅ 迁移验证通过")
            else:
                self._log("❌ 迁移验证失败")
            
            return validation_passed
            
        except Exception as e:
            self._log(f"迁移验证失败: {str(e)}")
            return False
    
    def finalize_migration(self) -> bool:
        """完成迁移：替换原始列"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            self._log("开始最终迁移步骤...")
            
            # 备份原始列到临时列
            try:
                cursor.execute("ALTER TABLE news ADD COLUMN time_published_original TEXT")
                cursor.execute("""
                    UPDATE news 
                    SET time_published_original = time_published 
                    WHERE ticker = ?
                """, (self.ticker,))
                conn.commit()
                self._log("原始时间数据已备份")
            except sqlite3.OperationalError as e:
                if "duplicate column name" in str(e).lower():
                    self._log("原始数据备份列已存在")
                else:
                    raise
            
            # 用标准化时间替换原始时间
            cursor.execute("""
                UPDATE news 
                SET time_published = time_published_normalized 
                WHERE ticker = ? AND time_published_normalized IS NOT NULL
            """, (self.ticker,))
            
            affected_rows = cursor.rowcount
            conn.commit()
            
            self._log(f"已更新 {affected_rows} 条记录的时间格式")
            
            # 删除临时列
            # SQLite不支持直接删除列，所以保留临时列作为备份
            self._log("保留临时列作为备份 (time_published_normalized, time_published_original)")
            
            conn.close()
            
            self._log("✅ 迁移最终化完成")
            return True
            
        except Exception as e:
            self._log(f"最终迁移失败: {str(e)}")
            return False
    
    def rollback_migration(self) -> bool:
        """回滚迁移"""
        try:
            if not self.backup_path or not os.path.exists(self.backup_path):
                self._log("错误: 备份文件不存在，无法回滚")
                return False
            
            self._log("开始回滚迁移...")
            shutil.copy2(self.backup_path, self.database_path)
            self._log("✅ 迁移回滚完成")
            return True
            
        except Exception as e:
            self._log(f"迁移回滚失败: {str(e)}")
            return False
    
    def cleanup_backup(self) -> bool:
        """清理备份文件"""
        try:
            if self.backup_path and os.path.exists(self.backup_path):
                os.remove(self.backup_path)
                self._log(f"备份文件已清理: {self.backup_path}")
                return True
            return True
        except Exception as e:
            self._log(f"清理备份文件失败: {str(e)}")
            return False


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="数据库时间格式迁移工具")
    parser.add_argument("ticker", help="股票代码")
    parser.add_argument("--analyze-only", action="store_true", help="仅分析时间格式，不执行迁移")
    parser.add_argument("--batch-size", type=int, default=1000, help="批处理大小")
    parser.add_argument("--rollback", action="store_true", help="回滚迁移")
    parser.add_argument("--cleanup", action="store_true", help="清理备份文件")
    parser.add_argument("--quiet", action="store_true", help="静默模式")
    
    args = parser.parse_args()
    
    migrator = TimeMigrator(args.ticker, verbose=not args.quiet)
    
    if args.rollback:
        success = migrator.rollback_migration()
        sys.exit(0 if success else 1)
    
    if args.cleanup:
        success = migrator.cleanup_backup()
        sys.exit(0 if success else 1)
    
    # 分析时间格式
    print(f"分析 {args.ticker} 的时间格式...")
    analysis = migrator.analyze_time_formats()
    
    if not analysis:
        print("时间格式分析失败")
        sys.exit(1)
    
    print(f"总记录数: {analysis['total_records']}")
    print("时间长度分布:")
    for length, count in analysis['length_distribution'].items():
        print(f"  {length} 字符: {count} 条")
    
    print("格式样本:")
    for format_type, samples in analysis['format_samples'].items():
        print(f"  {format_type}: {samples[:3]}")  # 显示前3个样本
    
    if not analysis['needs_migration']:
        print("✅ 所有时间格式已标准化，无需迁移")
        sys.exit(0)
    
    if args.analyze_only:
        print("仅分析模式，退出")
        sys.exit(0)
    
    # 执行迁移
    print("\n开始时间格式迁移...")
    
    # 备份数据库
    if not migrator.backup_database():
        print("数据库备份失败，中止迁移")
        sys.exit(1)
    
    # 执行迁移
    if not migrator.migrate_time_formats(args.batch_size):
        print("时间格式迁移失败")
        print("可以使用 --rollback 参数回滚迁移")
        sys.exit(1)
    
    # 验证迁移
    if not migrator.validate_migration():
        print("迁移验证失败")
        print("可以使用 --rollback 参数回滚迁移")
        sys.exit(1)
    
    # 完成迁移
    if not migrator.finalize_migration():
        print("最终迁移失败")
        print("可以使用 --rollback 参数回滚迁移")
        sys.exit(1)
    
    print("✅ 时间格式迁移完成")
    print("可以使用 --cleanup 参数清理备份文件")


if __name__ == "__main__":
    main() 