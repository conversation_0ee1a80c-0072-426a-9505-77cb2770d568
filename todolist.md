# 多智能体贡献分配算法实施清单

本文档基于已批准的方案设计，将代码实现分解为一系列具体的、原子化的任务。

### 实施清单：

1.  **项目结构搭建 (Project Scaffolding):**
    *   1.1. 在项目根目录下创建新目录 `contribution_assessment`，用于存放贡献度分配算法的所有相关模块。
    *   1.2. 在 `contribution_assessment` 目录中创建 `__init__.py` 文件，使其成为一个 Python 包。

2.  **阶段一：分析缓存模块 (Analysis Caching Module):**
    *   2.1. 在 `contribution_assessment/` 中创建新文件 `analysis_cache.py`。
    *   2.2. 在 `analysis_cache.py` 中定义 `AnalysisCache` 类，用于存储和检索分析层智能体 (NAA, TAA, FAA) 的输出。
    *   2.3. 实现 `AnalysisCache` 类的核心方法：`store(agent_id, data)`、`get(agent_id)` 和 `populate_from_agents(analyst_agents)`，后者用于一次性运行所有分析智能体并缓存其结果。

3.  **阶段二：联盟生成与剪枝模块 (Coalition Management Module):**
    *   3.1. 在 `contribution_assessment/` 中创建新文件 `coalition_manager.py`。
    *   3.2. 在 `coalition_manager.py` 中定义 `CoalitionManager` 类。
    *   3.3. 在 `CoalitionManager` 中实现一个私有方法 `_generate_all_subsets(agents)`，用于通过位运算或 `itertools` 高效生成所有可能的联盟。
    *   3.4. 实现核心公共方法 `generate_pruned_coalitions(all_agents, analyst_agents, trader_agent)`，该方法应：
        *   调用 `_generate_all_subsets` 生成所有联盟。
        *   应用"终点缺失"剪枝规则：剔除所有不包含 `trader_agent` 的联盟。
        *   应用"起点缺失"剪枝规则：剔除所有与 `analyst_agents` 集合交集为空的联盟。
        *   返回一个元组，包含两个集合：需要模拟的有效联盟 `C_sim` 和被剪枝的无效联盟 `C_pruned`。

4.  **阶段三：交易模拟引擎 (Trading Simulation Engine):**
    *   4.1. 在 `contribution_assessment/` 中创建新文件 `trading_simulator.py`。
    *   4.2. 在 `trading_simulator.py` 中定义 `TradingSimulator` 类。
    *   4.3. 实现核心方法 `run_simulation_for_coalition(coalition, analysis_cache)`：
        *   该方法接收一个联盟（智能体对象列表）和已填充的分析缓存作为输入。
        *   方法内部需要初始化一个交易环境实例（依赖于项目已有的 `stock_trading_env.py`）。
        *   根据 `coalition` 中的智能体，动态地、按依赖顺序执行一个完整的交易周期。
        *   确保展望层和决策层智能体从 `analysis_cache` 获取输入数据，而不是重新运行分析智能体。
        *   模拟结束时，计算并返回该联盟的夏普比率，作为其特征函数值 `v(S)`。

5.  **阶段四：Shapley 值计算模块 (Shapley Value Calculator):**
    *   5.1. 在 `contribution_assessment/` 中创建新文件 `shapley_calculator.py`。
    *   5.2. 在 `shapley_calculator.py` 中定义 `ShapleyCalculator` 类。
    *   5.3. 实现核心方法 `calculate(agents, coalition_values)`：
        *   该方法接收所有智能体的列表和一个字典 `coalition_values`，其中包含了所有模拟过的联盟及其价值 `v(S)`。
        *   该方法内部需要为所有被剪枝的联盟赋予 `v(S) = 0`。
        *   根据标准的 Shapley 值数学公式（包含阶乘和组合），为每个智能体 `i` 计算其最终贡献值 `μ_i`。
        *   返回一个字典，映射每个智能体 ID 到其对应的 Shapley 值。

6.  **主协调器与执行脚本 (Main Orchestrator and Execution Script):**
    *   6.1. 在 `contribution_assessment/` 中创建新文件 `assessor.py`。
    *   6.2. 在 `assessor.py` 中定义 `ContributionAssessor` 类，作为整个流程的主协调器。
    *   6.3. `ContributionAssessor` 的 `__init__` 方法应初始化 `AnalysisCache`, `CoalitionManager`, `TradingSimulator`, 和 `ShapleyCalculator` 的实例。
    *   6.4. 实现 `ContributionAssessor` 的主方法 `run()`，该方法将按顺序执行方案中定义的四个阶段，管理数据流，并最终返回所有智能体的 Shapley 值。
    *   6.5. 在项目根目录下创建 `run_contribution_assessment.py` 脚本，该脚本将作为用户入口，负责实例化并运行 `ContributionAssessor`，并打印最终结果。 