#!/usr/bin/env python3
"""
周级数据协调器

负责协调OHLCV、基本面和新闻数据的获取和组织，
为Shapley评估框架提供按周组织的数据缓存
"""

import os
import sys
import json
import sqlite3
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Tuple, Set
import pandas as pd

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.trading_calendar import TradingCalendar
from utils.comprehensive_logger import SimpleLogger
from data.prepare_data import get_database_path


class WeeklyDataCoordinator:
    """
    周级数据协调器
    
    负责获取和组织多种类型的市场数据，按交易周进行缓存，
    为Shapley评估框架提供结构化的数据输入
    """
    
    def __init__(self, logger: Optional[SimpleLogger] = None):
        """
        初始化数据协调器
        
        Args:
            logger: 日志记录器
        """
        self.logger = logger or SimpleLogger("WeeklyDataCoordinator")
        self.trading_calendar = TradingCalendar(logger=self.logger)
        
        # 数据缓存
        self._data_cache = {}
        
        self.logger.info("周级数据协调器初始化完成")
    
    def generate_weekly_cache_data(self, 
                                 ticker: str,
                                 start_date: str, 
                                 end_date: str,
                                 include_news: bool = True,
                                 include_fundamentals: bool = True) -> Dict[str, List[Dict[str, Any]]]:
        """
        生成周级缓存数据
        
        Args:
            ticker: 股票代码
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            include_news: 是否包含新闻数据
            include_fundamentals: 是否包含基本面数据
            
        Returns:
            周级缓存数据字典 {week_id: [day_data_1, day_data_2, ...]}
        """
        self.logger.info(f"开始生成 {ticker} 的周级缓存数据: {start_date} 到 {end_date}")
        
        # 1. 获取交易日
        trading_days = self.trading_calendar.get_trading_days(
            start_date, end_date, use_database=True, stocks=[ticker]
        )
        
        if not trading_days:
            self.logger.warning(f"在指定日期范围内没有找到交易日: {start_date} 到 {end_date}")
            return {}
        
        self.logger.info(f"找到 {len(trading_days)} 个交易日")
        
        # 2. 按周组织交易日
        trading_weeks = self.trading_calendar.get_trading_weeks(trading_days, strategy="adaptive")
        
        self.logger.info(f"组织为 {len(trading_weeks)} 个交易周")
        
        # 3. 为每个交易周生成数据
        weekly_cache_data = {}
        
        for week_info in trading_weeks:
            week_id = f"{week_info['start_date']}_W{week_info['week_number']}"
            week_trading_days = week_info['trading_days']
            
            self.logger.info(f"处理第 {week_info['week_number']} 周: {len(week_trading_days)} 个交易日")
            
            # 为该周的每个交易日生成数据
            week_daily_data = []
            for day_str in week_trading_days:
                day_data = self._generate_daily_data(
                    ticker, day_str, include_news, include_fundamentals
                )
                if day_data:
                    week_daily_data.append(day_data)
            
            if week_daily_data:
                weekly_cache_data[week_id] = week_daily_data
                self.logger.debug(f"第 {week_info['week_number']} 周数据生成完成: {len(week_daily_data)} 天")
        
        self.logger.info(f"周级缓存数据生成完成: {len(weekly_cache_data)} 周")
        return weekly_cache_data
    
    def _generate_daily_data(self, 
                           ticker: str, 
                           date_str: str,
                           include_news: bool = True,
                           include_fundamentals: bool = True) -> Optional[Dict[str, Any]]:
        """
        生成单日数据
        
        Args:
            ticker: 股票代码
            date_str: 日期字符串 (YYYY-MM-DD)
            include_news: 是否包含新闻数据
            include_fundamentals: 是否包含基本面数据
            
        Returns:
            单日数据字典或None（如果数据不完整）
        """
        try:
            # 基础数据结构
            day_data = {
                "date": date_str,
                "ticker": ticker,
                "ohlcv": None,
                "news": [],
                "fundamentals": {},
                "metadata": {
                    "data_completeness": {},
                    "generated_at": datetime.now().isoformat()
                }
            }
            
            # 1. 获取OHLCV数据
            ohlcv_data = self._get_ohlcv_data(ticker, date_str)
            if ohlcv_data:
                day_data["ohlcv"] = ohlcv_data
                day_data["metadata"]["data_completeness"]["ohlcv"] = True
            else:
                day_data["metadata"]["data_completeness"]["ohlcv"] = False
                self.logger.warning(f"缺少 {ticker} 在 {date_str} 的OHLCV数据")
            
            # 2. 获取新闻数据（如果需要）
            if include_news:
                news_data = self._get_news_data(ticker, date_str)
                day_data["news"] = news_data
                day_data["metadata"]["data_completeness"]["news"] = len(news_data) > 0
            
            # 3. 获取基本面数据（如果需要）
            if include_fundamentals:
                fundamental_data = self._get_fundamental_data(ticker, date_str)
                day_data["fundamentals"] = fundamental_data
                day_data["metadata"]["data_completeness"]["fundamentals"] = bool(fundamental_data)
            
            return day_data
            
        except Exception as e:
            self.logger.error(f"生成 {ticker} 在 {date_str} 的数据时出错: {e}")
            return None

    def _get_ohlcv_data(self, ticker: str, date_str: str) -> Optional[Dict[str, Any]]:
        """
        获取OHLCV数据

        Args:
            ticker: 股票代码
            date_str: 日期字符串

        Returns:
            OHLCV数据字典或None
        """
        try:
            db_path = get_database_path(ticker)
            if not os.path.exists(db_path):
                return None

            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT trade_date, Open, High, Low, Close, Adj_Close, Volume
                FROM ohlcv
                WHERE ticker = ? AND trade_date = ?
            """, (ticker.upper(), date_str))

            row = cursor.fetchone()
            conn.close()

            if row:
                return {
                    "date": row[0],
                    "open": float(row[1]),
                    "high": float(row[2]),
                    "low": float(row[3]),
                    "close": float(row[4]),
                    "adj_close": float(row[5]),
                    "volume": int(row[6]),
                    "daily_return": None  # 将在后续计算中填充
                }

            return None

        except Exception as e:
            self.logger.error(f"获取 {ticker} 在 {date_str} 的OHLCV数据时出错: {e}")
            return None

    def _get_news_data(self, ticker: str, date_str: str) -> List[Dict[str, Any]]:
        """
        获取新闻数据

        Args:
            ticker: 股票代码
            date_str: 日期字符串

        Returns:
            新闻数据列表
        """
        try:
            db_path = get_database_path(ticker)
            if not os.path.exists(db_path):
                return []

            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # 检查新闻表是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='news'")
            if not cursor.fetchone():
                conn.close()
                return []

            # 查询当日新闻
            cursor.execute("""
                SELECT title, summary, overall_sentiment_label, overall_sentiment_score,
                       time_published, url, source
                FROM news
                WHERE ticker = ?
                AND (
                    DATE(time_published) = ?
                    OR SUBSTR(time_published, 1, 4) || '-' || SUBSTR(time_published, 5, 2) || '-' || SUBSTR(time_published, 7, 2) = ?
                )
                ORDER BY time_published ASC
            """, (ticker.upper(), date_str, date_str))

            rows = cursor.fetchall()
            conn.close()

            news_data = []
            for row in rows:
                news_item = {
                    "title": row[0],
                    "summary": row[1],
                    "sentiment_label": row[2],
                    "sentiment_score": float(row[3]) if row[3] is not None else 0.0,
                    "time_published": row[4],
                    "url": row[5],
                    "source": row[6]
                }
                news_data.append(news_item)

            return news_data

        except Exception as e:
            self.logger.error(f"获取 {ticker} 在 {date_str} 的新闻数据时出错: {e}")
            return []

    def _get_fundamental_data(self, ticker: str, date_str: str) -> Dict[str, Any]:
        """
        获取基本面数据

        Args:
            ticker: 股票代码
            date_str: 日期字符串

        Returns:
            基本面数据字典
        """
        try:
            db_path = get_database_path(ticker)
            if not os.path.exists(db_path):
                return {}

            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            fundamental_data = {}

            # 检查年度财务数据表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='annual_financials'")
            if cursor.fetchone():
                # 获取最近的年度财务数据
                cursor.execute("""
                    SELECT report_type, data_json, fiscal_date
                    FROM annual_financials
                    WHERE ticker = ? AND fiscal_date <= ?
                    ORDER BY fiscal_date DESC, report_type
                    LIMIT 10
                """, (ticker.upper(), date_str))

                annual_rows = cursor.fetchall()
                if annual_rows:
                    fundamental_data["annual_reports"] = []
                    for row in annual_rows:
                        try:
                            data_json = json.loads(row[1]) if row[1] else {}
                            fundamental_data["annual_reports"].append({
                                "report_type": row[0],
                                "fiscal_date": row[2],
                                "data": data_json
                            })
                        except json.JSONDecodeError:
                            continue

            # 检查季度财务数据表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='quarterly_financials'")
            if cursor.fetchone():
                # 获取最近的季度财务数据
                cursor.execute("""
                    SELECT report_type, data_json, fiscal_date
                    FROM quarterly_financials
                    WHERE ticker = ? AND fiscal_date <= ?
                    ORDER BY fiscal_date DESC, report_type
                    LIMIT 6
                """, (ticker.upper(), date_str))

                quarterly_rows = cursor.fetchall()
                if quarterly_rows:
                    fundamental_data["quarterly_reports"] = []
                    for row in quarterly_rows:
                        try:
                            data_json = json.loads(row[1]) if row[1] else {}
                            fundamental_data["quarterly_reports"].append({
                                "report_type": row[0],
                                "fiscal_date": row[2],
                                "data": data_json
                            })
                        except json.JSONDecodeError:
                            continue

            conn.close()
            return fundamental_data

        except Exception as e:
            self.logger.error(f"获取 {ticker} 在 {date_str} 的基本面数据时出错: {e}")
            return {}

    def calculate_daily_returns(self, weekly_cache_data: Dict[str, List[Dict[str, Any]]]) -> Dict[str, List[Dict[str, Any]]]:
        """
        计算每日收益率

        Args:
            weekly_cache_data: 周级缓存数据

        Returns:
            包含收益率的更新数据
        """
        self.logger.info("开始计算每日收益率")

        # 收集所有日期的价格数据
        all_daily_data = []
        for week_id, week_data in weekly_cache_data.items():
            all_daily_data.extend(week_data)

        # 按日期排序
        all_daily_data.sort(key=lambda x: x["date"])

        # 计算收益率
        for i, day_data in enumerate(all_daily_data):
            if day_data["ohlcv"] and i > 0:
                prev_day_data = all_daily_data[i-1]
                if prev_day_data["ohlcv"]:
                    current_close = day_data["ohlcv"]["close"]
                    prev_close = prev_day_data["ohlcv"]["close"]
                    daily_return = (current_close - prev_close) / prev_close
                    day_data["ohlcv"]["daily_return"] = daily_return
                else:
                    day_data["ohlcv"]["daily_return"] = 0.0
            elif day_data["ohlcv"]:
                day_data["ohlcv"]["daily_return"] = 0.0  # 第一天收益率为0

        self.logger.info("每日收益率计算完成")
        return weekly_cache_data

    def get_data_summary(self, weekly_cache_data: Dict[str, List[Dict[str, Any]]]) -> Dict[str, Any]:
        """
        获取数据摘要统计

        Args:
            weekly_cache_data: 周级缓存数据

        Returns:
            数据摘要字典
        """
        total_weeks = len(weekly_cache_data)
        total_days = sum(len(week_data) for week_data in weekly_cache_data.values())

        # 统计数据完整性
        ohlcv_complete = 0
        news_available = 0
        fundamentals_available = 0

        for week_data in weekly_cache_data.values():
            for day_data in week_data:
                if day_data["metadata"]["data_completeness"].get("ohlcv", False):
                    ohlcv_complete += 1
                if day_data["metadata"]["data_completeness"].get("news", False):
                    news_available += 1
                if day_data["metadata"]["data_completeness"].get("fundamentals", False):
                    fundamentals_available += 1

        summary = {
            "total_weeks": total_weeks,
            "total_days": total_days,
            "data_completeness": {
                "ohlcv_complete_days": ohlcv_complete,
                "news_available_days": news_available,
                "fundamentals_available_days": fundamentals_available,
                "ohlcv_completeness_rate": ohlcv_complete / total_days if total_days > 0 else 0,
                "news_availability_rate": news_available / total_days if total_days > 0 else 0,
                "fundamentals_availability_rate": fundamentals_available / total_days if total_days > 0 else 0
            },
            "week_ids": list(weekly_cache_data.keys()),
            "date_range": {
                "start": min(day_data["date"] for week_data in weekly_cache_data.values() for day_data in week_data) if total_days > 0 else None,
                "end": max(day_data["date"] for week_data in weekly_cache_data.values() for day_data in week_data) if total_days > 0 else None
            }
        }

        return summary


def test_weekly_data_coordinator():
    """测试周级数据协调器"""
    print("测试周级数据协调器...")

    # 创建协调器
    coordinator = WeeklyDataCoordinator()

    # 测试AAPL数据生成
    ticker = "AAPL"
    start_date = "2025-01-01"
    end_date = "2025-01-31"

    print(f"生成 {ticker} 从 {start_date} 到 {end_date} 的周级数据...")

    weekly_data = coordinator.generate_weekly_cache_data(
        ticker=ticker,
        start_date=start_date,
        end_date=end_date,
        include_news=True,
        include_fundamentals=True
    )

    if weekly_data:
        # 计算收益率
        weekly_data = coordinator.calculate_daily_returns(weekly_data)

        # 获取摘要
        summary = coordinator.get_data_summary(weekly_data)

        print(f"数据生成成功!")
        print(f"总周数: {summary['total_weeks']}")
        print(f"总天数: {summary['total_days']}")
        print(f"OHLCV完整性: {summary['data_completeness']['ohlcv_completeness_rate']:.2%}")
        print(f"新闻可用性: {summary['data_completeness']['news_availability_rate']:.2%}")
        print(f"基本面可用性: {summary['data_completeness']['fundamentals_availability_rate']:.2%}")

        # 显示第一周的数据样例
        if weekly_data:
            first_week_id = list(weekly_data.keys())[0]
            first_week_data = weekly_data[first_week_id]
            print(f"\n第一周 ({first_week_id}) 数据样例:")
            for day_data in first_week_data[:2]:  # 只显示前两天
                print(f"  日期: {day_data['date']}")
                if day_data['ohlcv']:
                    print(f"    收盘价: {day_data['ohlcv']['close']:.2f}")
                    print(f"    日收益率: {day_data['ohlcv']['daily_return']:.4f}" if day_data['ohlcv']['daily_return'] is not None else "    日收益率: N/A")
                print(f"    新闻数量: {len(day_data['news'])}")
                print(f"    基本面数据: {'有' if day_data['fundamentals'] else '无'}")
    else:
        print("数据生成失败!")


if __name__ == "__main__":
    test_weekly_data_coordinator()
