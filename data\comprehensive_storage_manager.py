#!/usr/bin/env python3
"""
综合数据存储管理器 (Comprehensive Storage Manager)

为OPRO系统提供全面的数据存储功能，包括：
1. 每日交易数据存储
2. 提示词优化跟踪
3. 股价可视化数据收集
4. A/B测试实验数据
5. 数据备份和恢复

作者: AI Assistant
创建时间: 2025-07-04
"""

import os
import json
import sqlite3
import logging
import shutil
import gzip
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union, Tuple
from pathlib import Path
from dataclasses import dataclass, asdict
from contextlib import contextmanager
import threading
import time

@dataclass
class StorageConfig:
    """存储配置数据类"""
    enabled: bool = True
    base_path: str = "data"
    trading_data_path: str = "data/trading"
    prompts_data_path: str = "data/prompts"
    visualizations_path: str = "data/visualizations"
    exports_path: str = "data/exports"
    backups_path: str = "data/backups"
    database_path: str = "data/comprehensive_storage.db"
    auto_backup_interval_hours: int = 24
    data_validation_enabled: bool = True
    compression_enabled: bool = True
    max_file_size_mb: int = 100

@dataclass
class TradingSessionData:
    """交易会话数据结构"""
    session_id: str
    timestamp: str
    agent_inputs: Dict[str, Any]
    agent_outputs: Dict[str, Any]
    trading_decisions: Dict[str, Any]
    market_conditions: Dict[str, Any]
    profit_loss: Dict[str, float]
    metadata: Dict[str, Any]

@dataclass
class PromptOptimizationRecord:
    """提示词优化记录数据结构"""
    optimization_id: str
    agent_id: str
    timestamp: str
    original_prompt: str
    optimized_prompt: str
    optimization_reason: str
    performance_metrics: Dict[str, float]
    version_info: Dict[str, Any]
    metadata: Dict[str, Any]

class ComprehensiveStorageManager:
    """
    综合数据存储管理器
    
    提供统一的数据存储接口，管理所有类型的实验数据
    """
    
    def __init__(self, 
                 config: Optional[Dict[str, Any]] = None,
                 logger: Optional[logging.Logger] = None):
        """
        初始化存储管理器
        
        参数:
            config: 存储配置字典
            logger: 日志记录器
        """
        self.logger = logger or self._create_default_logger()
        
        # 解析配置
        storage_config = config.get("comprehensive_storage", {}) if config else {}
        self.config = StorageConfig(**storage_config)
        
        # 线程锁，确保数据写入安全
        self._lock = threading.RLock()
        
        # 初始化存储结构
        self._initialize_storage_structure()
        
        # 初始化数据库
        self._initialize_database()
        
        # 启动自动备份（如果启用）
        self._last_backup_time = None
        if self.config.enabled:
            self._start_auto_backup()
        
        self.logger.info("综合数据存储管理器初始化完成")
    
    def _create_default_logger(self) -> logging.Logger:
        """创建默认日志记录器"""
        logger = logging.getLogger(f"{__name__}.ComprehensiveStorageManager")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def _initialize_storage_structure(self):
        """初始化存储目录结构"""
        if not self.config.enabled:
            return
        
        directories = [
            self.config.base_path,
            self.config.trading_data_path,
            self.config.prompts_data_path,
            self.config.visualizations_path,
            self.config.exports_path,
            self.config.backups_path
        ]
        
        for directory in directories:
            try:
                os.makedirs(directory, exist_ok=True)
                self.logger.debug(f"确保目录存在: {directory}")
            except Exception as e:
                self.logger.error(f"创建目录失败 {directory}: {e}")
                raise
    
    def _initialize_database(self):
        """初始化SQLite数据库"""
        if not self.config.enabled:
            return
        
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                # 创建交易数据表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS trading_sessions (
                        session_id TEXT PRIMARY KEY,
                        timestamp TEXT NOT NULL,
                        agent_inputs TEXT,
                        agent_outputs TEXT,
                        trading_decisions TEXT,
                        market_conditions TEXT,
                        profit_loss TEXT,
                        metadata TEXT,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # 创建提示词优化表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS prompt_optimizations (
                        optimization_id TEXT PRIMARY KEY,
                        agent_id TEXT NOT NULL,
                        timestamp TEXT NOT NULL,
                        original_prompt TEXT,
                        optimized_prompt TEXT,
                        optimization_reason TEXT,
                        performance_metrics TEXT,
                        version_info TEXT,
                        metadata TEXT,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # 创建A/B测试表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS ab_tests (
                        test_id TEXT PRIMARY KEY,
                        agent_id TEXT NOT NULL,
                        test_name TEXT NOT NULL,
                        variants TEXT,
                        start_time TEXT,
                        end_time TEXT,
                        results TEXT,
                        statistical_analysis TEXT,
                        status TEXT DEFAULT 'active',
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # 创建可视化数据表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS visualization_data (
                        data_id TEXT PRIMARY KEY,
                        data_type TEXT NOT NULL,
                        timestamp TEXT NOT NULL,
                        stock_symbol TEXT,
                        price_data TEXT,
                        indicators TEXT,
                        chart_config TEXT,
                        file_path TEXT,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # 创建系统状态表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS system_status (
                        status_id TEXT PRIMARY KEY,
                        timestamp TEXT NOT NULL,
                        component TEXT NOT NULL,
                        status TEXT NOT NULL,
                        details TEXT,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                conn.commit()
                self.logger.info("数据库初始化完成")
                
        except Exception as e:
            self.logger.error(f"数据库初始化失败: {e}")
            raise
    
    @contextmanager
    def _get_db_connection(self):
        """获取数据库连接的上下文管理器"""
        conn = None
        try:
            conn = sqlite3.connect(self.config.database_path, timeout=30.0)
            conn.execute("PRAGMA journal_mode=WAL")  # 启用WAL模式提高并发性能
            yield conn
        except Exception as e:
            if conn:
                conn.rollback()
            self.logger.error(f"数据库操作失败: {e}")
            raise
        finally:
            if conn:
                conn.close()
    
    def _start_auto_backup(self):
        """启动自动备份线程"""
        def backup_worker():
            while True:
                try:
                    current_time = datetime.now()
                    if (self._last_backup_time is None or 
                        (current_time - self._last_backup_time).total_seconds() >= 
                        self.config.auto_backup_interval_hours * 3600):
                        
                        self.create_backup()
                        self._last_backup_time = current_time
                    
                    # 每小时检查一次
                    time.sleep(3600)
                    
                except Exception as e:
                    self.logger.error(f"自动备份线程错误: {e}")
                    time.sleep(3600)  # 出错后等待1小时再试
        
        backup_thread = threading.Thread(target=backup_worker, daemon=True)
        backup_thread.start()
        self.logger.info("自动备份线程已启动")

    def store_trading_session(self, session_data: TradingSessionData) -> bool:
        """
        存储交易会话数据

        参数:
            session_data: 交易会话数据

        返回:
            是否存储成功
        """
        if not self.config.enabled:
            return False

        try:
            with self._lock:
                # 数据验证
                if self.config.data_validation_enabled:
                    if not self._validate_trading_session_data(session_data):
                        self.logger.error("交易会话数据验证失败")
                        return False

                # 存储到数据库
                with self._get_db_connection() as conn:
                    cursor = conn.cursor()
                    cursor.execute("""
                        INSERT OR REPLACE INTO trading_sessions
                        (session_id, timestamp, agent_inputs, agent_outputs,
                         trading_decisions, market_conditions, profit_loss, metadata)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        session_data.session_id,
                        session_data.timestamp,
                        json.dumps(session_data.agent_inputs, ensure_ascii=False),
                        json.dumps(session_data.agent_outputs, ensure_ascii=False),
                        json.dumps(session_data.trading_decisions, ensure_ascii=False),
                        json.dumps(session_data.market_conditions, ensure_ascii=False),
                        json.dumps(session_data.profit_loss, ensure_ascii=False),
                        json.dumps(session_data.metadata, ensure_ascii=False)
                    ))
                    conn.commit()

                # 同时保存为JSON文件（便于直接查看）
                date_str = session_data.timestamp[:10]  # YYYY-MM-DD
                daily_dir = os.path.join(self.config.trading_data_path, date_str)
                os.makedirs(daily_dir, exist_ok=True)

                file_path = os.path.join(daily_dir, f"{session_data.session_id}.json")
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(asdict(session_data), f, indent=2, ensure_ascii=False)

                self.logger.info(f"交易会话数据已存储: {session_data.session_id}")
                return True

        except Exception as e:
            self.logger.error(f"存储交易会话数据失败: {e}")
            return False

    def store_prompt_optimization(self, optimization_data: PromptOptimizationRecord) -> bool:
        """
        存储提示词优化记录

        参数:
            optimization_data: 提示词优化记录

        返回:
            是否存储成功
        """
        if not self.config.enabled:
            return False

        try:
            with self._lock:
                # 数据验证
                if self.config.data_validation_enabled:
                    if not self._validate_prompt_optimization_data(optimization_data):
                        self.logger.error("提示词优化数据验证失败")
                        return False

                # 存储到数据库
                with self._get_db_connection() as conn:
                    cursor = conn.cursor()
                    cursor.execute("""
                        INSERT OR REPLACE INTO prompt_optimizations
                        (optimization_id, agent_id, timestamp, original_prompt,
                         optimized_prompt, optimization_reason, performance_metrics,
                         version_info, metadata)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        optimization_data.optimization_id,
                        optimization_data.agent_id,
                        optimization_data.timestamp,
                        optimization_data.original_prompt,
                        optimization_data.optimized_prompt,
                        optimization_data.optimization_reason,
                        json.dumps(optimization_data.performance_metrics, ensure_ascii=False),
                        json.dumps(optimization_data.version_info, ensure_ascii=False),
                        json.dumps(optimization_data.metadata, ensure_ascii=False)
                    ))
                    conn.commit()

                # 保存为文件
                agent_dir = os.path.join(self.config.prompts_data_path, optimization_data.agent_id)
                os.makedirs(agent_dir, exist_ok=True)

                file_path = os.path.join(agent_dir, f"{optimization_data.optimization_id}.json")
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(asdict(optimization_data), f, indent=2, ensure_ascii=False)

                self.logger.info(f"提示词优化记录已存储: {optimization_data.optimization_id}")
                return True

        except Exception as e:
            self.logger.error(f"存储提示词优化记录失败: {e}")
            return False

    def _validate_trading_session_data(self, data: TradingSessionData) -> bool:
        """验证交易会话数据"""
        try:
            # 检查必需字段
            if not data.session_id or not data.timestamp:
                return False

            # 检查数据类型
            if not isinstance(data.agent_inputs, dict):
                return False
            if not isinstance(data.agent_outputs, dict):
                return False
            if not isinstance(data.trading_decisions, dict):
                return False
            if not isinstance(data.profit_loss, dict):
                return False

            # 检查时间戳格式
            datetime.fromisoformat(data.timestamp.replace('Z', '+00:00'))

            return True

        except Exception as e:
            self.logger.error(f"交易会话数据验证错误: {e}")
            return False

    def _validate_prompt_optimization_data(self, data: PromptOptimizationRecord) -> bool:
        """验证提示词优化数据"""
        try:
            # 检查必需字段
            if not data.optimization_id or not data.agent_id or not data.timestamp:
                return False

            # 检查提示词内容
            if not data.original_prompt or not data.optimized_prompt:
                return False

            # 检查数据类型
            if not isinstance(data.performance_metrics, dict):
                return False

            # 检查时间戳格式
            datetime.fromisoformat(data.timestamp.replace('Z', '+00:00'))

            return True

        except Exception as e:
            self.logger.error(f"提示词优化数据验证错误: {e}")
            return False

    def get_trading_sessions(self,
                           start_date: Optional[str] = None,
                           end_date: Optional[str] = None,
                           agent_ids: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """
        查询交易会话数据

        参数:
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            agent_ids: 智能体ID列表

        返回:
            交易会话数据列表
        """
        if not self.config.enabled:
            return []

        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()

                query = "SELECT * FROM trading_sessions WHERE 1=1"
                params = []

                if start_date:
                    query += " AND timestamp >= ?"
                    params.append(start_date)

                if end_date:
                    query += " AND timestamp <= ?"
                    params.append(end_date + "T23:59:59")

                query += " ORDER BY timestamp DESC"

                cursor.execute(query, params)
                rows = cursor.fetchall()

                # 转换为字典格式
                columns = [desc[0] for desc in cursor.description]
                results = []

                for row in rows:
                    session_dict = dict(zip(columns, row))

                    # 解析JSON字段
                    for json_field in ['agent_inputs', 'agent_outputs', 'trading_decisions',
                                     'market_conditions', 'profit_loss', 'metadata']:
                        if session_dict[json_field]:
                            try:
                                session_dict[json_field] = json.loads(session_dict[json_field])
                            except:
                                session_dict[json_field] = {}

                    # 过滤智能体
                    if agent_ids:
                        agent_outputs = session_dict.get('agent_outputs', {})
                        if not any(agent_id in agent_outputs for agent_id in agent_ids):
                            continue

                    results.append(session_dict)

                return results

        except Exception as e:
            self.logger.error(f"查询交易会话数据失败: {e}")
            return []

    def get_prompt_optimizations(self,
                               agent_id: Optional[str] = None,
                               start_date: Optional[str] = None,
                               end_date: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        查询提示词优化记录

        参数:
            agent_id: 智能体ID
            start_date: 开始日期
            end_date: 结束日期

        返回:
            提示词优化记录列表
        """
        if not self.config.enabled:
            return []

        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()

                query = "SELECT * FROM prompt_optimizations WHERE 1=1"
                params = []

                if agent_id:
                    query += " AND agent_id = ?"
                    params.append(agent_id)

                if start_date:
                    query += " AND timestamp >= ?"
                    params.append(start_date)

                if end_date:
                    query += " AND timestamp <= ?"
                    params.append(end_date + "T23:59:59")

                query += " ORDER BY timestamp DESC"

                cursor.execute(query, params)
                rows = cursor.fetchall()

                # 转换为字典格式
                columns = [desc[0] for desc in cursor.description]
                results = []

                for row in rows:
                    opt_dict = dict(zip(columns, row))

                    # 解析JSON字段
                    for json_field in ['performance_metrics', 'version_info', 'metadata']:
                        if opt_dict[json_field]:
                            try:
                                opt_dict[json_field] = json.loads(opt_dict[json_field])
                            except:
                                opt_dict[json_field] = {}

                    results.append(opt_dict)

                return results

        except Exception as e:
            self.logger.error(f"查询提示词优化记录失败: {e}")
            return []

    def create_backup(self, backup_name: Optional[str] = None) -> Dict[str, Any]:
        """
        创建数据备份

        参数:
            backup_name: 备份名称，如果为None则自动生成

        返回:
            备份结果信息
        """
        if not self.config.enabled:
            return {"success": False, "error": "存储功能未启用"}

        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = backup_name or f"backup_{timestamp}"
            backup_dir = os.path.join(self.config.backups_path, backup_name)

            os.makedirs(backup_dir, exist_ok=True)

            # 备份数据库
            db_backup_path = os.path.join(backup_dir, "comprehensive_storage.db")
            shutil.copy2(self.config.database_path, db_backup_path)

            # 备份文件数据
            for source_dir, backup_subdir in [
                (self.config.trading_data_path, "trading"),
                (self.config.prompts_data_path, "prompts"),
                (self.config.visualizations_path, "visualizations")
            ]:
                if os.path.exists(source_dir):
                    dest_dir = os.path.join(backup_dir, backup_subdir)
                    shutil.copytree(source_dir, dest_dir, dirs_exist_ok=True)

            # 压缩备份（如果启用）
            if self.config.compression_enabled:
                archive_path = f"{backup_dir}.tar.gz"
                shutil.make_archive(backup_dir, 'gztar', backup_dir)
                shutil.rmtree(backup_dir)  # 删除未压缩版本
                backup_path = archive_path
            else:
                backup_path = backup_dir

            # 计算备份大小
            if os.path.isfile(backup_path):
                backup_size = os.path.getsize(backup_path)
            else:
                backup_size = sum(
                    os.path.getsize(os.path.join(dirpath, filename))
                    for dirpath, _, filenames in os.walk(backup_path)
                    for filename in filenames
                )

            backup_info = {
                "success": True,
                "backup_name": backup_name,
                "backup_path": backup_path,
                "backup_size_mb": round(backup_size / (1024 * 1024), 2),
                "timestamp": timestamp,
                "compressed": self.config.compression_enabled
            }

            self.logger.info(f"数据备份完成: {backup_name} ({backup_info['backup_size_mb']} MB)")
            return backup_info

        except Exception as e:
            self.logger.error(f"创建数据备份失败: {e}")
            return {"success": False, "error": str(e)}

    def export_data(self,
                   export_format: str = "json",
                   start_date: Optional[str] = None,
                   end_date: Optional[str] = None,
                   data_types: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        导出数据到指定格式

        参数:
            export_format: 导出格式 ("json", "csv", "excel")
            start_date: 开始日期
            end_date: 结束日期
            data_types: 数据类型列表 ("trading", "prompts", "visualizations")

        返回:
            导出结果信息
        """
        if not self.config.enabled:
            return {"success": False, "error": "存储功能未启用"}

        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            export_dir = os.path.join(self.config.exports_path, f"export_{timestamp}")
            os.makedirs(export_dir, exist_ok=True)

            exported_files = []
            data_types = data_types or ["trading", "prompts"]

            # 导出交易数据
            if "trading" in data_types:
                trading_data = self.get_trading_sessions(start_date, end_date)
                if trading_data:
                    file_path = self._export_trading_data(trading_data, export_dir, export_format)
                    if file_path:
                        exported_files.append(file_path)

            # 导出提示词数据
            if "prompts" in data_types:
                prompt_data = self.get_prompt_optimizations(None, start_date, end_date)
                if prompt_data:
                    file_path = self._export_prompt_data(prompt_data, export_dir, export_format)
                    if file_path:
                        exported_files.append(file_path)

            return {
                "success": True,
                "export_directory": export_dir,
                "exported_files": exported_files,
                "export_format": export_format,
                "timestamp": timestamp
            }

        except Exception as e:
            self.logger.error(f"导出数据失败: {e}")
            return {"success": False, "error": str(e)}

    def _export_trading_data(self, data: List[Dict], export_dir: str, format: str) -> Optional[str]:
        """导出交易数据"""
        try:
            if format == "json":
                file_path = os.path.join(export_dir, "trading_sessions.json")
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=2, ensure_ascii=False)
                return file_path

            elif format == "csv":
                import pandas as pd

                # 展平嵌套数据
                flattened_data = []
                for session in data:
                    flat_session = {
                        "session_id": session.get("session_id"),
                        "timestamp": session.get("timestamp"),
                        "created_at": session.get("created_at")
                    }

                    # 添加盈亏数据
                    profit_loss = session.get("profit_loss", {})
                    for agent_id, pnl in profit_loss.items():
                        flat_session[f"pnl_{agent_id}"] = pnl

                    # 添加交易决策摘要
                    decisions = session.get("trading_decisions", {})
                    flat_session["total_decisions"] = len(decisions)

                    flattened_data.append(flat_session)

                df = pd.DataFrame(flattened_data)
                file_path = os.path.join(export_dir, "trading_sessions.csv")
                df.to_csv(file_path, index=False, encoding='utf-8-sig')
                return file_path

            elif format == "excel":
                import pandas as pd

                file_path = os.path.join(export_dir, "trading_sessions.xlsx")
                with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                    # 主要会话数据
                    main_data = []
                    for session in data:
                        main_data.append({
                            "会话ID": session.get("session_id"),
                            "时间戳": session.get("timestamp"),
                            "创建时间": session.get("created_at"),
                            "智能体数量": len(session.get("agent_outputs", {})),
                            "交易决策数量": len(session.get("trading_decisions", {}))
                        })

                    pd.DataFrame(main_data).to_excel(writer, sheet_name="交易会话", index=False)

                    # 盈亏汇总
                    pnl_data = []
                    for session in data:
                        profit_loss = session.get("profit_loss", {})
                        for agent_id, pnl in profit_loss.items():
                            pnl_data.append({
                                "会话ID": session.get("session_id"),
                                "智能体ID": agent_id,
                                "盈亏": pnl,
                                "时间戳": session.get("timestamp")
                            })

                    if pnl_data:
                        pd.DataFrame(pnl_data).to_excel(writer, sheet_name="盈亏明细", index=False)

                return file_path

        except Exception as e:
            self.logger.error(f"导出交易数据失败: {e}")
            return None

    def _export_prompt_data(self, data: List[Dict], export_dir: str, format: str) -> Optional[str]:
        """导出提示词数据"""
        try:
            if format == "json":
                file_path = os.path.join(export_dir, "prompt_optimizations.json")
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=2, ensure_ascii=False)
                return file_path

            elif format == "csv":
                import pandas as pd

                # 展平数据
                flattened_data = []
                for opt in data:
                    flat_opt = {
                        "优化ID": opt.get("optimization_id"),
                        "智能体ID": opt.get("agent_id"),
                        "时间戳": opt.get("timestamp"),
                        "原始提示词长度": len(opt.get("original_prompt", "")),
                        "优化提示词长度": len(opt.get("optimized_prompt", "")),
                        "优化原因": opt.get("optimization_reason"),
                        "创建时间": opt.get("created_at")
                    }

                    # 添加性能指标
                    metrics = opt.get("performance_metrics", {})
                    for metric_name, value in metrics.items():
                        flat_opt[f"指标_{metric_name}"] = value

                    flattened_data.append(flat_opt)

                df = pd.DataFrame(flattened_data)
                file_path = os.path.join(export_dir, "prompt_optimizations.csv")
                df.to_csv(file_path, index=False, encoding='utf-8-sig')
                return file_path

        except Exception as e:
            self.logger.error(f"导出提示词数据失败: {e}")
            return None

    def get_storage_statistics(self) -> Dict[str, Any]:
        """获取存储统计信息"""
        if not self.config.enabled:
            return {"enabled": False}

        try:
            stats = {
                "enabled": True,
                "database_size_mb": 0,
                "total_trading_sessions": 0,
                "total_prompt_optimizations": 0,
                "storage_paths": {
                    "base_path": self.config.base_path,
                    "database_path": self.config.database_path
                },
                "last_backup": self._last_backup_time.isoformat() if self._last_backup_time else None
            }

            # 数据库大小
            if os.path.exists(self.config.database_path):
                db_size = os.path.getsize(self.config.database_path)
                stats["database_size_mb"] = round(db_size / (1024 * 1024), 2)

            # 记录统计
            with self._get_db_connection() as conn:
                cursor = conn.cursor()

                cursor.execute("SELECT COUNT(*) FROM trading_sessions")
                stats["total_trading_sessions"] = cursor.fetchone()[0]

                cursor.execute("SELECT COUNT(*) FROM prompt_optimizations")
                stats["total_prompt_optimizations"] = cursor.fetchone()[0]

                # 最近活动
                cursor.execute("SELECT MAX(timestamp) FROM trading_sessions")
                last_trading = cursor.fetchone()[0]
                stats["last_trading_session"] = last_trading

                cursor.execute("SELECT MAX(timestamp) FROM prompt_optimizations")
                last_optimization = cursor.fetchone()[0]
                stats["last_prompt_optimization"] = last_optimization

            return stats

        except Exception as e:
            self.logger.error(f"获取存储统计失败: {e}")
            return {"enabled": True, "error": str(e)}
