# 每周 Shapley 值计算计划

## 问题原因

当前交易系统存在一个不合理现象：仅对首日数据进行缓存或初始化，导致后续交易日快速执行，缺乏必要的周期性评估与调整。用户要求系统在完成一周的交易周期后，立即执行一次全面的 Shapley 值计算，以深入分析并诊断当前交易策略或模型中存在的根本性问题。

## 结论

为了满足用户需求，我们需要在交易模拟过程中，在所有联盟模拟完成后，按周（5个交易日）对已模拟的数据进行 Shapley 值计算，并将结果打印并保存。

## 计划

本计划旨在实现交易系统在每周交易周期结束后，进行周期性 Shapley 值计算的功能。

### 计划概述

1.  **修改 `TradingSimulator`：** 使其在模拟过程中能够返回每个联盟的每日收益数据。
2.  **修改 `ContributionAssessor`：**
    *   在交易模拟阶段结束后，引入一个新的方法来处理周期性 Shapley 值计算。
    *   该方法将接收所有联盟的每日收益数据，并按 5 个交易日为一周的周期进行 Shapley 值计算。
    *   计算结果将打印出来，并保存到文件中。

### 详细计划步骤

#### 步骤 1: 修改 `contribution_assessment/trading_simulator.py`

*   **目标：** 让 `run_simulation_for_coalition` 方法返回每个联盟在模拟期间的每日收益列表，而不仅仅是最终的夏普比率。
*   **修改点：** `run_simulation_for_coalition` 方法的返回值将从 `float` 变为包含 `sharpe_ratio` 和 `daily_returns` 的字典。

#### 步骤 2: 修改 `contribution_assessment/assessor.py`

*   **目标：** 在交易模拟阶段之后，引入周期性 Shapley 值计算。
*   **修改点 1: 修改 `_run_trading_simulation_phase` 方法：**
    *   修改其返回值，使其包含每个联盟的每日收益数据。
*   **修改点 2: 新增方法 `_run_periodic_shapley_calculation_phase`：**
    *   该方法将接收目标智能体列表、所有联盟的每日收益数据和总模拟天数。
    *   它将按 5 个交易日为一周的周期，对每个周期的收益数据进行 Shapley 值计算。
    *   计算结果将打印到日志中，并保存到独立的 JSON 文件中。
*   **修改点 3: 调整 `run` 方法：**
    *   在调用 `_run_trading_simulation_phase` 之后，调用新的 `_run_periodic_shapley_calculation_phase` 方法。
*   **修改点 4: 调整 `_compile_final_result` 方法：**
    *   使其能够包含周期性 Shapley 值计算的结果，作为最终结果的一部分。

### Mermaid 图示

```mermaid
graph TD
    A[ContributionAssessor.run] --> B[_run_llm_analysis_phase]
    B --> C[_run_coalition_generation_phase]
    C --> D[_run_trading_simulation_phase]
    D -- 返回所有联盟的每日收益 --> E[新增: _run_periodic_shapley_calculation_phase]
    E --> F[按5个交易日周期迭代]
    F --> G[为每个周期计算Shapley值]
    G --> H[打印并保存结果]
    E --> I[_run_shapley_calculation_phase (最终)]
    I --> J[汇总最终结果]
    J --> K[输出最终结果]