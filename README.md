# 多智能体交易系统 - 贡献度评估系统

## 🎯 项目概述

本项目实现了一个完整的多智能体贡献度评估系统，使用Shapley值计算方法来评估每个智能体在交易决策中的贡献度。系统支持真实的LLM智能体分析，提供准确的智能体协作评估。

## 🏗️ 系统架构

### 智能体层次结构
```
分析层 (Analysis Layer)
├── NAA (News Analyst Agent) - 新闻分析智能体
├── TAA (Technical Analyst Agent) - 技术分析智能体
└── FAA (Fundamental Analyst Agent) - 基本面分析智能体

展望层 (Outlook Layer)
├── BOA (Bullish Outlook Agent) - 看涨展望智能体
├── BeOA (Bearish Outlook Agent) - 看跌展望智能体
└── NOA (Neutral Observer Agent) - 中性观察智能体

决策层 (Decision Layer)
└── TRA (Trader Agent) - 交易决策智能体
```

### 核心模块
- **贡献度评估器** (`contribution_assessment/`) - 主协调器和核心算法
- **智能体系统** (`agents/`) - 各类智能体实现
- **数据基础设施** (`data/`) - 市场数据和数据处理

## 🚀 快速开始

### 1. 环境配置

#### 安装依赖
```bash
# 安装LLM SDK (选择其一)
pip install zhipuai  # 智谱AI
pip install openai   # OpenAI
```

#### 设置环境变量
```bash
# 智谱AI (推荐)
export ZHIPUAI_API_KEY=your_zhipuai_api_key

# 或者 OpenAI
export OPENAI_API_KEY=your_openai_api_key
```

#### 创建 .env 文件
```bash
# 在项目根目录创建 .env 文件
echo "ZHIPUAI_API_KEY=your_api_key" > .env
```

### 2. 环境检查

运行环境检查工具确保配置正确：
```bash
# 检查智谱AI配置
python check_llm_setup.py --provider zhipuai

# 检查OpenAI配置
python check_llm_setup.py --provider openai
```

### 3. 运行系统

#### 快速测试
```bash
# 运行快速测试（推荐首次使用）
python run_contribution_assessment.py --llm-provider zhipuai --quick-test --verbose

# 使用OpenAI
python run_contribution_assessment.py --llm-provider openai --quick-test --verbose
```

#### 完整评估
```bash
# 运行完整贡献度评估
python run_contribution_assessment.py \
  --llm-provider zhipuai \
  --start-date 2025-01-01 \
  --end-date 2025-01-03 \
  --verbose

# 指定特定智能体
python run_contribution_assessment.py \
  --llm-provider zhipuai \
  --agents NAA TAA FAA TRA \
  --max-coalitions 5 \
  --verbose
```

#### 使用专用脚本（推荐）
```bash
# 使用专门为真实智能体设计的脚本
python run_with_real_agents.py --llm-provider zhipuai --verbose

# 快速测试
python test_real_agents.py
```

## 📋 命令行参数

### 基本配置
- `--llm-provider` - LLM提供商 (zhipuai/openai) **[必需]**
- `--start-date` - 开始日期 (YYYY-MM-DD，默认: 2023-01-01)
- `--end-date` - 结束日期 (YYYY-MM-DD，默认: 2023-01-31)
- `--stocks` - 股票代码列表 (默认: AAPL)
- `--starting-cash` - 初始资金 (默认: 1,000,000)

### 智能体配置
- `--agents` - 目标智能体列表 (默认: 所有智能体)
- `--max-coalitions` - 最大模拟联盟数量
- `--simulation-days` - 模拟天数

### 运行模式
- `--quick-test` - 运行快速测试（使用简化配置）
- `--weekly-evaluation` - 启用周期性Shapley值计算
- `--daily-llm` - 启用每日LLM交易模式

### OPRO优化参数 (run_opro_system.py)
- `--mode` - 运行模式 (evaluation/optimization/integrated/dashboard)
- `--enable-opro` - 启用OPRO功能 **[提示词优化必需]**
- `--force-optimization` - 强制优化
- `--optimize-before` - 评估前优化
- `--optimize-after` - 评估后优化（默认启用）

### 输出控制
- `--verbose` - 显示详细输出
- `--quiet` - 静默模式
- `--output-file` - 输出结果到JSON文件

## 📊 数据资源

### 可用数据
- **AAPL股票数据**：9,605条新闻 + 6,451条价格记录
- **时间范围**：1999年至2025年
- **数据格式**：SQLite数据库 + JSON文件
- **数据完整性**：已验证可用

### 数据结构
```
data/
├── tickers/AAPL/
│   ├── AAPL_data.db          # SQLite数据库
│   ├── AAPL_news.json        # 新闻数据
│   └── AAPL_fundamentals.json # 基本面数据
└── scripts/                  # 数据处理脚本
```

## 🔧 工具链

### 环境检查工具
```bash
python check_llm_setup.py --provider zhipuai
```
检查项目：
- ✅ 环境变量设置
- ✅ SDK安装状态
- ✅ LLM接口连接
- ✅ 智能体创建
- ✅ 评估器配置

### 专用运行脚本
```bash
python run_with_real_agents.py --llm-provider zhipuai
```
特点：
- 🤖 专门为真实智能体设计
- 🔍 自动环境检查
- 📊 结果验证
- 🚨 错误诊断

### 快速测试脚本
```bash
python test_real_agents.py
```
功能：
- 🧪 快速验证配置
- ⚡ 轻量级测试
- 🎯 针对性检查

## 📈 输出结果

### 成功运行标志
当系统正确使用真实LLM智能体时，您会看到：

```
🤖 正在创建LLM智能体实例 (提供商: zhipuai)...
✅ 成功创建智能体: NAA
✅ 成功创建智能体: TAA
...
✅ 成功创建 7 个智能体实例

使用智能体实例进行分析...
🤖 NAA LLM输入: [详细的提示内容]
🤖 NAA LLM输出: [LLM的分析结果]
...

🏆 智能体贡献度排名 (Shapley值):
  1. NAA: 0.123456
  2. TAA: 0.098765
  3. FAA: 0.087654
  4. TRA: 0.076543
```

### 警告标志
如果系统仍在使用模拟数据，会显示：
```
⚠️  未提供分析智能体实例，将使用模拟数据
⚠️  警告: 系统仍在使用模拟数据!
```

## 🛠️ 故障排除

### 常见问题

#### 1. 环境变量未设置
```
❌ 未找到 ZHIPUAI_API_KEY 环境变量
```
**解决方法**：
```bash
export ZHIPUAI_API_KEY=your_api_key
# 或创建 .env 文件
echo "ZHIPUAI_API_KEY=your_api_key" > .env
```

#### 2. SDK未安装
```
❌ zhipuai SDK 未安装
```
**解决方法**：
```bash
pip install zhipuai
```

#### 3. LLM接口连接失败
```
❌ LLM接口初始化失败
```
**解决方法**：
- 检查API密钥是否正确
- 确认网络连接正常
- 验证API配额是否充足

#### 4. 仍在使用模拟数据
```
⚠️  系统仍在使用模拟数据
```
**解决方法**：
1. 确保传入了 `--llm-provider` 参数
2. 运行环境检查：`python check_llm_setup.py --provider zhipuai`
3. 使用专用脚本：`python run_with_real_agents.py --llm-provider zhipuai`

### 调试步骤

1. **环境检查**：
   ```bash
   python check_llm_setup.py --provider zhipuai
   ```

2. **快速测试**：
   ```bash
   python test_real_agents.py
   ```

3. **详细日志**：
   ```bash
   python run_contribution_assessment.py --llm-provider zhipuai --verbose
   ```

## 📚 详细文档

- **[使用真实LLM智能体指南.md](使用真实LLM智能体指南.md)** - 详细的配置和使用指南
- **[修复总结.md](修复总结.md)** - 技术修复详情和原理说明
- **[CORE_SYSTEM_FRAMEWORK.md](CORE_SYSTEM_FRAMEWORK.md)** - 系统架构框架文档

## 🎯 使用示例

### 示例1：快速测试
```bash
# 1. 检查环境
python check_llm_setup.py --provider zhipuai

# 2. 运行快速测试
python run_contribution_assessment.py --llm-provider zhipuai --quick-test --verbose
```

### 示例2：完整评估
```bash
# 运行3天的完整评估
python run_contribution_assessment.py \
  --llm-provider zhipuai \
  --start-date 2025-01-01 \
  --end-date 2025-01-03 \
  --verbose \
  --output-file results.json
```

### 示例3：指定智能体
```bash
# 只评估分析层智能体
python run_contribution_assessment.py \
  --llm-provider zhipuai \
  --agents NAA TAA FAA \
  --max-coalitions 3 \
  --quick-test
```

### 示例4：周期性评估
```bash
# 启用周期性Shapley值计算
python run_contribution_assessment.py \
  --llm-provider zhipuai \
  --weekly-evaluation \
  --start-date 2025-01-01 \
  --end-date 2025-01-15 \
  --verbose
```

### 示例5：OPRO提示词优化
```bash
# 🔍 重要：OPRO提示词优化使用说明

# ❌ 直接运行 - 不会进行提示词优化
python run_opro_system.py

# ❌ 标准评估模式 - 不会进行提示词优化
python run_opro_system.py --provider zhipuai --mode evaluation

# ✅ OPRO优化模式 - 会进行提示词优化
python run_opro_system.py --provider zhipuai --mode optimization --enable-opro --verbose

# ✅ 集成模式 - 会进行评估+提示词优化
python run_opro_system.py --provider zhipuai --mode integrated --enable-opro --verbose

# 📊 仪表板模式 - 查看OPRO优化数据
python run_opro_system.py --provider zhipuai --mode dashboard --enable-opro
```

## ⚡ 性能优化

### 建议配置
- **快速测试**：使用 `--quick-test` 和 `--max-coalitions 3`
- **完整评估**：限制日期范围到1-2周
- **大规模评估**：考虑使用 `--max-coalitions` 限制计算量

### API使用优化
- 监控API调用次数和成本
- 使用缓存机制避免重复调用
- 考虑批量处理减少API请求

## 🔄 系统更新

### 最新改进
- ✅ 修复了"系统仍然使用模拟智能体"的问题
- ✅ 优化了智能体实例传递逻辑
- ✅ 增强了环境配置检查
- ✅ 提供了完整的工具链支持
- ✅ 改进了错误诊断和故障排除

### 核心特性
- 🤖 支持真实LLM智能体分析
- 📊 准确的Shapley值计算
- 🔄 周期性贡献度评估
- 🛠️ 完整的工具链支持
- 📋 详细的日志和诊断

---

## 📞 支持

如果遇到问题，请：
1. 首先运行 `python check_llm_setup.py --provider your_provider`
2. 查看 `使用真实LLM智能体指南.md` 获取详细帮助
3. 检查 `修复总结.md` 了解技术细节

**注意**：确保在运行任何评估之前，系统显示"✅ 成功使用真实LLM智能体!"而不是"⚠️ 警告: 系统仍在使用模拟数据!"
