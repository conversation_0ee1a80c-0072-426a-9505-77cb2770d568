# 每日LLM交易系统使用指南

## 🎉 新功能特性

现在您可以体验真正的**每日LLM交易**：
- ✅ **每个交易日都调用LLM**进行实时分析和决策
- ✅ **显示完整的LLM输入输出**，让您看到AI的思考过程
- ✅ **真实的智能体协作**，不再是模拟数据
- ✅ **层级化决策流程**：分析→展望→交易决策

## 🚀 快速开始

### 方法一：使用新的每日LLM脚本（推荐）

```bash
# 基础使用
python daily_llm_trading.py --llm-provider zhipuai

# 完整配置
python daily_llm_trading.py \
    --llm-provider zhipuai \
    --start-date 2025-01-01 \
    --end-date 2025-01-03 \
    --agents NAA TAA FAA BOA TRA \
    --weekly-evaluation \
    --verbose
```

### 方法二：使用原脚本的新参数

```bash
python run_contribution_assessment.py \
    --start-date 2025-01-01 \
    --end-date 2025-01-03 \
    --llm-provider zhipuai \
    --daily-llm \
    --verbose
```

## 📊 您将看到的输出

### 1. LLM输入输出详情
```
================================================================================
🤖 NAA LLM输入:
----------------------------------------
你是一个专业的新闻分析师，专门分析市场新闻对股票价格的影响。

📅 当前日期: 2025-01-01
📈 股票价格信息:
  • AAPL: $228.87
💰 投资组合: {'AAPL': 0}
💵 可用现金: $1,000,000.00

请基于以上信息进行分析，并以JSON格式返回结果。
----------------------------------------
🤖 NAA LLM输出:
----------------------------------------
{
  "sentiment": 0.1,
  "summary": "苹果公司目前处于相对稳定状态...",
  "key_events": ["新产品发布预期", "市场整体走势"],
  "impact_assessment": "短期中性，长期看好",
  "confidence": 0.75
}
================================================================================
```

### 2. 每日交易决策
```
📊 交易决策: buy (信心度: 0.65)
📝 决策理由: 基于技术分析显示突破信号，新闻面相对积极，建议小仓位买入测试
```

### 3. 智能体协作流程
```
🤖 执行智能体 NAA (使用LLM)
🤖 执行智能体 TAA (使用LLM)  
🤖 执行智能体 FAA (使用LLM)
🤖 执行智能体 BOA (使用LLM)
🤖 执行智能体 TRA (使用LLM)
```

## 🧠 智能体架构

### 分析层（Analyst Layer）
- **NAA (新闻分析师)**: 分析市场新闻和舆论情绪
- **TAA (技术分析师)**: 分析价格趋势和技术指标
- **FAA (基本面分析师)**: 评估公司财务和内在价值

### 展望层（Outlook Layer）  
- **BOA (看涨分析师)**: 构建乐观市场叙述
- **BeOA (看跌分析师)**: 识别风险和看跌因素
- **NOA (中性观察员)**: 提供平衡客观分析

### 决策层（Decision Layer）
- **TRA (交易员)**: 综合所有分析做出最终交易决策

## 🔧 配置选项

### LLM相关
- `--llm-provider`: LLM提供商（zhipuai/openai）
- `--daily-llm`: 启用每日LLM模式
- `--verbose`: 显示详细的LLM输入输出

### 交易配置
- `--start-date`: 开始日期
- `--end-date`: 结束日期  
- `--stocks`: 股票代码列表
- `--starting-cash`: 初始资金

### 智能体配置
- `--agents`: 参与的智能体列表
- `--weekly-evaluation`: 启用周期性评估

## 📈 预期效果对比

### 之前（模拟模式）
```
- 所有智能体使用固定的模拟数据
- 看不到AI的实际分析过程
- 缺乏真实的市场洞察
- 结果千篇一律
```

### 现在（每日LLM模式）
```
✅ 每天都有真实的LLM分析
✅ 完整的AI思考过程展示
✅ 智能体间真实协作
✅ 基于实时数据的决策
✅ 更准确的策略评估
```

## 🎯 实际应用场景

### 1. 策略研究
```bash
# 测试不同智能体组合的效果
python daily_llm_trading.py --llm-provider zhipuai --agents NAA TRA
python daily_llm_trading.py --llm-provider zhipuai --agents TAA TRA  
python daily_llm_trading.py --llm-provider zhipuai --agents NAA TAA FAA TRA
```

### 2. 风险分析
```bash
# 包含风险评估智能体
python daily_llm_trading.py --llm-provider zhipuai --agents NAA TAA FAA BeOA TRA
```

### 3. 完整系统测试
```bash
# 运行所有智能体的完整流程
python daily_llm_trading.py \
    --llm-provider zhipuai \
    --agents NAA TAA FAA BOA BeOA NOA TRA \
    --start-date 2025-01-01 \
    --end-date 2025-01-05 \
    --weekly-evaluation \
    --verbose
```

## 🔍 故障排除

### 1. LLM连接问题
```bash
# 检查环境变量
echo $ZHIPUAI_API_KEY
export ZHIPUAI_API_KEY="your_api_key_here"
```

### 2. 智能体创建失败
- 确保所有依赖项已安装
- 检查agents目录是否存在
- 验证LLM接口初始化

### 3. 没有看到LLM输入输出
- 确保使用了 `--verbose` 参数
- 检查日志级别设置
- 确认使用的是daily_llm_trading.py脚本

## 💡 最佳实践

1. **从简单开始**: 先用少量智能体（如NAA+TRA）测试
2. **观察输出**: 仔细查看LLM的分析逻辑是否合理
3. **调整提示词**: 根据结果优化智能体的提示词
4. **比较结果**: 对比不同智能体组合的表现
5. **控制成本**: 较短的时间范围可以减少LLM调用次数

现在您可以体验真正的AI驱动交易决策过程了！🎉