# OPRO系统综合数据存储功能

## 概述

本项目为OPRO（Optimization by PROmpting）系统添加了全面的数据存储和分析功能。该系统能够自动收集、存储、分析和备份所有相关的交易数据、提示词优化记录和系统性能指标。

## 🚀 主要特性

### 📊 数据收集与存储
- **每日交易数据存储**：智能体输入/输出、交易决策、盈亏记录
- **提示词优化跟踪**：完整的优化历史和性能对比
- **市场数据收集**：股价、成交量、技术指标
- **元数据管理**：时间戳、会话ID、数据完整性验证

### 📈 可视化与分析
- **多种图表类型**：线图、蜡烛图、OHLC图
- **技术指标分析**：RSI、MACD、布林带等
- **交易决策对比**：价格走势与智能体决策的关联分析
- **性能趋势分析**：长期性能变化追踪

### 🧪 A/B测试框架
- **对比实验设计**：原始vs优化提示词效果验证
- **统计显著性测试**：t检验、ANOVA等统计方法
- **置信度评估**：结果可靠性量化分析
- **自动化报告生成**：实验结果自动汇总

### 💾 备份与恢复
- **自动化备份**：定时全量和增量备份
- **数据压缩**：节省存储空间
- **完整性验证**：备份数据校验和验证
- **灵活恢复**：支持指定时间点恢复

### 📋 报告与导出
- **多格式导出**：Excel、CSV、JSON格式支持
- **自动化报告**：交易性能、优化效果分析报告
- **可视化仪表板**：综合数据展示
- **自定义分析**：灵活的数据查询和分析

## 🏗️ 系统架构

```
综合数据存储系统
├── 综合存储管理器 (ComprehensiveStorageManager)
│   ├── SQLite数据库管理
│   ├── 文件系统组织
│   └── 数据验证与完整性检查
├── 交易数据收集器 (TradingDataCollector)
│   ├── 智能体数据收集
│   ├── 市场条件记录
│   └── 会话管理
├── 提示词优化跟踪器 (PromptOptimizationTracker)
│   ├── 版本历史管理
│   ├── 性能对比分析
│   └── 优化效果评估
├── 可视化管理器 (VisualizationManager)
│   ├── 图表生成
│   ├── 技术指标计算
│   └── 交互式可视化
├── A/B测试框架 (ABTestingFramework)
│   ├── 实验设计
│   ├── 统计分析
│   └── 结果验证
├── 数据分析工具 (DataAnalysisTools)
│   ├── 报告生成
│   ├── 数据导出
│   └── 趋势分析
└── 备份管理器 (BackupManager)
    ├── 自动备份
    ├── 增量备份
    └── 数据恢复
```

## 📁 目录结构

```
data/
├── comprehensive_storage_manager.py    # 核心存储管理器
├── trading_data_collector.py          # 交易数据收集器
├── trading_data_extractor.py          # 数据提取工具
├── prompt_optimization_tracker.py     # 提示词优化跟踪器
├── visualization_manager.py           # 可视化管理器
├── ab_testing_framework.py           # A/B测试框架
├── data_analysis_tools.py             # 数据分析工具
├── backup_manager.py                  # 备份管理器
└── integrated_data_manager.py         # 集成数据管理器

docs/
├── comprehensive_data_storage_guide.md # 详细使用指南
└── data_storage_quick_reference.md    # 快速参考

examples/
└── data_storage_demo.py               # 演示脚本

config/
└── comprehensive_storage_example.json # 示例配置文件
```

## 🚀 快速开始

### 1. 基本使用

```bash
# 启用数据存储功能运行评估
python run_opro_system.py --mode evaluation --enable-data-storage --stocks AAPL MSFT

# 生成综合分析报告
python run_opro_system.py --analysis-report comprehensive

# 创建数据备份
python run_opro_system.py --create-backup --backup-type full
```

### 2. 运行演示

```bash
# 运行完整功能演示
python examples/data_storage_demo.py
```

### 3. 查看系统状态

```bash
# 查看数据存储状态
python run_opro_system.py --data-report --backup-status
```

## 📊 使用案例

### 案例1：完整交易实验流程

```bash
# 1. 运行初始评估并收集数据
python run_opro_system.py --mode evaluation --stocks AAPL MSFT GOOGL \
  --start-date 2024-01-01 --end-date 2024-01-31

# 2. 进行提示词优化
python run_opro_system.py --mode optimization --enable-opro

# 3. 生成性能分析报告
python run_opro_system.py --analysis-report trading --export-format excel

# 4. 创建完整备份
python run_opro_system.py --create-backup --backup-type full
```

### 案例2：定期数据分析

```bash
# 每周交易性能报告
python run_opro_system.py --analysis-report trading

# 每月优化效果分析
python run_opro_system.py --analysis-report optimization

# 季度综合分析
python run_opro_system.py --analysis-report comprehensive
```

### 案例3：数据管理维护

```bash
# 导出所有数据
python run_opro_system.py --export-all-data --export-format csv

# 查看备份列表
python run_opro_system.py --list-backups

# 创建增量备份
python run_opro_system.py --create-backup --backup-type incremental
```

## ⚙️ 配置说明

### 基本配置

在 `config/opro_config.json` 中添加：

```json
{
  "storage": {
    "comprehensive_storage": {
      "enabled": true,
      "base_path": "data",
      "auto_backup_interval_hours": 24,
      "data_validation_enabled": true,
      "compression_enabled": true
    }
  }
}
```

### 高级配置

参考 `config/comprehensive_storage_example.json` 获取完整配置选项。

## 📈 性能特性

- **高效存储**：SQLite数据库 + 文件系统混合存储
- **数据压缩**：自动压缩备份文件，节省空间
- **增量备份**：只备份变更数据，提高效率
- **并发安全**：线程安全的数据操作
- **内存优化**：大数据集分批处理

## 🔧 依赖要求

### Python包依赖

```
pandas>=1.3.0
numpy>=1.21.0
matplotlib>=3.4.0
seaborn>=0.11.0
plotly>=5.0.0
scipy>=1.7.0
openpyxl>=3.0.0
schedule>=1.1.0
```

### 系统要求

- Python 3.8+
- 至少2GB可用磁盘空间
- 建议4GB以上内存

## 📚 文档

- [详细使用指南](docs/comprehensive_data_storage_guide.md)
- [快速参考](docs/data_storage_quick_reference.md)
- [API文档](docs/api_reference.md)

## 🐛 故障排除

### 常见问题

1. **数据存储功能未启用**
   ```bash
   # 检查配置
   python run_opro_system.py --data-report
   ```

2. **备份创建失败**
   ```bash
   # 检查磁盘空间和权限
   df -h
   ls -la data/backups/
   ```

3. **导出失败**
   ```bash
   # 尝试不同格式
   python run_opro_system.py --export-all-data --export-format json
   ```

### 日志查看

```bash
# 启用详细日志
python run_opro_system.py --log-file debug.log --mode evaluation
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- 感谢OPRO团队提供的基础框架
- 感谢开源社区提供的优秀工具和库
- 感谢所有贡献者的努力

---

**版本**: 1.0.0  
**最后更新**: 2025-07-04  
**维护者**: AI Assistant
