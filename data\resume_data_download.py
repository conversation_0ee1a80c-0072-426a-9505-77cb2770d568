#!/usr/bin/env python3
"""
恢复数据下载脚本

检查各股票的数据获取状态，并继续未完成的下载任务
主要用于恢复因API限制而中断的新闻和财务数据下载

用法: python data/resume_data_download.py [--ticker TICKER] [--verbose]
示例: python data/resume_data_download.py --verbose
"""

import os
import sys
import argparse
import subprocess
import time
import json
from datetime import datetime, date
from typing import List, Dict, Any
import sqlite3

# Add project root to sys.path
current_script_path = os.path.abspath(__file__)
project_root = os.path.dirname(os.path.dirname(current_script_path))
sys.path.append(project_root)

# Import from config
try:
    from config import DATA_DIR, ALPHAVANTAGE_API_KEY
except ImportError:
    print("错误: config.py 未找到或必需变量未定义。", file=sys.stderr)
    sys.exit(1)

# 目标股票列表
TARGET_STOCKS = ["NVDA", "AAPL", "MSFT", "META"]

def get_database_path(ticker):
    """根据ticker获取对应的数据库路径"""
    ticker_dir = os.path.join(DATA_DIR, "tickers", ticker.upper())
    return os.path.join(ticker_dir, f"{ticker.upper()}_data.db")

def check_news_checkpoint(ticker: str) -> Dict[str, Any]:
    """检查新闻下载检查点状态"""
    checkpoint_base_dir = os.path.dirname(DATA_DIR)
    checkpoint_dir = os.path.join(checkpoint_base_dir, "news_checkpoints")
    checkpoint_file = os.path.join(checkpoint_dir, f"{ticker}_news_checkpoint.json")
    
    if os.path.exists(checkpoint_file):
        try:
            with open(checkpoint_file, 'r', encoding='utf-8') as f:
                checkpoint_data = json.load(f)
            return {
                "exists": True,
                "data": checkpoint_data,
                "file_path": checkpoint_file
            }
        except Exception as e:
            return {
                "exists": False,
                "error": f"读取检查点文件失败: {e}"
            }
    else:
        return {"exists": False}

def check_data_completeness(ticker: str) -> Dict[str, Any]:
    """检查股票数据的完整性"""
    db_path = get_database_path(ticker)
    
    if not os.path.exists(db_path):
        return {"exists": False}
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        result = {"exists": True, "ticker": ticker}
        
        # 检查OHLCV数据
        cursor.execute("SELECT COUNT(*), MIN(trade_date), MAX(trade_date) FROM ohlcv WHERE ticker = ?", (ticker,))
        ohlcv_result = cursor.fetchone()
        result["ohlcv"] = {
            "count": ohlcv_result[0] if ohlcv_result[0] else 0,
            "date_range": f"{ohlcv_result[1]} - {ohlcv_result[2]}" if ohlcv_result[1] and ohlcv_result[2] else "无数据"
        }
        
        # 检查新闻数据
        cursor.execute("SELECT COUNT(*), MIN(time_published), MAX(time_published) FROM news")
        news_result = cursor.fetchone()
        result["news"] = {
            "count": news_result[0] if news_result[0] else 0,
            "date_range": f"{news_result[1]} - {news_result[2]}" if news_result[1] and news_result[2] else "无数据"
        }
        
        # 检查财务数据
        cursor.execute("SELECT COUNT(*) FROM annual_financials WHERE ticker = ?", (ticker,))
        annual_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM quarterly_financials WHERE ticker = ?", (ticker,))
        quarterly_count = cursor.fetchone()[0]
        
        result["financials"] = {
            "annual_count": annual_count,
            "quarterly_count": quarterly_count
        }
        
        conn.close()
        return result
        
    except Exception as e:
        return {"exists": True, "error": str(e)}

def run_script_safely(script_name: str, args: List[str], verbose: bool = False) -> bool:
    """安全地运行数据获取脚本"""
    script_path = os.path.join(os.path.dirname(__file__), script_name)
    cmd = [sys.executable, script_path] + args
    
    if verbose:
        print(f"执行命令: {' '.join(cmd)}", file=sys.stderr)
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if verbose:
            if result.stdout.strip():
                print(f"输出: {result.stdout.strip()}", file=sys.stderr)
            if result.stderr.strip():
                print(f"错误: {result.stderr.strip()}", file=sys.stderr)
        
        if result.returncode != 0:
            print(f"{script_name} 执行失败，返回码: {result.returncode}", file=sys.stderr)
            return False
        
        return True
        
    except Exception as e:
        print(f"执行 {script_name} 时发生错误: {e}", file=sys.stderr)
        return False

def resume_ticker_data(ticker: str, verbose: bool = False) -> bool:
    """恢复单个股票的数据下载"""
    print(f"\n{'='*50}")
    print(f"检查股票 {ticker} 的数据状态")
    print(f"{'='*50}")
    
    # 检查数据完整性
    completeness = check_data_completeness(ticker)
    
    if not completeness["exists"]:
        print(f"❌ {ticker} 数据库不存在，需要重新开始下载")
        return False
    
    if "error" in completeness:
        print(f"❌ {ticker} 数据库查询失败: {completeness['error']}")
        return False
    
    # 显示当前数据状态
    print(f"📊 当前数据状态:")
    print(f"  OHLCV: {completeness['ohlcv']['count']} 条记录, {completeness['ohlcv']['date_range']}")
    print(f"  新闻: {completeness['news']['count']} 条记录, {completeness['news']['date_range']}")
    print(f"  财务: 年度{completeness['financials']['annual_count']}份, 季度{completeness['financials']['quarterly_count']}份")
    
    # 检查新闻下载检查点
    news_checkpoint = check_news_checkpoint(ticker)
    
    needs_action = False
    
    # 检查是否需要继续新闻下载
    if news_checkpoint["exists"]:
        checkpoint_data = news_checkpoint["data"]
        next_date = checkpoint_data.get("next_date_to_process")
        end_date = checkpoint_data.get("end_date")
        
        print(f"📰 发现新闻下载检查点:")
        print(f"  下次处理日期: {next_date}")
        print(f"  目标结束日期: {end_date}")
        
        if next_date and end_date:
            # 检查是否还有未完成的日期范围
            next_date_obj = datetime.strptime(next_date, '%Y-%m-%d').date()
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
            
            if next_date_obj <= end_date_obj:
                print(f"🔄 需要继续新闻下载从 {next_date} 到 {end_date}")
                
                if run_script_safely('get_news_data.py', [ticker, next_date, end_date] + (['--verbose'] if verbose else []), verbose):
                    print(f"✅ {ticker} 新闻数据下载继续成功")
                    needs_action = True
                else:
                    print(f"❌ {ticker} 新闻数据下载继续失败")
                    return False
            else:
                print(f"✅ {ticker} 新闻数据已完成下载")
    
    # 检查财务数据是否需要补充
    if completeness['financials']['annual_count'] == 0 or completeness['financials']['quarterly_count'] == 0:
        print(f"🔄 需要下载/补充财务数据")
        
        if run_script_safely('get_fundamental_data.py', [ticker], verbose):
            print(f"✅ {ticker} 财务数据下载成功")
            needs_action = True
        else:
            print(f"❌ {ticker} 财务数据下载失败")
            return False
    else:
        print(f"✅ {ticker} 财务数据已完整")
    
    if not needs_action:
        print(f"✅ {ticker} 所有数据都已完整，无需进一步操作")
    
    return True

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="恢复中断的数据下载")
    parser.add_argument("--ticker", type=str, help="指定要恢复的股票代码")
    parser.add_argument("--verbose", action="store_true", help="启用详细输出")
    args = parser.parse_args()
    
    # 检查API配置
    if not ALPHAVANTAGE_API_KEY or ALPHAVANTAGE_API_KEY == "YOUR_ALPHA_VANTAGE_API_KEY":
        print("错误: 请在 config.py 中配置您的 Alpha Vantage API 密钥。", file=sys.stderr)
        sys.exit(1)
    
    print("=" * 60)
    print("数据下载恢复工具")
    print("=" * 60)
    
    if args.ticker:
        # 恢复单个股票
        ticker = args.ticker.upper()
        if ticker not in TARGET_STOCKS:
            print(f"警告: {ticker} 不在目标股票列表中")
        
        success = resume_ticker_data(ticker, args.verbose)
        if success:
            print(f"\n✅ {ticker} 数据恢复完成")
        else:
            print(f"\n❌ {ticker} 数据恢复失败")
            sys.exit(1)
    else:
        # 恢复所有目标股票
        success_count = 0
        
        for ticker in TARGET_STOCKS:
            if resume_ticker_data(ticker, args.verbose):
                success_count += 1
            
            # 在处理下一个股票前稍作延迟
            if ticker != TARGET_STOCKS[-1]:
                print("等待 5 秒后处理下一个股票...")
                time.sleep(5)
        
        print(f"\n{'='*60}")
        print(f"恢复完成: {success_count}/{len(TARGET_STOCKS)} 只股票成功")
        print(f"{'='*60}")
        
        if success_count == len(TARGET_STOCKS):
            print("🎉 所有股票数据恢复成功！")
        else:
            print("⚠️  部分股票数据恢复失败，请检查上面的错误信息。")

if __name__ == "__main__":
    main() 