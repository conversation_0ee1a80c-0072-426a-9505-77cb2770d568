"""
交易决策智能体 (Trader Agent)

最终的交易决策制定者，综合所有分析做出具体的交易行动
"""

from typing import Dict, Any
from .base_agent import BaseAgent


class TraderAgent(BaseAgent):
    """交易决策智能体 (TRA)"""
    
    def __init__(self, llm_interface=None, logger=None):
        super().__init__("TRA", llm_interface, logger)
    
    def get_prompt_template(self) -> str:
        return """你是一个专业的交易员，负责做出最终的交易决策。

你需要综合考虑：
1. 新闻分析师(NAA)的市场情绪分析
2. 技术分析师(TAA)的技术面分析  
3. 基本面分析师(FAA)的价值评估
4. 看涨分析师(BOA)的乐观展望
5. 看跌分析师(BeOA)的风险警示
6. 中性观察员(NOA)的平衡观点

基于以上所有信息，做出具体的交易决策。

请返回JSON格式的交易决策，包含：
- action: 交易行动（buy/sell/hold）
- position_size: 仓位大小（0到1，1表示全仓）
- reasoning: 决策理由
- risk_assessment: 风险评估
- stop_loss: 止损位（如果适用）
- take_profit: 止盈位（如果适用）
- time_horizon: 持有时间框架
- confidence: 决策信心度（0到1）

注意：
- 如果信号冲突严重，选择持有(hold)
- 考虑风险管理和仓位控制
- 基于多个分析师的共识程度调整信心度"""
    
    def process(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """处理交易决策"""
        prompt = self.get_prompt_template()
        result = self.call_llm(prompt, state)
        
        # 将LLM的决策转换为交易环境可理解的格式
        if isinstance(result, dict) and "action" in result:
            action = result.get("action", "hold").lower()
            position_size = result.get("position_size", 0.0)
            
            # 转换为交易行动字典
            if action == "buy":
                result["trading_actions"] = {"AAPL": position_size}
            elif action == "sell":
                result["trading_actions"] = {"AAPL": -position_size}
            else:  # hold
                result["trading_actions"] = {"__HOLD__": 1.0}
        
        return result