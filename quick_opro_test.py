#!/usr/bin/env python3
"""
OPRO快速测试脚本 (Quick OPRO Test)

用于快速验证OPRO集成是否正常工作。

使用方法:
    python quick_opro_test.py
    python quick_opro_test.py --provider zhipuai
"""

import argparse
import logging
import sys
import os
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def setup_logging():
    """设置简单的日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def test_opro_availability():
    """测试OPRO模块可用性"""
    print("1. 测试OPRO模块可用性...")
    
    try:
        from contribution_assessment.opro_optimizer import OPROOptimizer
        from contribution_assessment.historical_score_manager import HistoricalScoreManager
        from agents.opro_base_agent import OPROBaseAgent
        print("   ✅ OPRO模块导入成功")
        return True
    except ImportError as e:
        print(f"   ❌ OPRO模块导入失败: {e}")
        return False

def test_llm_connection(provider: str):
    """测试LLM连接"""
    print(f"2. 测试LLM连接 ({provider})...")
    
    try:
        from contribution_assessment.llm_interface import LLMInterface
        
        llm = LLMInterface(provider=provider)
        
        if llm.client:
            print("   ✅ LLM客户端初始化成功")
            
            # 测试简单调用
            response = llm.analyze("测试", "glm-4-flash")
            if response:
                print("   ✅ LLM调用测试成功")
                return True
            else:
                print("   ❌ LLM调用测试失败")
                return False
        else:
            print("   ❌ LLM客户端初始化失败")
            return False
            
    except Exception as e:
        print(f"   ❌ LLM连接测试失败: {e}")
        return False

def test_historical_score_manager():
    """测试历史得分管理器"""
    print("3. 测试历史得分管理器...")
    
    try:
        from contribution_assessment.historical_score_manager import HistoricalScoreManager
        
        manager = HistoricalScoreManager(
            results_base_path="results/periodic_shapley",
            db_path="results/test_opro.db"
        )
        
        # 测试基本功能
        stats = manager.get_summary_stats()
        print(f"   ✅ 历史得分管理器工作正常")
        print(f"      数据库统计: {stats.get('database_stats', {})}")
        return True
        
    except Exception as e:
        print(f"   ❌ 历史得分管理器测试失败: {e}")
        return False

def test_opro_optimizer(llm_interface):
    """测试OPRO优化器"""
    print("4. 测试OPRO优化器...")
    
    try:
        from contribution_assessment.opro_optimizer import OPROOptimizer
        from contribution_assessment.historical_score_manager import HistoricalScoreManager
        
        # 创建组件
        score_manager = HistoricalScoreManager(
            results_base_path="results/periodic_shapley",
            db_path="results/test_opro.db"
        )
        
        optimizer = OPROOptimizer(
            llm_interface=llm_interface,
            historical_score_manager=score_manager
        )
        
        # 测试统计功能
        stats = optimizer.get_stats()
        print("   ✅ OPRO优化器初始化成功")
        print(f"      优化统计: 总优化次数 {stats.get('total_optimizations', 0)}")
        return True
        
    except Exception as e:
        print(f"   ❌ OPRO优化器测试失败: {e}")
        return False

def test_assessor_integration(provider: str):
    """测试评估器集成"""
    print("5. 测试评估器OPRO集成...")
    
    try:
        from contribution_assessment.assessor import ContributionAssessor
        from contribution_assessment.llm_interface import LLMInterface
        
        # 创建测试配置
        config = {
            "start_date": "2023-01-01",
            "end_date": "2023-01-02",
            "stocks": ["AAPL"],
            "starting_cash": 100000,
            "simulation_days": 1,
            "verbose": False
        }
        
        # 创建LLM接口
        llm_interface = LLMInterface(provider=provider)
        
        # 创建评估器（启用OPRO）
        assessor = ContributionAssessor(
            config=config,
            logger=setup_logging(),
            llm_provider=provider,
            enable_opro=True
        )
        
        if assessor.enable_opro:
            print("   ✅ OPRO集成成功")
            
            # 测试仪表板功能
            dashboard_data = assessor.get_opro_dashboard_data()
            if dashboard_data.get("opro_enabled", False):
                print("   ✅ OPRO仪表板功能正常")
                return True
            else:
                print("   ❌ OPRO仪表板功能异常")
                return False
        else:
            print("   ❌ OPRO集成失败")
            return False
            
    except Exception as e:
        print(f"   ❌ 评估器集成测试失败: {e}")
        return False

def test_opro_base_agent():
    """测试OPRO基础智能体"""
    print("6. 测试OPRO基础智能体...")
    
    try:
        from agents.opro_base_agent import OPROBaseAgent
        
        # 创建测试智能体
        agent = OPROBaseAgent(
            agent_id="TEST",
            opro_enabled=True
        )
        
        # 测试基本功能
        agent.current_prompt = "测试提示词"
        prompt = agent.get_prompt_template()
        
        if prompt == "测试提示词":
            print("   ✅ OPRO基础智能体功能正常")
            
            # 测试统计功能
            stats = agent.get_opro_stats()
            print(f"      智能体统计: OPRO启用 {stats.get('opro_enabled', False)}")
            return True
        else:
            print("   ❌ OPRO基础智能体功能异常")
            return False
            
    except Exception as e:
        print(f"   ❌ OPRO基础智能体测试失败: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="OPRO快速测试脚本")
    parser.add_argument("--provider", type=str, default="zhipuai", 
                       choices=["zhipuai", "openai"], help="LLM提供商")
    
    args = parser.parse_args()
    
    print("=" * 80)
    print("OPRO快速测试开始")
    print("=" * 80)
    print(f"LLM提供商: {args.provider}")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 运行测试
    test_results = []
    
    # 1. OPRO模块可用性
    result = test_opro_availability()
    test_results.append(("OPRO模块可用性", result))
    
    if not result:
        print("\n❌ OPRO模块不可用，无法继续测试")
        sys.exit(1)
    
    # 2. LLM连接
    result = test_llm_connection(args.provider)
    test_results.append(("LLM连接", result))
    
    if not result:
        print(f"\n❌ LLM连接失败，请检查 {args.provider.upper()}_API_KEY 环境变量")
        sys.exit(1)
    
    # 获取LLM接口用于后续测试
    from contribution_assessment.llm_interface import LLMInterface
    llm_interface = LLMInterface(provider=args.provider)
    
    # 3. 历史得分管理器
    result = test_historical_score_manager()
    test_results.append(("历史得分管理器", result))
    
    # 4. OPRO优化器
    result = test_opro_optimizer(llm_interface)
    test_results.append(("OPRO优化器", result))
    
    # 5. 评估器集成
    result = test_assessor_integration(args.provider)
    test_results.append(("评估器OPRO集成", result))
    
    # 6. OPRO基础智能体
    result = test_opro_base_agent()
    test_results.append(("OPRO基础智能体", result))
    
    # 汇总结果
    print()
    print("=" * 80)
    print("测试结果汇总")
    print("=" * 80)
    
    passed_count = 0
    total_count = len(test_results)
    
    for test_name, passed in test_results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{status} {test_name}")
        if passed:
            passed_count += 1
    
    print("-" * 80)
    print(f"总计: {passed_count}/{total_count} 测试通过")
    
    if passed_count == total_count:
        print("🎉 所有测试通过！OPRO系统已准备就绪。")
        print()
        print("建议的下一步:")
        print("1. 运行完整测试: python test_opro_integration.py --provider zhipuai --test-mode full --enable-opro")
        print("2. 运行OPRO系统: python run_opro_system.py --provider zhipuai --mode integrated --enable-opro")
        sys.exit(0)
    else:
        print("❌ 部分测试失败，请检查相关组件。")
        sys.exit(1)

if __name__ == "__main__":
    main()