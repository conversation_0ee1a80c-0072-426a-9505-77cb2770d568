"""
联盟生成与剪枝模块 (Coalition Management Module)

本模块实现了智能体联盟的生成和剪枝功能，用于Shapley值计算中的联盟枚举。
通过应用"终点缺失"和"起点缺失"剪枝规则，大幅减少需要模拟的联盟数量，
提高计算效率。

主要功能：
1. 生成所有可能的智能体联盟组合
2. 应用剪枝规则过滤无效联盟
3. 返回有效联盟和被剪枝联盟的分类结果
4. 提供联盟统计和分析功能
"""

import itertools
from typing import Dict, Any, List, Set, Tuple, Optional, Union
import logging
import time
from datetime import datetime


class CoalitionManager:
    """
    联盟管理器
    
    负责生成智能体联盟并应用剪枝规则，支持高效的联盟枚举和过滤。
    基于多智能体交易系统的依赖关系图，实现智能的联盟剪枝策略。
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        初始化联盟管理器
        
        参数:
            logger: 日志记录器，如果为None则创建默认记录器
        """
        self.logger = logger or self._create_default_logger()
        
        # 联盟生成统计信息
        self._stats = {
            "total_generated": 0,
            "valid_coalitions": 0,
            "pruned_coalitions": 0,
            "generation_time": 0.0,
            "last_generation": None
        }
        
        # 默认的智能体依赖关系（基于CORE_SYSTEM_FRAMEWORK.md）
        self.default_agent_graph = {
            "NAA": [],  # 新闻分析智能体，无依赖
            "TAA": [],  # 技术分析智能体，无依赖
            "FAA": [],  # 基本面分析智能体，无依赖
            "BOA": ["NAA", "TAA", "FAA"],  # 看多展望智能体，依赖分析层
            "BeOA": ["NAA", "TAA", "FAA"], # 看空展望智能体，依赖分析层
            "NOA": ["NAA", "TAA", "FAA"],  # 中立观察智能体，依赖分析层
            "TRA": ["BOA", "BeOA", "NOA"]  # 交易智能体，依赖展望层
        }
        
        self.logger.info("联盟管理器初始化完成")
    
    def _create_default_logger(self) -> logging.Logger:
        """创建默认日志记录器"""
        logger = logging.getLogger(f"{__name__}.CoalitionManager")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def _generate_all_subsets(self, agents: Union[List[str], Set[str]]) -> List[Set[str]]:
        """
        生成所有可能的智能体子集（联盟）
        
        使用itertools高效生成所有可能的智能体组合，包括空集和全集。
        
        参数:
            agents: 智能体列表或集合
            
        返回:
            所有可能子集的列表，每个子集为智能体ID的集合
        """
        agent_list = list(agents) if isinstance(agents, set) else agents
        all_subsets = []
        
        # 生成所有长度的组合（从0到n）
        for r in range(len(agent_list) + 1):
            for subset in itertools.combinations(agent_list, r):
                all_subsets.append(set(subset))
        
        self.logger.debug(f"生成了 {len(all_subsets)} 个子集，智能体数量: {len(agent_list)}")
        return all_subsets
    
    def generate_pruned_coalitions(self, 
                                 all_agents: Union[List[str], Set[str]], 
                                 analyst_agents: Union[List[str], Set[str]], 
                                 trader_agent: str) -> Tuple[Set[frozenset], Set[frozenset]]:
        """
        生成剪枝后的联盟集合
        
        这是核心方法，应用两个关键剪枝规则：
        1. "终点缺失"剪枝：剔除所有不包含trader_agent的联盟
        2. "起点缺失"剪枝：剔除所有与analyst_agents集合交集为空的联盟
        
        参数:
            all_agents: 所有智能体的列表或集合
            analyst_agents: 分析层智能体的列表或集合（NAA, TAA, FAA）
            trader_agent: 交易智能体ID（通常为"TRA"）
            
        返回:
            元组 (C_sim, C_pruned)：
            - C_sim: 需要模拟的有效联盟集合
            - C_pruned: 被剪枝的无效联盟集合
        """
        start_time = time.time()
        
        # 转换为集合以便高效操作
        all_agents_set = set(all_agents)
        analyst_agents_set = set(analyst_agents)
        
        self.logger.info(f"开始联盟生成和剪枝")
        self.logger.info(f"总智能体数: {len(all_agents_set)}")
        self.logger.info(f"分析智能体: {analyst_agents_set}")
        self.logger.info(f"交易智能体: {trader_agent}")
        
        # 验证输入参数
        if trader_agent not in all_agents_set:
            raise ValueError(f"交易智能体 {trader_agent} 不在智能体列表中")
        
        if not analyst_agents_set.issubset(all_agents_set):
            missing = analyst_agents_set - all_agents_set
            raise ValueError(f"分析智能体 {missing} 不在智能体列表中")
        
        # 生成所有可能的联盟
        all_coalitions = self._generate_all_subsets(all_agents_set)
        total_coalitions = len(all_coalitions)
        
        self.logger.info(f"生成了 {total_coalitions} 个初始联盟")
        
        # 应用剪枝规则
        valid_coalitions = set()
        pruned_coalitions = set()
        
        for coalition in all_coalitions:
            coalition_set = set(coalition)
            
            # 剪枝规则1：终点缺失 - 必须包含交易智能体
            if trader_agent not in coalition_set:
                pruned_coalitions.add(frozenset(coalition_set))
                continue
            
            # 剪枝规则2：起点缺失 - 必须至少包含一个分析智能体
            if len(coalition_set.intersection(analyst_agents_set)) == 0:
                pruned_coalitions.add(frozenset(coalition_set))
                continue
            
            # 通过所有剪枝规则的联盟为有效联盟
            valid_coalitions.add(frozenset(coalition_set))
        
        # 更新统计信息
        generation_time = time.time() - start_time
        self._stats.update({
            "total_generated": total_coalitions,
            "valid_coalitions": len(valid_coalitions),
            "pruned_coalitions": len(pruned_coalitions),
            "generation_time": generation_time,
            "last_generation": datetime.now()
        })
        
        # 计算剪枝效率
        pruning_efficiency = (len(pruned_coalitions) / total_coalitions) * 100 if total_coalitions > 0 else 0
        
        self.logger.info(f"联盟剪枝完成:")
        self.logger.info(f"  - 总联盟数: {total_coalitions}")
        self.logger.info(f"  - 有效联盟: {len(valid_coalitions)}")
        self.logger.info(f"  - 剪枝联盟: {len(pruned_coalitions)}")
        self.logger.info(f"  - 剪枝效率: {pruning_efficiency:.1f}%")
        self.logger.info(f"  - 生成耗时: {generation_time:.3f}s")
        
        return valid_coalitions, pruned_coalitions
    
    def analyze_coalition_structure(self, 
                                  valid_coalitions: Set[frozenset], 
                                  all_agents: Union[List[str], Set[str]]) -> Dict[str, Any]:
        """
        分析联盟结构特征
        
        提供有效联盟的详细统计分析，包括大小分布、智能体参与度等。
        
        参数:
            valid_coalitions: 有效联盟集合
            all_agents: 所有智能体列表
            
        返回:
            包含联盟结构分析结果的字典
        """
        if not valid_coalitions:
            return {
                "total_coalitions": 0,
                "size_distribution": {},
                "agent_participation": {},
                "average_size": 0.0,
                "max_size": 0,
                "min_size": 0
            }
        
        all_agents_set = set(all_agents)
        
        # 联盟大小分布
        size_distribution = {}
        coalition_sizes = []
        
        for coalition in valid_coalitions:
            size = len(coalition)
            coalition_sizes.append(size)
            size_distribution[size] = size_distribution.get(size, 0) + 1
        
        # 智能体参与度统计
        agent_participation = {agent: 0 for agent in all_agents_set}
        for coalition in valid_coalitions:
            for agent in coalition:
                agent_participation[agent] += 1
        
        # 计算参与率
        total_coalitions = len(valid_coalitions)
        agent_participation_rate = {
            agent: (count / total_coalitions) * 100 
            for agent, count in agent_participation.items()
        }
        
        analysis = {
            "total_coalitions": total_coalitions,
            "size_distribution": size_distribution,
            "agent_participation": agent_participation,
            "agent_participation_rate": agent_participation_rate,
            "average_size": sum(coalition_sizes) / len(coalition_sizes) if coalition_sizes else 0.0,
            "max_size": max(coalition_sizes) if coalition_sizes else 0,
            "min_size": min(coalition_sizes) if coalition_sizes else 0
        }
        
        self.logger.debug(f"联盟结构分析完成: {total_coalitions} 个有效联盟")
        return analysis
    
    def get_coalition_by_size(self, 
                            valid_coalitions: Set[frozenset], 
                            size: int) -> List[Set[str]]:
        """
        获取指定大小的所有联盟
        
        参数:
            valid_coalitions: 有效联盟集合
            size: 联盟大小
            
        返回:
            指定大小的联盟列表
        """
        return [set(coalition) for coalition in valid_coalitions if len(coalition) == size]
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取联盟生成统计信息
        
        返回:
            包含统计信息的字典
        """
        stats = self._stats.copy()
        if stats["total_generated"] > 0:
            stats["pruning_efficiency"] = (stats["pruned_coalitions"] / stats["total_generated"]) * 100
        else:
            stats["pruning_efficiency"] = 0.0
        return stats
    
    def validate_coalition_constraints(self, 
                                     coalition: Union[Set[str], List[str]], 
                                     agent_graph: Optional[Dict[str, List[str]]] = None) -> Tuple[bool, List[str]]:
        """
        验证联盟是否满足依赖关系约束
        
        检查联盟中的智能体是否满足依赖关系图定义的约束条件。
        
        参数:
            coalition: 要验证的联盟
            agent_graph: 智能体依赖关系图，如果为None则使用默认图
            
        返回:
            元组 (is_valid, violations)：
            - is_valid: 联盟是否有效
            - violations: 违反约束的描述列表
        """
        coalition_set = set(coalition)
        graph = agent_graph or self.default_agent_graph
        violations = []
        
        for agent in coalition_set:
            if agent not in graph:
                violations.append(f"未知智能体: {agent}")
                continue
            
            # 检查依赖关系
            dependencies = graph[agent]
            missing_deps = set(dependencies) - coalition_set
            
            if missing_deps:
                violations.append(f"智能体 {agent} 缺少依赖: {missing_deps}")
        
        is_valid = len(violations) == 0
        return is_valid, violations
