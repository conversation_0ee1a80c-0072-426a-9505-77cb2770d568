#!/usr/bin/env python3
"""
集成数据管理器 (Integrated Data Manager)

统一管理所有数据存储组件，提供简化的接口给主运行脚本使用：
1. 统一初始化所有数据存储组件
2. 提供简化的数据收集接口
3. 自动化数据流程管理
4. 状态监控和报告

作者: AI Assistant
创建时间: 2025-07-04
"""

import os
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional

from .comprehensive_storage_manager import ComprehensiveStorageManager, StorageConfig
from .trading_data_collector import TradingDataCollector
from .trading_data_extractor import TradingDataExtractor
from .prompt_optimization_tracker import PromptOptimizationTracker
from .visualization_manager import VisualizationManager, VisualizationConfig
from .ab_testing_framework import ABTestingFramework
from .data_analysis_tools import DataAnalysisTools
from .backup_manager import BackupManager, BackupConfig

class IntegratedDataManager:
    """
    集成数据管理器
    
    统一管理所有数据存储和分析组件
    """
    
    def __init__(self, 
                 config: Optional[Dict[str, Any]] = None,
                 logger: Optional[logging.Logger] = None):
        """
        初始化集成数据管理器
        
        参数:
            config: 配置字典
            logger: 日志记录器
        """
        self.config = config or {}
        self.logger = logger or self._create_default_logger()
        
        # 初始化状态
        self.enabled = self.config.get("comprehensive_storage", {}).get("enabled", True)
        self.components_initialized = False
        
        # 组件实例
        self.storage_manager = None
        self.trading_collector = None
        self.trading_extractor = None
        self.prompt_tracker = None
        self.visualization_manager = None
        self.ab_testing_framework = None
        self.data_analysis_tools = None
        self.backup_manager = None
        
        # 初始化组件
        if self.enabled:
            self._initialize_components()
        
        self.logger.info(f"集成数据管理器初始化完成 (启用: {self.enabled})")
    
    def _create_default_logger(self) -> logging.Logger:
        """创建默认日志记录器"""
        logger = logging.getLogger(f"{__name__}.IntegratedDataManager")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def _initialize_components(self):
        """初始化所有数据存储组件"""
        try:
            # 1. 初始化存储管理器
            self.storage_manager = ComprehensiveStorageManager(
                config=self.config.get("storage", {}),
                logger=self.logger
            )
            
            # 2. 初始化交易数据收集器
            self.trading_collector = TradingDataCollector(
                storage_manager=self.storage_manager,
                logger=self.logger
            )
            
            # 3. 初始化交易数据提取器
            self.trading_extractor = TradingDataExtractor(
                data_collector=self.trading_collector,
                logger=self.logger
            )
            
            # 4. 初始化提示词优化跟踪器
            self.prompt_tracker = PromptOptimizationTracker(
                storage_manager=self.storage_manager,
                logger=self.logger
            )
            
            # 5. 初始化可视化管理器
            viz_config = VisualizationConfig()
            self.visualization_manager = VisualizationManager(
                storage_manager=self.storage_manager,
                config=viz_config,
                logger=self.logger
            )
            
            # 6. 初始化A/B测试框架
            self.ab_testing_framework = ABTestingFramework(
                storage_manager=self.storage_manager,
                logger=self.logger
            )

            # 7. 初始化数据分析工具
            self.data_analysis_tools = DataAnalysisTools(
                storage_manager=self.storage_manager,
                logger=self.logger
            )

            # 8. 初始化备份管理器
            backup_config = BackupConfig()
            if "comprehensive_storage" in self.config:
                storage_config = self.config["comprehensive_storage"]
                backup_config.backup_interval_hours = storage_config.get("auto_backup_interval_hours", 24)

            self.backup_manager = BackupManager(
                storage_manager=self.storage_manager,
                config=backup_config,
                logger=self.logger
            )

            self.components_initialized = True
            self.logger.info("所有数据存储组件初始化完成")
            
        except Exception as e:
            self.logger.error(f"初始化数据存储组件失败: {e}")
            self.components_initialized = False
            raise
    
    def process_assessment_result(self, assessment_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理评估结果，提取和存储相关数据
        
        参数:
            assessment_result: 贡献度评估结果
            
        返回:
            处理结果摘要
        """
        if not self.enabled or not self.components_initialized:
            return {"enabled": False, "message": "数据存储功能未启用"}
        
        try:
            processing_summary = {
                "timestamp": datetime.now().isoformat(),
                "assessment_processed": False,
                "trading_data_extracted": False,
                "visualizations_generated": False,
                "errors": []
            }
            
            # 1. 提取交易数据
            try:
                if self.trading_extractor:
                    success = self.trading_extractor.extract_from_assessment_result(assessment_result)
                    processing_summary["trading_data_extracted"] = success
                    if success:
                        self.logger.info("从评估结果提取交易数据成功")
            except Exception as e:
                error_msg = f"提取交易数据失败: {e}"
                processing_summary["errors"].append(error_msg)
                self.logger.error(error_msg)
            
            # 2. 生成可视化（如果有股票数据）
            try:
                config = assessment_result.get("config", {})
                stocks = config.get("stocks", [])
                start_date = config.get("start_date")
                end_date = config.get("end_date")
                
                if stocks and start_date and end_date and self.visualization_manager:
                    viz_results = []
                    for stock in stocks[:2]:  # 限制处理前2只股票
                        result = self.visualization_manager.generate_price_chart(
                            stock, start_date, end_date, "line"
                        )
                        if result.get("success"):
                            viz_results.append(result)
                    
                    processing_summary["visualizations_generated"] = len(viz_results) > 0
                    processing_summary["visualization_results"] = viz_results
                    
                    if viz_results:
                        self.logger.info(f"生成了 {len(viz_results)} 个可视化图表")
                
            except Exception as e:
                error_msg = f"生成可视化失败: {e}"
                processing_summary["errors"].append(error_msg)
                self.logger.error(error_msg)
            
            processing_summary["assessment_processed"] = True
            return processing_summary
            
        except Exception as e:
            self.logger.error(f"处理评估结果失败: {e}")
            return {
                "enabled": True,
                "assessment_processed": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    def track_prompt_optimization(self, 
                                agent_id: str,
                                original_prompt: str,
                                optimized_prompt: str,
                                optimization_reason: str,
                                performance_metrics: Dict[str, float]) -> Dict[str, Any]:
        """
        跟踪提示词优化
        
        参数:
            agent_id: 智能体ID
            original_prompt: 原始提示词
            optimized_prompt: 优化后提示词
            optimization_reason: 优化原因
            performance_metrics: 性能指标
            
        返回:
            跟踪结果
        """
        if not self.enabled or not self.components_initialized:
            return {"enabled": False, "message": "数据存储功能未启用"}
        
        try:
            if not self.prompt_tracker:
                return {"success": False, "error": "提示词跟踪器未初始化"}

            optimization_id = self.prompt_tracker.track_optimization(
                agent_id=agent_id,
                original_prompt=original_prompt,
                optimized_prompt=optimized_prompt,
                optimization_reason=optimization_reason,
                performance_metrics=performance_metrics
            )
            
            if optimization_id:
                self.logger.info(f"提示词优化跟踪成功: {optimization_id}")
                return {
                    "success": True,
                    "optimization_id": optimization_id,
                    "timestamp": datetime.now().isoformat()
                }
            else:
                return {
                    "success": False,
                    "error": "跟踪提示词优化失败"
                }
                
        except Exception as e:
            self.logger.error(f"跟踪提示词优化失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def generate_comprehensive_report(self) -> Dict[str, Any]:
        """
        生成综合数据报告
        
        返回:
            综合报告
        """
        if not self.enabled or not self.components_initialized:
            return {"enabled": False, "message": "数据存储功能未启用"}
        
        try:
            report = {
                "report_timestamp": datetime.now().isoformat(),
                "system_status": "active",
                "components_status": {},
                "storage_statistics": {},
                "recent_activities": {},
                "recommendations": []
            }
            
            # 1. 存储统计
            if self.storage_manager:
                report["storage_statistics"] = self.storage_manager.get_storage_statistics()
            
            # 2. 组件状态
            report["components_status"] = {
                "storage_manager": self.storage_manager is not None,
                "trading_collector": self.trading_collector is not None,
                "trading_extractor": self.trading_extractor is not None,
                "prompt_tracker": self.prompt_tracker is not None,
                "visualization_manager": self.visualization_manager is not None,
                "ab_testing_framework": self.ab_testing_framework is not None,
                "data_analysis_tools": self.data_analysis_tools is not None,
                "backup_manager": self.backup_manager is not None
            }
            
            # 3. 最近活动
            if self.ab_testing_framework:
                active_tests = self.ab_testing_framework.get_active_tests()
                report["recent_activities"]["active_ab_tests"] = len(active_tests)
                report["recent_activities"]["ab_tests_details"] = active_tests[:5]  # 最近5个
            
            # 4. 生成建议
            recommendations = []
            
            storage_stats = report.get("storage_statistics", {})
            if storage_stats.get("total_trading_sessions", 0) == 0:
                recommendations.append("尚未收集交易数据，建议运行评估以开始数据收集")
            
            if storage_stats.get("total_prompt_optimizations", 0) == 0:
                recommendations.append("尚未进行提示词优化，建议启动OPRO优化流程")
            
            if not report["recent_activities"].get("active_ab_tests", 0):
                recommendations.append("当前无活跃A/B测试，建议创建测试验证优化效果")
            
            report["recommendations"] = recommendations
            
            return report
            
        except Exception as e:
            self.logger.error(f"生成综合报告失败: {e}")
            return {
                "enabled": True,
                "error": str(e),
                "report_timestamp": datetime.now().isoformat()
            }
    
    def export_all_data(self, export_format: str = "json") -> Dict[str, Any]:
        """
        导出所有数据

        参数:
            export_format: 导出格式

        返回:
            导出结果
        """
        if not self.enabled or not self.components_initialized:
            return {"enabled": False, "message": "数据存储功能未启用"}

        try:
            if self.storage_manager:
                return self.storage_manager.export_data(
                    export_format=export_format,
                    data_types=["trading", "prompts"]
                )
            else:
                return {"success": False, "error": "存储管理器未初始化"}

        except Exception as e:
            self.logger.error(f"导出所有数据失败: {e}")
            return {"success": False, "error": str(e)}

    def generate_analysis_report(self, report_type: str = "comprehensive") -> Dict[str, Any]:
        """
        生成分析报告

        参数:
            report_type: 报告类型 ("trading", "optimization", "comprehensive")

        返回:
            报告生成结果
        """
        if not self.enabled or not self.components_initialized:
            return {"enabled": False, "message": "数据存储功能未启用"}

        try:
            if not self.data_analysis_tools:
                return {"success": False, "error": "数据分析工具未初始化"}

            if report_type == "trading":
                return self.data_analysis_tools.export_trading_performance_analysis()
            elif report_type == "optimization":
                return self.data_analysis_tools.export_prompt_optimization_analysis()
            elif report_type == "comprehensive":
                return self.data_analysis_tools.generate_comprehensive_dashboard()
            else:
                return {"success": False, "error": f"不支持的报告类型: {report_type}"}

        except Exception as e:
            self.logger.error(f"生成分析报告失败: {e}")
            return {"success": False, "error": str(e)}
    
    def create_backup(self, backup_type: str = "auto") -> Dict[str, Any]:
        """
        创建数据备份

        参数:
            backup_type: 备份类型 ("full", "incremental", "auto")

        返回:
            备份结果
        """
        if not self.enabled or not self.components_initialized:
            return {"enabled": False, "message": "数据存储功能未启用"}

        try:
            if not self.backup_manager:
                return {"success": False, "error": "备份管理器未初始化"}

            if backup_type == "full":
                return self.backup_manager.create_full_backup()
            elif backup_type == "incremental":
                return self.backup_manager.create_incremental_backup()
            else:  # auto
                return self.backup_manager.create_incremental_backup()

        except Exception as e:
            self.logger.error(f"创建数据备份失败: {e}")
            return {"success": False, "error": str(e)}

    def get_backup_status(self) -> Dict[str, Any]:
        """
        获取备份状态

        返回:
            备份状态信息
        """
        if not self.enabled or not self.components_initialized:
            return {"enabled": False, "message": "数据存储功能未启用"}

        try:
            if self.backup_manager:
                return self.backup_manager.get_backup_status()
            else:
                return {"enabled": False, "error": "备份管理器未初始化"}

        except Exception as e:
            self.logger.error(f"获取备份状态失败: {e}")
            return {"enabled": False, "error": str(e)}

    def list_backups(self) -> List[Dict[str, Any]]:
        """
        列出所有备份

        返回:
            备份列表
        """
        if not self.enabled or not self.components_initialized:
            return []

        try:
            if self.backup_manager:
                return self.backup_manager.list_backups()
            else:
                return []

        except Exception as e:
            self.logger.error(f"列出备份失败: {e}")
            return []
    
    def get_system_status(self) -> Dict[str, Any]:
        """
        获取系统状态
        
        返回:
            系统状态信息
        """
        return {
            "enabled": self.enabled,
            "components_initialized": self.components_initialized,
            "timestamp": datetime.now().isoformat(),
            "components": {
                "storage_manager": self.storage_manager is not None,
                "trading_collector": self.trading_collector is not None,
                "trading_extractor": self.trading_extractor is not None,
                "prompt_tracker": self.prompt_tracker is not None,
                "visualization_manager": self.visualization_manager is not None,
                "ab_testing_framework": self.ab_testing_framework is not None,
                "data_analysis_tools": self.data_analysis_tools is not None,
                "backup_manager": self.backup_manager is not None
            }
        }
    
    def cleanup(self):
        """清理资源"""
        try:
            # 这里可以添加清理逻辑，比如关闭数据库连接等
            self.logger.info("集成数据管理器资源清理完成")
        except Exception as e:
            self.logger.error(f"资源清理失败: {e}")
