"""
基础智能体类 (Base Agent Class)

定义所有智能体的基础接口和通用功能
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
import logging
from datetime import datetime


class BaseAgent(ABC):
    """
    基础智能体抽象类
    
    所有智能体都必须继承此类并实现process方法
    """
    
    def __init__(self, agent_id: str, llm_interface=None, logger: Optional[logging.Logger] = None):
        """
        初始化基础智能体
        
        参数:
            agent_id: 智能体唯一标识符
            llm_interface: LLM接口实例
            logger: 日志记录器
        """
        self.agent_id = agent_id
        self.llm_interface = llm_interface
        self.logger = logger or self._create_default_logger()
        
        # 智能体状态
        self.last_analysis = None
        self.analysis_count = 0
        self.total_processing_time = 0.0
        
        self.logger.info(f"智能体 {self.agent_id} 初始化完成")
    
    def _create_default_logger(self) -> logging.Logger:
        """创建默认日志记录器"""
        logger = logging.getLogger(f"agents.{self.agent_id}")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    @abstractmethod
    def get_prompt_template(self) -> str:
        """
        获取该智能体的提示词模板
        
        返回:
            提示词模板字符串
        """
        pass
    
    @abstractmethod
    def process(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理输入状态并生成分析结果
        
        参数:
            state: 包含市场数据、历史状态等信息的状态字典
            
        返回:
            分析结果字典
        """
        pass
    
    def format_state_for_llm(self, state: Dict[str, Any]) -> str:
        """
        将状态信息格式化为LLM可读的文本
        
        参数:
            state: 状态字典
            
        返回:
            格式化的状态描述
        """
        formatted_parts = []
        
        # 基础信息
        current_date = state.get("current_date", state.get("date", "未知"))
        formatted_parts.append(f"📅 分析日期: {current_date}")
        
        # 分析期间信息
        if "analysis_period" in state:
            period = state["analysis_period"]
            start_date = period.get("start_date", "未知")
            end_date = period.get("end_date", "未知")
            formatted_parts.append(f"📊 分析期间: {start_date} 至 {end_date}")
        
        # 股票价格信息
        if "stock_data" in state:
            formatted_parts.append("\n📈 股票价格信息:")
            for symbol, data in state["stock_data"].items():
                if hasattr(data, 'iloc') and len(data) > 0:
                    current_price = data.iloc[-1]['close'] if 'close' in data.columns else "N/A"
                    formatted_parts.append(f"  • {symbol}: ${current_price}")
        
        # 投资组合信息
        if "portfolio" in state:
            formatted_parts.append(f"\n💰 投资组合: {state['portfolio']}")
        
        if "cash" in state:
            formatted_parts.append(f"💵 可用现金: ${state['cash']:,.2f}")
        
        # 前序智能体输出
        if "previous_outputs" in state and state["previous_outputs"]:
            formatted_parts.append("\n🤖 前序智能体分析:")
            for agent_id, output in state["previous_outputs"].items():
                if isinstance(output, dict):
                    summary = output.get("summary", output.get("reasoning", str(output)[:100] + "..."))
                    formatted_parts.append(f"  • {agent_id}: {summary}")
        
        return "\n".join(formatted_parts)
    
    def call_llm(self, prompt: str, state: Dict[str, Any]) -> Dict[str, Any]:
        """
        调用LLM进行分析
        
        参数:
            prompt: 提示词
            state: 当前状态
            
        返回:
            LLM分析结果
        """
        if not self.llm_interface:
            self.logger.warning(f"智能体 {self.agent_id} 没有LLM接口，返回默认结果")
            return self._get_default_output(state)
        
        try:
            start_time = datetime.now()
            
            # 格式化状态信息
            state_info = self.format_state_for_llm(state)
            
            # 构建完整提示词
            full_prompt = f"{prompt}\n\n{state_info}\n\n请基于以上信息进行分析，并以JSON格式返回结果。"
            
            # 显示LLM输入（用户要求看到LLM的输入输出）
            self.logger.info("=" * 80)
            self.logger.info(f"🤖 {self.agent_id} LLM输入:")
            self.logger.info("-" * 40)
            self.logger.info(full_prompt)
            self.logger.info("-" * 40)
            
            # 调用LLM
            response = self.llm_interface.analyze(prompt=full_prompt, model="glm-4-flash")
            
            # 显示LLM输出
            self.logger.info(f"🤖 {self.agent_id} LLM输出:")
            self.logger.info("-" * 40)
            self.logger.info(response)
            self.logger.info("=" * 80)
            
            # 记录统计信息
            processing_time = (datetime.now() - start_time).total_seconds()
            self.total_processing_time += processing_time
            self.analysis_count += 1
            
            # 解析响应
            if isinstance(response, dict):
                result = response
            else:
                # 尝试解析JSON响应
                import json
                try:
                    result = json.loads(response)
                except json.JSONDecodeError:
                    result = {"raw_response": response, "analysis": response}
            
            # 添加元数据
            result.update({
                "agent_id": self.agent_id,
                "timestamp": datetime.now().isoformat(),
                "processing_time": processing_time,
                "llm_used": True
            })
            
            self.last_analysis = result
            return result
            
        except Exception as e:
            self.logger.error(f"智能体 {self.agent_id} LLM调用失败: {e}")
            return self._get_default_output(state)
    
    def _get_default_output(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取默认输出（当LLM不可用时）
        
        参数:
            state: 当前状态
            
        返回:
            默认分析结果
        """
        return {
            "agent_id": self.agent_id,
            "analysis": f"默认分析 - {self.agent_id}",
            "confidence": 0.5,
            "reasoning": "LLM不可用，使用默认分析",
            "timestamp": datetime.now().isoformat(),
            "llm_used": False
        }
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取智能体统计信息
        
        返回:
            统计信息字典
        """
        avg_processing_time = (
            self.total_processing_time / self.analysis_count 
            if self.analysis_count > 0 else 0.0
        )
        
        return {
            "agent_id": self.agent_id,
            "analysis_count": self.analysis_count,
            "total_processing_time": self.total_processing_time,
            "average_processing_time": avg_processing_time,
            "has_llm": self.llm_interface is not None,
            "last_analysis_time": (
                self.last_analysis.get("timestamp") 
                if self.last_analysis else None
            )
        }