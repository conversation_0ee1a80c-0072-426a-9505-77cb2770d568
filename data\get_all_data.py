#!/usr/bin/env python3
"""
综合数据获取脚本

一站式获取股票的 OHLCV 数据、新闻数据和财务报表数据
用法: python data/get_all_data.py <ticker> <start_date> <end_date> [--verbose]
示例: python data/get_all_data.py AAPL 2025-01-01 2025-05-01
"""

import os
import sys
import json
import sqlite3
import subprocess
import time
from datetime import datetime, timedelta, date
import argparse # Added argparse for --verbose in main
from typing import List, Tuple, Optional, Dict, Any, Union

# Add project root to sys.path
current_script_path = os.path.abspath(__file__)
project_root = os.path.dirname(os.path.dirname(current_script_path))
sys.path.append(project_root)

# Import from config
try:
    from config import DATA_DIR, ALPHAVANTAGE_API_KEY
except ImportError:
    print("Error: config.py not found or required variables not defined.", file=sys.stderr)
    print("Please ensure config.py exists with DATA_DIR, ALPHAVANTAGE_API_KEY defined.", file=sys.stderr)
    sys.exit(1)

# 获取数据库路径
def get_database_path(ticker):
    """
    根据ticker获取对应的数据库路径
    """
    ticker_dir = os.path.join(DATA_DIR, "tickers", ticker.upper())
    os.makedirs(ticker_dir, exist_ok=True)
    return os.path.join(ticker_dir, f"{ticker.upper()}_data.db")

# 旧版数据库路径(兼容旧代码)
LEGACY_DATABASE_PATH = os.path.join(DATA_DIR, "trading_data.db")
DATE_FORMAT = '%Y-%m-%d'

def _create_connection(ticker):
    """创建数据库连接"""
    try:
        database_path = get_database_path(ticker)
        conn = sqlite3.connect(database_path)
        conn.row_factory = sqlite3.Row
        return conn
    except sqlite3.Error as e:
        print(f"数据库连接错误: {e}", file=sys.stderr)
        return None

def ensure_database_structure(ticker, verbose: bool = False):
    """确保ticker对应的数据库结构已创建"""
    if verbose:
        print(f"确保{ticker}的数据库结构已创建...", file=sys.stderr)
    try:
        prepare_script_path = os.path.join(os.path.dirname(__file__), 'prepare_data.py')
        # Use sys.executable to ensure the correct python interpreter is used
        cmd = [sys.executable, prepare_script_path, ticker]
        # Do not capture output unless verbose, to avoid hanging if prepare_data asks for input.
        # For now, let it print directly.
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, encoding='utf-8')
        stdout, stderr = process.communicate()
        
        if process.returncode != 0:
            if verbose:
                print(f"prepare_data.py completed with code {process.returncode}", file=sys.stderr)
            if stdout:
                if verbose: print("prepare_data.py STDOUT:", stdout, file=sys.stderr)
            if stderr:
                print("prepare_data.py STDERR:", stderr, file=sys.stderr) # Show stderr always if error
            # raise Exception(f"prepare_data.py failed with code {process.returncode}") # Option: raise
        elif verbose:
            if stdout.strip(): print("prepare_data.py 输出:", stdout, file=sys.stderr)
            if stderr.strip(): print("prepare_data.py 错误输出:", stderr, file=sys.stderr)
            
    except Exception as e:
        print(f"运行 prepare_data.py 时出错: {e}", file=sys.stderr)
        # Depending on severity, you might want to re-raise or sys.exit
        # For now, just print, as manage_stock_data will continue or fail on DB ops

def _execute_data_script(script_name: str, args: List[str], verbose: bool = False) -> bool:
    """Helper to execute a data fetching sub-script."""
    script_path = os.path.join(os.path.dirname(__file__), script_name)
    cmd = [sys.executable, script_path] + args
    if verbose:
        print(f"执行脚本: {' '.join(cmd)}", file=sys.stderr)
    try:
        # Let subprocess print its stdout/stderr directly for now if not capturing.
        # If capturing:
        # process = subprocess.run(cmd, check=True, capture_output=True, text=True, encoding='utf-8')
        # if verbose:
        #     if process.stdout.strip(): print(f"{script_name} STDOUT: {process.stdout.strip()}", file=sys.stderr)
        # if process.stderr.strip(): # Always print stderr from script if any
        #     print(f"{script_name} STDERR: {process.stderr.strip()}", file=sys.stderr)
        
        # Simpler: run and allow output, check return code.
        result = subprocess.run(cmd, text=True, encoding='utf-8') # Removed capture_output
        if result.returncode != 0:
            print(f"{script_name} 执行失败，返回码: {result.returncode}", file=sys.stderr)
            # Optionally print stdout/stderr from result if needed, though it should have printed live
            return False
        if verbose:
            print(f"{script_name} 执行成功。", file=sys.stderr)
        return True
    except FileNotFoundError:
        print(f"错误: 脚本 {script_path} 未找到。", file=sys.stderr)
        return False
    except subprocess.CalledProcessError as e:
        print(f"脚本 {script_name} 执行时发生错误: {e}", file=sys.stderr)
        if verbose and e.stdout: print(f"STDOUT: {e.stdout}", file=sys.stderr)
        if e.stderr: print(f"STDERR: {e.stderr}", file=sys.stderr) # Always print stderr
        return False
    except Exception as e:
        print(f"执行 {script_name} 时发生未知错误: {e}", file=sys.stderr)
        return False

def _get_db_date_range(conn: sqlite3.Connection, ticker: str, table_name: str, date_column: str = 'date') -> Tuple[Optional[date], Optional[date]]:
    """获取指定表和股票的现有数据日期范围 (min_date, max_date)"""
    min_date_str, max_date_str = None, None
    try:
        # 根据表名选择正确的查询条件
        if table_name == 'ohlcv':
            # OHLCV表需要按ticker过滤，日期字段是trade_date
            query_min = f"SELECT {date_column} FROM {table_name} WHERE ticker = ? AND {date_column} IS NOT NULL AND {date_column} != '' ORDER BY {date_column} ASC LIMIT 1"
            query_max = f"SELECT {date_column} FROM {table_name} WHERE ticker = ? AND {date_column} IS NOT NULL AND {date_column} != '' ORDER BY {date_column} DESC LIMIT 1"
            query_params = (ticker,)
        else:
            # 新闻表不按ticker过滤，日期字段是time_published
            query_min = f"SELECT {date_column} FROM {table_name} WHERE {date_column} IS NOT NULL AND {date_column} != '' ORDER BY {date_column} ASC LIMIT 1"
            query_max = f"SELECT {date_column} FROM {table_name} WHERE {date_column} IS NOT NULL AND {date_column} != '' ORDER BY {date_column} DESC LIMIT 1"
            query_params = ()

        # Query for MIN date
        cursor = conn.execute(query_min, query_params)
        row = cursor.fetchone()
        if row and row[0]:
            min_date_str = row[0]

        # Query for MAX date
        cursor = conn.execute(query_max, query_params)
        row = cursor.fetchone()
        if row and row[0]:
            max_date_str = row[0]

        # 解析日期字符串
        min_date_obj = None
        max_date_obj = None
        
        if min_date_str:
            min_date_obj = _parse_date_string(min_date_str)
        if max_date_str:
            max_date_obj = _parse_date_string(max_date_str)
        
        return min_date_obj, max_date_obj
    except sqlite3.Error as e:
        print(f"查询 {table_name} 日期范围时出错 for {ticker}: {e}", file=sys.stderr)
    except Exception as e:
        print(f"处理 {table_name} 日期时出错 for {ticker}: {e}", file=sys.stderr)
    return None, None

def _parse_date_string(date_str: str) -> Optional[date]:
    """解析各种格式的日期字符串"""
    if not date_str:
        return None
    
    # 尝试不同的日期格式
    formats_to_try = [
        '%Y-%m-%d',           # 2025-01-01
        '%Y-%m-%d %H:%M:%S',  # 2025-01-01 10:30:00
        '%Y%m%dT%H%M%S',      # 20250101T103000
        '%Y%m%d',             # 20250101
    ]
    
    for fmt in formats_to_try:
        try:
            return datetime.strptime(date_str, fmt).date()
        except ValueError:
            continue
    
    # 如果所有格式都失败，尝试提取日期部分
    try:
        # 尝试提取前10个字符作为日期 (YYYY-MM-DD)
        if len(date_str) >= 10 and date_str[4] == '-' and date_str[7] == '-':
            return datetime.strptime(date_str[:10], '%Y-%m-%d').date()
        
        # 尝试提取前8个字符作为日期 (YYYYMMDD)
        if len(date_str) >= 8 and date_str[:8].isdigit():
            return datetime.strptime(date_str[:8], '%Y%m%d').date()
    except ValueError:
        pass
    
    print(f"无法解析日期字符串: '{date_str}'", file=sys.stderr)
    return None

def _check_financial_data_exists_in_range(conn: sqlite3.Connection, ticker: str, table_name: str, req_start_date: date, req_end_date: date) -> bool:
    """检查财务报表数据在指定日期范围内是否存在"""
    try:
        # fiscal_date is the key date column for financial tables
        query = f"SELECT 1 FROM {table_name} WHERE ticker = ? AND fiscal_date >= ? AND fiscal_date <= ? LIMIT 1"
        # Convert date objects to string for query
        cursor = conn.execute(query, (ticker, req_start_date.strftime(DATE_FORMAT), req_end_date.strftime(DATE_FORMAT)))
        return cursor.fetchone() is not None
    except sqlite3.Error as e:
        print(f"查询 {table_name} 财务数据时出错 for {ticker}: {e}", file=sys.stderr)
        return False # Assume missing on error to trigger download

def manage_stock_data(ticker: str, req_start_date_str: str, req_end_date_str: str, verbose: bool = False):
    """
    管理指定股票的数据：检查、下载缺失部分、更新重叠部分。
    使用独立的ticker数据库。
    """
    if verbose:
        print(f"开始管理股票 {ticker} 的数据，请求范围: {req_start_date_str} 至 {req_end_date_str}", file=sys.stderr)

    if not ALPHAVANTAGE_API_KEY or ALPHAVANTAGE_API_KEY == "YOUR_ALPHA_VANTAGE_API_KEY":
        print("错误: 请在 config.py 中配置您的 Alpha Vantage API 密钥。", file=sys.stderr)
        # raise ValueError("Alpha Vantage API Key not configured.") # Or return False
        return False # Indicate failure

    # 0. 确保数据库表结构存在
    # 为特定ticker确保数据库结构
    ensure_database_structure(ticker, verbose)


    conn = _create_connection(ticker)
    if not conn:
        return False # Indicate failure

    try:
        req_start_date_obj = datetime.strptime(req_start_date_str, DATE_FORMAT).date()
        req_end_date_obj = datetime.strptime(req_end_date_str, DATE_FORMAT).date()
    except ValueError:
        print(f"错误: 无效的日期格式。请使用 {DATE_FORMAT} (例如 2023-01-01)。", file=sys.stderr)
        if conn: conn.close()
        return False

    all_success = True

    # 1. 处理 OHLCV 数据
    if verbose: print(f"\n--- 处理 {ticker} OHLCV 数据 ---", file=sys.stderr)
    db_min_ohlcv, db_max_ohlcv = _get_db_date_range(conn, ticker, 'ohlcv', 'trade_date')
    if verbose: print(f"数据库中 {ticker} OHLCV 范围: {db_min_ohlcv} 至 {db_max_ohlcv}", file=sys.stderr)

    # 决定是否需要调用 get_OHLCV_data.py
    # 检查数据库中是否已有足够的数据覆盖请求范围
    should_fetch_ohlcv = False  # 默认为 False，只有在需要时才下载
    
    if db_min_ohlcv is None or db_max_ohlcv is None:
        # 数据库中没有数据，需要下载
        should_fetch_ohlcv = True
        if verbose: print(f"{ticker} OHLCV 数据库为空，需要下载数据", file=sys.stderr)
    elif req_start_date_obj < db_min_ohlcv or req_end_date_obj > db_max_ohlcv:
        # 请求的日期范围超出了数据库中的范围，需要下载
        should_fetch_ohlcv = True
        if verbose: print(f"{ticker} OHLCV 请求范围 ({req_start_date_obj} 至 {req_end_date_obj}) 超出数据库范围 ({db_min_ohlcv} 至 {db_max_ohlcv})，需要下载数据", file=sys.stderr)
    else:
        # 数据库中已有足够的数据覆盖请求范围
        if verbose: print(f"{ticker} OHLCV 数据库已覆盖请求范围 ({req_start_date_obj} 至 {req_end_date_obj})，跳过下载", file=sys.stderr)

    if should_fetch_ohlcv:
        if verbose: print(f"计划为 {ticker} 执行一次OHLCV全历史数据下载/更新。调用 get_OHLCV_data.py...", file=sys.stderr)
        # 调用 get_OHLCV_data.py 时传递用户原始的日期范围作为名义参数
        # get_OHLCV_data.py 内部会忽略这些日期进行过滤，而是下载全部历史
        if not _execute_data_script('get_OHLCV_data.py', [ticker, req_start_date_str, req_end_date_str], verbose):
            all_success = False # Stop on first failure for this type
    elif verbose:
        print(f"{ticker} OHLCV 数据：根据现有数据和请求范围，且未强制刷新，无需下载。", file=sys.stderr)


    # 2. 处理新闻数据 (简化逻辑，类似OHLCV)
    if verbose: print(f"\n--- 处理 {ticker} 新闻数据 ---", file=sys.stderr)
    # News data uses time_published column
    db_min_news, db_max_news = _get_db_date_range(conn, ticker, 'news', date_column='time_published')
    if verbose: print(f"数据库中 {ticker} 新闻范围: {db_min_news} 至 {db_max_news}", file=sys.stderr)

    # 决定是否需要下载新闻数据
    should_fetch_news = False  # 默认为 False，只有在需要时才下载
    
    if db_min_news is None or db_max_news is None:
        # 数据库中没有新闻数据，需要下载
        should_fetch_news = True
        if verbose: print(f"{ticker} 新闻数据库为空，需要下载数据", file=sys.stderr)
    elif req_start_date_obj < db_min_news or req_end_date_obj > db_max_news:
        # 请求的日期范围超出了数据库中的范围，需要下载
        should_fetch_news = True
        if verbose: print(f"{ticker} 新闻请求范围 ({req_start_date_obj} 至 {req_end_date_obj}) 超出数据库范围 ({db_min_news} 至 {db_max_news})，需要下载数据", file=sys.stderr)
    else:
        # 数据库中已有足够的数据覆盖请求范围
        if verbose: print(f"{ticker} 新闻数据库已覆盖请求范围 ({req_start_date_obj} 至 {req_end_date_obj})，跳过下载", file=sys.stderr)

    if should_fetch_news:
        if verbose: print(f"计划为 {ticker} 下载新闻数据，范围: {req_start_date_str} 至 {req_end_date_str}", file=sys.stderr)
        if not _execute_data_script('get_news_data.py', [ticker, req_start_date_str, req_end_date_str], verbose):
            all_success = False
    elif verbose:
        print(f"{ticker} 新闻数据：根据现有数据和请求范围，无需下载。", file=sys.stderr)


    # 3. 处理财务报表数据
    if verbose: print(f"\n--- 处理 {ticker} 财务报表数据 ---", file=sys.stderr)
    needs_annual_financials = not _check_financial_data_exists_in_range(conn, ticker, 'annual_financials', req_start_date_obj, req_end_date_obj)
    needs_quarterly_financials = not _check_financial_data_exists_in_range(conn, ticker, 'quarterly_financials', req_start_date_obj, req_end_date_obj)

    if needs_annual_financials or needs_quarterly_financials:
        if verbose:
            missing_types = []
            if needs_annual_financials: missing_types.append("年度")
            if needs_quarterly_financials: missing_types.append("季度")
            print(f"{ticker} 的 {'、'.join(missing_types)} 财务报表数据在请求范围 {req_start_date_str}-{req_end_date_str} 内可能缺失或不完整。", file=sys.stderr)
            print(f"将尝试下载/更新 {ticker} 的所有财务报表数据...", file=sys.stderr)
        if not _execute_data_script('get_fundamental_data.py', [ticker], verbose):
            all_success = False
    elif verbose:
        print(f"{ticker} 的年度和季度财务报表数据在请求范围 {req_start_date_str}-{req_end_date_str} 内已存在。", file=sys.stderr)
    
    if conn:
        conn.close()
    
    if verbose:
        if all_success:
            print(f"\n对 {ticker} 的数据管理完成。", file=sys.stderr)
        else:
            print(f"\n对 {ticker} 的数据管理部分失败。", file=sys.stderr)
            
    return all_success

def main():
    """主函数 - 直接运行此脚本时使用"""
    parser = argparse.ArgumentParser(description="获取指定股票的所有相关数据。")
    parser.add_argument("ticker", type=str, help="股票代码 (例如 AAPL)")
    parser.add_argument("start_date", type=str, help="开始日期 (YYYY-MM-DD)")
    parser.add_argument("end_date", type=str, help="结束日期 (YYYY-MM-DD)")
    parser.add_argument("--verbose", action="store_true", help="启用详细输出")
    args = parser.parse_args()
    
    ticker = args.ticker.upper()
    
    # 0. 确保数据目录和基本数据库结构存在
    os.makedirs(DATA_DIR, exist_ok=True)
    os.makedirs(os.path.join(DATA_DIR, "tickers"), exist_ok=True)
    ensure_database_structure(ticker, args.verbose)

    success = manage_stock_data(ticker, args.start_date, args.end_date, args.verbose)
    
    if success:
        db_path = get_database_path(ticker)
        print(f"\n所有数据已成功为 {ticker} 在范围 {args.start_date}-{args.end_date} 内管理完毕。")
        # Final summary from user query
        print(f"数据库现在应该包含 {ticker} 的以下数据:")
        print("- ohlcv: 股票价格和交易量数据")
        print("- news: 新闻数据")
        print("- annual_financials: 年度财务报表数据")
        print("- quarterly_financials: 季度财务报表数据")
        print(f"数据存储在: {db_path}")
    else:
        print(f"\n为 {ticker} 管理数据时发生一个或多个错误。请检查上面的日志。")
        sys.exit(1)

if __name__ == "__main__":
    main() 