# 真实日历交易日计算修复

## 问题描述

用户报告运行 **2025-01-01 到 2025-01-15**（15天）时，系统错误显示了 **4个交易周**，但实际应该只有 **2-3个交易周**。

## 根本原因

1. **硬编码默认值**: `simulation_days` 被硬编码为 20 天
2. **粗糙估算**: 使用 70% 估算交易日，没有考虑实际的周末和节假日
3. **数据不匹配**: 周期性计算基于配置天数而非实际数据长度

## 修复方案

### ✅ 1. 真实日历检查

实现了精确的美股交易日历计算：

```python
def _count_trading_days(self, start_date: str, end_date: str) -> int:
    """
    计算指定日期范围内的实际交易天数（排除周末和美股节假日）
    """
    # 生成完整日期范围
    date_range = pd.date_range(start=start_dt, end=end_dt, freq='D')
    
    # 检查每一天
    for date in date_range:
        # 排除周末（周六=5, 周日=6）
        if date.weekday() >= 5:
            weekend_days += 1
            continue
        
        # 排除美股节假日
        if date in us_holidays:
            holiday_days += 1
            continue
        
        # 其他为交易日
        trading_days += 1
```

### ✅ 2. 完整节假日数据库

包含 2023-2026 年的美股节假日：

- 新年节 (New Year's Day)
- 马丁·路德·金日 (MLK Day)  
- 总统日 (Presidents' Day)
- 耶稣受难日 (Good Friday)
- 阵亡将士纪念日 (Memorial Day)
- 六月节 (Juneteenth)
- 独立日 (Independence Day)
- 劳工节 (Labor Day)
- 感恩节 (Thanksgiving)
- 圣诞节 (Christmas Day)

### ✅ 3. 智能降级机制

如果真实日历计算失败，自动降级到改进的估算方法：

```python
def _estimate_trading_days_fallback(self, start_date: str, end_date: str) -> int:
    # 更精确的估算：69% 交易日（排除周末和节假日）
    estimated_trading_days = int(calendar_days * 0.69)
```

### ✅ 4. 基于实际数据的周期划分

周期性 Shapley 计算现在基于实际数据长度：

```python
# 根据实际数据确定模拟天数
actual_data_days = max(len(returns) for returns in coalition_daily_returns.values())
total_weeks = (actual_data_days + trading_days_per_week - 1) // trading_days_per_week
```

## 修复效果验证

### 🎯 2025-01-01 到 2025-01-15 的精确分析

**日历分析**:
```
2025-01-01 (周三): 新年节假日 ❌
2025-01-02 (周四): 交易日 ✅
2025-01-03 (周五): 交易日 ✅
2025-01-04 (周六): 周末 ❌
2025-01-05 (周日): 周末 ❌
2025-01-06 (周一): 交易日 ✅
2025-01-07 (周二): 交易日 ✅
2025-01-08 (周三): 交易日 ✅
2025-01-09 (周四): 交易日 ✅
2025-01-10 (周五): 交易日 ✅
2025-01-11 (周六): 周末 ❌
2025-01-12 (周日): 周末 ❌
2025-01-13 (周一): 交易日 ✅
2025-01-14 (周二): 交易日 ✅
2025-01-15 (周三): 交易日 ✅
```

**统计结果**:
- 总天数: 15
- 周末天数: 4
- 节假日天数: 1 (新年)
- **交易天数: 10** ✅

**周期划分**:
- 第1周: 第1-5天 (01-02 到 01-10)
- 第2周: 第6-10天 (01-13 到 01-15)
- **总周数: 2** ✅

### 📊 修复前后对比

| 项目 | 修复前 | 修复后 |
|------|--------|--------|
| **计算方法** | 硬编码20天 | 真实日历检查 |
| **交易天数** | 20天 (错误) | 10天 (正确) |
| **周期数** | 4周 (错误) | 2周 (正确) |
| **无效周期** | 后3周全为0 | 无无效周期 |
| **准确性** | ❌ | ✅ |

### 🧪 测试验证

所有测试案例均通过：

1. **✅ 原问题 (0101-0115)**: 15天 → 10个交易日 → 2周
2. **✅ 完整工作周 (0106-0112)**: 7天 → 5个交易日 → 1周  
3. **✅ 跨月范围 (0127-0207)**: 12天 → 10个交易日 → 2周
4. **✅ 单日范围 (0102-0102)**: 1天 → 1个交易日 → 1周

## 使用方法

### 自动使用（推荐）

修复已集成到现有系统，无需更改使用方式：

```bash
python run_contribution_assessment.py \
    --llm-provider zhipuai \
    --start-date 2025-01-01 \
    --end-date 2025-01-15 \
    --verbose
```

### 手动指定天数

如果需要覆盖自动计算：

```python
config = {
    "start_date": "2025-01-01",
    "end_date": "2025-01-15", 
    "simulation_days": 12,  # 手动指定
    # ...
}
```

### 验证计算

运行测试脚本验证：

```bash
python3 test_date_fix.py
```

## 技术细节

### 降级策略

1. **优先级1**: 真实日历检查（准确性最高）
2. **优先级2**: 改进估算方法（69%比率）  
3. **优先级3**: 最小值保护（至少1个交易日）

### 性能影响

- **计算开销**: 微不足道（毫秒级）
- **内存影响**: 无显著影响
- **兼容性**: 完全向后兼容

### 扩展性

- 节假日数据库可轻松扩展到更多年份
- 支持不同市场的交易日历（只需修改节假日列表）
- 可适配不同的周期划分策略

## 总结

这个修复解决了交易日计算的根本问题，确保：

1. **🎯 精确性**: 基于真实的美股交易日历
2. **🔄 自动化**: 无需手动配置，自动适配日期范围  
3. **🛡️ 健壮性**: 多重降级策略，确保系统稳定
4. **📈 准确性**: Shapley 值计算基于真实交易周期

现在你的 **2025-01-01 到 2025-01-15** 测试将正确显示 **2个交易周**，每周都有有意义的 Shapley 值，不再有无效的零值周期！