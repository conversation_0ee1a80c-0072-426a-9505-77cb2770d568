#!/usr/bin/env python3
"""
A/B测试框架 (A/B Testing Framework)

为提示词优化提供A/B测试和实验验证功能：
1. 对比实验设计和执行
2. 统计显著性测试
3. 性能指标对比分析
4. 实验结果报告生成
5. 多变体测试支持

作者: AI Assistant
创建时间: 2025-07-04
"""

import os
import json
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from scipy import stats
import uuid

from .comprehensive_storage_manager import ComprehensiveStorageManager

@dataclass
class ABTestConfig:
    """A/B测试配置"""
    test_name: str
    agent_id: str
    variants: List[str]  # 提示词变体列表
    test_duration_hours: int = 24
    min_sample_size: int = 10
    significance_level: float = 0.05
    success_metric: str = "shapley_value"
    metadata: Dict[str, Any] = None

@dataclass
class ABTestResult:
    """A/B测试结果"""
    variant_id: str
    sample_size: int
    mean_performance: float
    std_performance: float
    confidence_interval: Tuple[float, float]
    raw_data: List[float]

@dataclass
class ABTestAnalysis:
    """A/B测试分析结果"""
    test_id: str
    test_name: str
    agent_id: str
    start_time: str
    end_time: str
    total_samples: int
    variants_results: List[ABTestResult]
    statistical_analysis: Dict[str, Any]
    recommendations: List[str]
    best_variant: str
    confidence_level: float

class ABTestingFramework:
    """
    A/B测试框架
    
    提供完整的A/B测试功能，用于验证提示词优化效果
    """
    
    def __init__(self, 
                 storage_manager: ComprehensiveStorageManager,
                 logger: Optional[logging.Logger] = None):
        """
        初始化A/B测试框架
        
        参数:
            storage_manager: 存储管理器实例
            logger: 日志记录器
        """
        self.storage_manager = storage_manager
        self.logger = logger or self._create_default_logger()
        
        # 活跃测试跟踪
        self.active_tests = {}  # {test_id: ABTestConfig}
        self.test_results = {}  # {test_id: List[performance_data]}
        
        # 加载现有测试
        self._load_existing_tests()
        
        self.logger.info("A/B测试框架初始化完成")
    
    def _create_default_logger(self) -> logging.Logger:
        """创建默认日志记录器"""
        logger = logging.getLogger(f"{__name__}.ABTestingFramework")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def _load_existing_tests(self):
        """加载现有的A/B测试"""
        try:
            with self.storage_manager._get_db_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT test_id, agent_id, test_name, variants, start_time, 
                           end_time, status, results
                    FROM ab_tests
                    WHERE status = 'active'
                """)
                
                rows = cursor.fetchall()
                
                for row in rows:
                    test_id, agent_id, test_name, variants_json, start_time, end_time, status, results_json = row
                    
                    try:
                        variants = json.loads(variants_json) if variants_json else []
                        
                        # 检查测试是否过期
                        if end_time and datetime.fromisoformat(end_time) < datetime.now():
                            # 自动结束过期测试
                            self._finalize_expired_test(test_id)
                            continue
                        
                        # 重建测试配置
                        config = ABTestConfig(
                            test_name=test_name,
                            agent_id=agent_id,
                            variants=variants
                        )
                        
                        self.active_tests[test_id] = config
                        
                        # 加载测试结果
                        if results_json:
                            self.test_results[test_id] = json.loads(results_json)
                        else:
                            self.test_results[test_id] = []
                        
                    except Exception as e:
                        self.logger.error(f"加载测试失败 {test_id}: {e}")
                
                self.logger.info(f"加载了 {len(self.active_tests)} 个活跃A/B测试")
                
        except Exception as e:
            self.logger.error(f"加载现有A/B测试失败: {e}")
    
    def start_ab_test(self, config: ABTestConfig) -> str:
        """
        启动A/B测试
        
        参数:
            config: A/B测试配置
            
        返回:
            测试ID
        """
        try:
            test_id = f"ab_test_{config.agent_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{uuid.uuid4().hex[:8]}"
            
            # 验证配置
            if not self._validate_test_config(config):
                raise ValueError("A/B测试配置无效")
            
            # 计算结束时间
            start_time = datetime.now()
            end_time = start_time + timedelta(hours=config.test_duration_hours)
            
            # 保存到数据库
            with self.storage_manager._get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO ab_tests 
                    (test_id, agent_id, test_name, variants, start_time, end_time, 
                     status, results, statistical_analysis)
                    VALUES (?, ?, ?, ?, ?, ?, 'active', '[]', '{}')
                """, (
                    test_id,
                    config.agent_id,
                    config.test_name,
                    json.dumps(config.variants, ensure_ascii=False),
                    start_time.isoformat(),
                    end_time.isoformat()
                ))
                conn.commit()
            
            # 添加到活跃测试
            self.active_tests[test_id] = config
            self.test_results[test_id] = []
            
            self.logger.info(f"A/B测试已启动: {test_id} ({config.test_name})")
            return test_id
            
        except Exception as e:
            self.logger.error(f"启动A/B测试失败: {e}")
            return ""
    
    def record_test_result(self, 
                         test_id: str, 
                         variant_id: str, 
                         performance_score: float,
                         metadata: Optional[Dict[str, Any]] = None) -> bool:
        """
        记录测试结果
        
        参数:
            test_id: 测试ID
            variant_id: 变体ID
            performance_score: 性能分数
            metadata: 元数据
            
        返回:
            是否记录成功
        """
        try:
            if test_id not in self.active_tests:
                self.logger.error(f"测试不存在或已结束: {test_id}")
                return False
            
            # 验证变体ID
            config = self.active_tests[test_id]
            if variant_id not in [f"variant_{i}" for i in range(len(config.variants))]:
                self.logger.error(f"无效的变体ID: {variant_id}")
                return False
            
            # 记录结果
            result_record = {
                "variant_id": variant_id,
                "performance_score": performance_score,
                "timestamp": datetime.now().isoformat(),
                "metadata": metadata or {}
            }
            
            self.test_results[test_id].append(result_record)
            
            # 更新数据库
            with self.storage_manager._get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    UPDATE ab_tests 
                    SET results = ?
                    WHERE test_id = ?
                """, (
                    json.dumps(self.test_results[test_id], ensure_ascii=False),
                    test_id
                ))
                conn.commit()
            
            self.logger.debug(f"记录测试结果: {test_id} -> {variant_id} = {performance_score}")
            
            # 检查是否达到最小样本量
            self._check_test_completion(test_id)
            
            return True
            
        except Exception as e:
            self.logger.error(f"记录测试结果失败: {e}")
            return False
    
    def analyze_test_results(self, test_id: str) -> Optional[ABTestAnalysis]:
        """
        分析测试结果
        
        参数:
            test_id: 测试ID
            
        返回:
            分析结果
        """
        try:
            if test_id not in self.test_results:
                self.logger.error(f"测试结果不存在: {test_id}")
                return None
            
            config = self.active_tests.get(test_id)
            if not config:
                self.logger.error(f"测试配置不存在: {test_id}")
                return None
            
            results_data = self.test_results[test_id]
            if not results_data:
                self.logger.warning(f"测试暂无数据: {test_id}")
                return None
            
            # 按变体分组数据
            variant_data = {}
            for result in results_data:
                variant_id = result["variant_id"]
                if variant_id not in variant_data:
                    variant_data[variant_id] = []
                variant_data[variant_id].append(result["performance_score"])
            
            # 计算每个变体的统计信息
            variants_results = []
            for variant_id, scores in variant_data.items():
                if len(scores) > 0:
                    mean_score = np.mean(scores)
                    std_score = np.std(scores, ddof=1) if len(scores) > 1 else 0.0
                    
                    # 计算置信区间
                    if len(scores) > 1:
                        ci = stats.t.interval(
                            1 - config.significance_level,
                            len(scores) - 1,
                            loc=mean_score,
                            scale=stats.sem(scores)
                        )
                    else:
                        ci = (mean_score, mean_score)
                    
                    variants_results.append(ABTestResult(
                        variant_id=variant_id,
                        sample_size=len(scores),
                        mean_performance=mean_score,
                        std_performance=std_score,
                        confidence_interval=ci,
                        raw_data=scores
                    ))
            
            # 统计显著性测试
            statistical_analysis = self._perform_statistical_tests(variants_results, config.significance_level)
            
            # 确定最佳变体
            best_variant = max(variants_results, key=lambda x: x.mean_performance).variant_id
            
            # 生成建议
            recommendations = self._generate_test_recommendations(variants_results, statistical_analysis)
            
            # 计算置信水平
            confidence_level = self._calculate_overall_confidence(variants_results, statistical_analysis)
            
            analysis = ABTestAnalysis(
                test_id=test_id,
                test_name=config.test_name,
                agent_id=config.agent_id,
                start_time=datetime.now().isoformat(),  # 简化处理
                end_time=datetime.now().isoformat(),
                total_samples=len(results_data),
                variants_results=variants_results,
                statistical_analysis=statistical_analysis,
                recommendations=recommendations,
                best_variant=best_variant,
                confidence_level=confidence_level
            )
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"分析测试结果失败: {e}")
            return None
    
    def _perform_statistical_tests(self, variants_results: List[ABTestResult], significance_level: float) -> Dict[str, Any]:
        """执行统计显著性测试"""
        analysis = {
            "significance_level": significance_level,
            "tests_performed": [],
            "significant_differences": []
        }
        
        try:
            # 如果只有两个变体，执行t检验
            if len(variants_results) == 2:
                var1, var2 = variants_results[0], variants_results[1]
                
                if len(var1.raw_data) > 1 and len(var2.raw_data) > 1:
                    # 独立样本t检验
                    t_stat, p_value = stats.ttest_ind(var1.raw_data, var2.raw_data)
                    
                    analysis["tests_performed"].append({
                        "test_type": "independent_t_test",
                        "variants": [var1.variant_id, var2.variant_id],
                        "t_statistic": float(t_stat),
                        "p_value": float(p_value),
                        "significant": p_value < significance_level
                    })
                    
                    if p_value < significance_level:
                        better_variant = var1.variant_id if var1.mean_performance > var2.mean_performance else var2.variant_id
                        analysis["significant_differences"].append({
                            "better_variant": better_variant,
                            "p_value": float(p_value),
                            "effect_size": abs(var1.mean_performance - var2.mean_performance)
                        })
            
            # 如果有多个变体，执行ANOVA
            elif len(variants_results) > 2:
                all_data = [var.raw_data for var in variants_results if len(var.raw_data) > 1]
                
                if len(all_data) >= 2:
                    f_stat, p_value = stats.f_oneway(*all_data)
                    
                    analysis["tests_performed"].append({
                        "test_type": "one_way_anova",
                        "variants": [var.variant_id for var in variants_results],
                        "f_statistic": float(f_stat),
                        "p_value": float(p_value),
                        "significant": p_value < significance_level
                    })
                    
                    # 如果ANOVA显著，进行事后检验
                    if p_value < significance_level:
                        analysis["significant_differences"].append({
                            "test_type": "anova",
                            "p_value": float(p_value),
                            "note": "存在显著差异，建议进行事后检验"
                        })
            
        except Exception as e:
            self.logger.error(f"统计测试失败: {e}")
            analysis["error"] = str(e)
        
        return analysis
    
    def _generate_test_recommendations(self, variants_results: List[ABTestResult], statistical_analysis: Dict[str, Any]) -> List[str]:
        """生成测试建议"""
        recommendations = []
        
        # 检查样本量
        min_sample_size = min(var.sample_size for var in variants_results)
        if min_sample_size < 10:
            recommendations.append(f"样本量较小（最小: {min_sample_size}），建议增加测试时间以获得更可靠的结果")
        
        # 检查显著性
        significant_tests = [test for test in statistical_analysis.get("tests_performed", []) if test.get("significant", False)]
        if significant_tests:
            recommendations.append("检测到统计显著差异，可以采用表现最佳的变体")
        else:
            recommendations.append("未检测到统计显著差异，建议继续测试或重新设计实验")
        
        # 检查效果大小
        best_var = max(variants_results, key=lambda x: x.mean_performance)
        worst_var = min(variants_results, key=lambda x: x.mean_performance)
        effect_size = abs(best_var.mean_performance - worst_var.mean_performance)
        
        if effect_size > 0.1:
            recommendations.append(f"效果差异较大（{effect_size:.3f}），建议采用最佳变体")
        elif effect_size < 0.05:
            recommendations.append("效果差异较小，可能需要更长时间的测试")
        
        return recommendations
    
    def _calculate_overall_confidence(self, variants_results: List[ABTestResult], statistical_analysis: Dict[str, Any]) -> float:
        """计算整体置信水平"""
        try:
            # 基于样本量和统计测试结果计算置信度
            total_samples = sum(var.sample_size for var in variants_results)
            
            # 基础置信度（基于样本量）
            base_confidence = min(total_samples / 100.0, 0.8)
            
            # 统计显著性加成
            significant_tests = [test for test in statistical_analysis.get("tests_performed", []) if test.get("significant", False)]
            if significant_tests:
                base_confidence += 0.2
            
            return min(base_confidence, 1.0)
            
        except Exception:
            return 0.5  # 默认置信度
    
    def _validate_test_config(self, config: ABTestConfig) -> bool:
        """验证测试配置"""
        if not config.test_name or not config.agent_id:
            return False
        
        if len(config.variants) < 2:
            return False
        
        if config.test_duration_hours <= 0:
            return False
        
        if config.min_sample_size <= 0:
            return False
        
        return True
    
    def _check_test_completion(self, test_id: str):
        """检查测试是否完成"""
        try:
            config = self.active_tests.get(test_id)
            if not config:
                return
            
            results_data = self.test_results.get(test_id, [])
            
            # 按变体统计样本量
            variant_counts = {}
            for result in results_data:
                variant_id = result["variant_id"]
                variant_counts[variant_id] = variant_counts.get(variant_id, 0) + 1
            
            # 检查是否所有变体都达到最小样本量
            min_samples_met = all(count >= config.min_sample_size for count in variant_counts.values())
            
            if min_samples_met:
                self.logger.info(f"测试 {test_id} 达到最小样本量要求，可以进行分析")
        
        except Exception as e:
            self.logger.error(f"检查测试完成状态失败: {e}")
    
    def _finalize_expired_test(self, test_id: str):
        """结束过期测试"""
        try:
            with self.storage_manager._get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    UPDATE ab_tests 
                    SET status = 'completed'
                    WHERE test_id = ?
                """, (test_id,))
                conn.commit()
            
            self.logger.info(f"过期测试已结束: {test_id}")
            
        except Exception as e:
            self.logger.error(f"结束过期测试失败: {e}")
    
    def get_active_tests(self) -> List[Dict[str, Any]]:
        """获取活跃测试列表"""
        active_tests_info = []
        
        for test_id, config in self.active_tests.items():
            results_count = len(self.test_results.get(test_id, []))
            
            active_tests_info.append({
                "test_id": test_id,
                "test_name": config.test_name,
                "agent_id": config.agent_id,
                "variants_count": len(config.variants),
                "results_count": results_count,
                "status": "active"
            })
        
        return active_tests_info
