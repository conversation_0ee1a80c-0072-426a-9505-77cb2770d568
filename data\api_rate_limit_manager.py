#!/usr/bin/env python3
"""
API速率限制管理器

管理Alpha Vantage API的调用限制，包括：
1. API调用次数跟踪
2. 多密钥轮换
3. 智能重试机制
4. 调用间隔控制
"""

import os
import sys
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import logging
import random

# 添加项目根目录到路径
current_script_path = os.path.abspath(__file__)
project_root = os.path.dirname(os.path.dirname(current_script_path))
sys.path.append(project_root)

try:
    from config import DATA_DIR, ALPHAVANTAGE_API_KEY
except ImportError as e:
    print(f"导入配置失败: {e}", file=sys.stderr)
    sys.exit(1)

class APIRateLimitManager:
    """API速率限制管理器"""
    
    def __init__(self, 
                 primary_api_key: str = None,
                 backup_api_keys: List[str] = None,
                 max_calls_per_day: int = 25,
                 min_call_interval: int = 12,
                 verbose: bool = True):
        """
        初始化API速率限制管理器
        
        Args:
            primary_api_key: 主要API密钥
            backup_api_keys: 备用API密钥列表
            max_calls_per_day: 每日最大调用次数
            min_call_interval: 最小调用间隔（秒）
            verbose: 是否显示详细日志
        """
        self.verbose = verbose
        self.logger = self._setup_logger()
        
        # API密钥管理
        self.primary_api_key = primary_api_key or ALPHAVANTAGE_API_KEY
        self.backup_api_keys = backup_api_keys or []
        self.current_api_key = self.primary_api_key
        self.api_key_index = 0
        
        # 速率限制参数
        self.max_calls_per_day = max_calls_per_day
        self.min_call_interval = min_call_interval
        
        # 调用统计
        self.call_stats_file = os.path.join(DATA_DIR, "cache", "api_call_stats.json")
        self.call_history = self._load_call_history()
        
        # 最后调用时间
        self.last_call_time = None
        
        # 重试配置
        self.max_retries = 3
        self.retry_delays = [60, 300, 900]  # 1分钟, 5分钟, 15分钟
        
        self.logger.info("API速率限制管理器初始化完成")
        self.logger.info(f"主要API密钥: {self.primary_api_key[:8]}...")
        self.logger.info(f"备用密钥数量: {len(self.backup_api_keys)}")
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger("APIRateLimitManager")
        logger.setLevel(logging.INFO if self.verbose else logging.WARNING)
        
        if not logger.handlers:
            handler = logging.StreamHandler(sys.stderr)
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _load_call_history(self) -> Dict[str, Dict[str, int]]:
        """加载API调用历史"""
        try:
            if os.path.exists(self.call_stats_file):
                with open(self.call_stats_file, 'r') as f:
                    return json.load(f)
            else:
                return {}
        except Exception as e:
            self.logger.warning(f"加载API调用历史失败: {e}")
            return {}
    
    def _save_call_history(self) -> None:
        """保存API调用历史"""
        try:
            os.makedirs(os.path.dirname(self.call_stats_file), exist_ok=True)
            with open(self.call_stats_file, 'w') as f:
                json.dump(self.call_history, f, indent=2)
        except Exception as e:
            self.logger.warning(f"保存API调用历史失败: {e}")
    
    def get_current_api_key(self) -> str:
        """获取当前API密钥"""
        return self.current_api_key
    
    def get_today_call_count(self, api_key: str = None) -> int:
        """获取今日API调用次数"""
        if api_key is None:
            api_key = self.current_api_key
        
        today = datetime.now().strftime('%Y-%m-%d')
        return self.call_history.get(api_key, {}).get(today, 0)
    
    def can_make_call(self, api_key: str = None) -> bool:
        """检查是否可以进行API调用"""
        if api_key is None:
            api_key = self.current_api_key
        
        # 检查今日调用次数
        today_calls = self.get_today_call_count(api_key)
        if today_calls >= self.max_calls_per_day:
            return False
        
        # 检查调用间隔
        if self.last_call_time:
            elapsed = time.time() - self.last_call_time
            if elapsed < self.min_call_interval:
                return False
        
        return True
    
    def wait_for_next_call(self) -> None:
        """等待到下次可以调用的时间"""
        if self.last_call_time:
            elapsed = time.time() - self.last_call_time
            remaining = self.min_call_interval - elapsed
            
            if remaining > 0:
                self.logger.info(f"等待 {remaining:.1f} 秒后进行下次API调用...")
                time.sleep(remaining)
    
    def record_api_call(self, api_key: str = None, success: bool = True) -> None:
        """记录API调用"""
        if api_key is None:
            api_key = self.current_api_key
        
        today = datetime.now().strftime('%Y-%m-%d')
        
        # 初始化记录
        if api_key not in self.call_history:
            self.call_history[api_key] = {}
        
        if today not in self.call_history[api_key]:
            self.call_history[api_key][today] = 0
        
        # 增加调用次数
        self.call_history[api_key][today] += 1
        
        # 更新最后调用时间
        self.last_call_time = time.time()
        
        # 保存历史
        self._save_call_history()
        
        if success:
            self.logger.info(f"API调用成功 (今日: {self.call_history[api_key][today]}/{self.max_calls_per_day})")
        else:
            self.logger.warning(f"API调用失败 (今日: {self.call_history[api_key][today]}/{self.max_calls_per_day})")
    
    def switch_to_next_api_key(self) -> bool:
        """切换到下一个可用的API密钥"""
        all_keys = [self.primary_api_key] + self.backup_api_keys
        
        # 尝试找到可用的API密钥
        for i in range(len(all_keys)):
            next_index = (self.api_key_index + 1 + i) % len(all_keys)
            next_key = all_keys[next_index]
            
            if self.can_make_call(next_key):
                self.current_api_key = next_key
                self.api_key_index = next_index
                self.logger.info(f"切换到API密钥: {next_key[:8]}...")
                return True
        
        self.logger.warning("没有可用的API密钥")
        return False
    
    def execute_with_rate_limit(self, func, *args, **kwargs):
        """
        在速率限制下执行函数
        
        Args:
            func: 要执行的函数
            *args: 函数参数
            **kwargs: 函数关键字参数
            
        Returns:
            函数执行结果
        """
        for attempt in range(self.max_retries + 1):
            # 检查当前密钥是否可用
            if not self.can_make_call():
                if not self.switch_to_next_api_key():
                    if attempt < self.max_retries:
                        delay = self.retry_delays[min(attempt, len(self.retry_delays) - 1)]
                        self.logger.warning(f"所有API密钥都已达到限制，等待 {delay} 秒后重试...")
                        time.sleep(delay)
                        continue
                    else:
                        raise Exception("所有API密钥都已达到每日调用限制")
            
            # 等待调用间隔
            self.wait_for_next_call()
            
            try:
                # 执行函数
                result = func(*args, **kwargs)
                
                # 记录成功调用
                self.record_api_call(success=True)
                
                return result
                
            except Exception as e:
                # 记录失败调用
                self.record_api_call(success=False)
                
                # 检查是否是速率限制错误
                error_msg = str(e).lower()
                if any(keyword in error_msg for keyword in ['rate limit', 'api limit', 'quota', 'throttle']):
                    self.logger.warning(f"遇到API速率限制: {e}")
                    
                    if attempt < self.max_retries:
                        # 尝试切换API密钥
                        if self.switch_to_next_api_key():
                            continue
                        else:
                            delay = self.retry_delays[min(attempt, len(self.retry_delays) - 1)]
                            self.logger.warning(f"等待 {delay} 秒后重试...")
                            time.sleep(delay)
                            continue
                
                # 其他错误直接抛出
                if attempt == self.max_retries:
                    raise e
                else:
                    self.logger.warning(f"API调用失败 (尝试 {attempt + 1}/{self.max_retries + 1}): {e}")
                    time.sleep(self.retry_delays[min(attempt, len(self.retry_delays) - 1)])
        
        raise Exception(f"API调用在 {self.max_retries + 1} 次尝试后仍然失败")
    
    def get_api_usage_summary(self) -> Dict[str, Any]:
        """获取API使用摘要"""
        today = datetime.now().strftime('%Y-%m-%d')
        summary = {
            "current_api_key": f"{self.current_api_key[:8]}...",
            "total_api_keys": 1 + len(self.backup_api_keys),
            "today_usage": {},
            "total_remaining_calls": 0,
            "next_reset_time": "明天 00:00:00"
        }
        
        all_keys = [self.primary_api_key] + self.backup_api_keys
        
        for key in all_keys:
            used_calls = self.get_today_call_count(key)
            remaining_calls = max(0, self.max_calls_per_day - used_calls)
            
            summary["today_usage"][f"{key[:8]}..."] = {
                "used": used_calls,
                "remaining": remaining_calls,
                "limit": self.max_calls_per_day
            }
            
            summary["total_remaining_calls"] += remaining_calls
        
        return summary
    
    def cleanup_old_records(self, days_to_keep: int = 30) -> None:
        """清理旧的调用记录"""
        cutoff_date = datetime.now() - timedelta(days=days_to_keep)
        cutoff_str = cutoff_date.strftime('%Y-%m-%d')
        
        cleaned_count = 0
        for api_key in list(self.call_history.keys()):
            dates_to_remove = []
            for date_str in self.call_history[api_key]:
                if date_str < cutoff_str:
                    dates_to_remove.append(date_str)
            
            for date_str in dates_to_remove:
                del self.call_history[api_key][date_str]
                cleaned_count += 1
        
        if cleaned_count > 0:
            self.logger.info(f"清理了 {cleaned_count} 条旧的API调用记录")
            self._save_call_history()


if __name__ == "__main__":
    # 测试代码
    import argparse
    
    parser = argparse.ArgumentParser(description="API速率限制管理器")
    parser.add_argument("--summary", action="store_true", help="显示API使用摘要")
    parser.add_argument("--cleanup", action="store_true", help="清理旧记录")
    parser.add_argument("--test", action="store_true", help="测试API调用")
    
    args = parser.parse_args()
    
    manager = APIRateLimitManager(verbose=True)
    
    if args.summary:
        summary = manager.get_api_usage_summary()
        print("\n📊 API使用摘要:")
        print(json.dumps(summary, indent=2, ensure_ascii=False))
    
    if args.cleanup:
        manager.cleanup_old_records()
        print("✅ 旧记录清理完成")
    
    if args.test:
        def mock_api_call():
            print("模拟API调用...")
            time.sleep(1)
            return "API调用成功"
        
        try:
            result = manager.execute_with_rate_limit(mock_api_call)
            print(f"测试结果: {result}")
        except Exception as e:
            print(f"测试失败: {e}")
