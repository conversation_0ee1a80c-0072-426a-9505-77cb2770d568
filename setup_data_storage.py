#!/usr/bin/env python3
"""
数据存储系统安装和设置脚本

该脚本用于：
1. 检查和安装必要的依赖
2. 创建必要的目录结构
3. 初始化数据库
4. 验证系统配置
5. 运行基本功能测试

作者: AI Assistant
创建时间: 2025-07-04
"""

import os
import sys
import json
import subprocess
import logging
from pathlib import Path

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('setup.log')
        ]
    )
    return logging.getLogger(__name__)

def check_python_version():
    """检查Python版本"""
    logger = logging.getLogger(__name__)
    
    if sys.version_info < (3, 8):
        logger.error("需要Python 3.8或更高版本")
        return False
    
    logger.info(f"Python版本检查通过: {sys.version}")
    return True

def install_dependencies():
    """安装必要的依赖包"""
    logger = logging.getLogger(__name__)
    
    required_packages = [
        'pandas>=1.3.0',
        'numpy>=1.21.0',
        'matplotlib>=3.4.0',
        'seaborn>=0.11.0',
        'plotly>=5.0.0',
        'scipy>=1.7.0',
        'openpyxl>=3.0.0',
        'schedule>=1.1.0'
    ]
    
    logger.info("开始安装依赖包...")
    
    for package in required_packages:
        try:
            logger.info(f"安装 {package}...")
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install', package
            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            logger.info(f"✓ {package} 安装成功")
        except subprocess.CalledProcessError:
            logger.error(f"✗ {package} 安装失败")
            return False
    
    logger.info("所有依赖包安装完成")
    return True

def create_directory_structure():
    """创建目录结构"""
    logger = logging.getLogger(__name__)
    
    directories = [
        'data',
        'data/trading',
        'data/prompts',
        'data/visualizations',
        'data/exports',
        'data/backups',
        'data/backups/full',
        'data/backups/incremental',
        'data/backups/metadata',
        'logs',
        'docs',
        'examples',
        'config'
    ]
    
    logger.info("创建目录结构...")
    
    for directory in directories:
        try:
            os.makedirs(directory, exist_ok=True)
            logger.info(f"✓ 创建目录: {directory}")
        except Exception as e:
            logger.error(f"✗ 创建目录失败 {directory}: {e}")
            return False
    
    logger.info("目录结构创建完成")
    return True

def create_default_config():
    """创建默认配置文件"""
    logger = logging.getLogger(__name__)
    
    config_file = 'config/opro_config.json'
    
    if os.path.exists(config_file):
        logger.info("配置文件已存在，跳过创建")
        return True
    
    default_config = {
        "description": "OPRO系统配置文件",
        "version": "1.0",
        "last_updated": "2025-07-04",
        
        "system": {
            "agents": ["NAA", "TAA", "FAA", "BOA", "BeOA", "NOA", "TRA"],
            "default_stocks": ["AAPL", "MSFT", "GOOGL"],
            "default_date_range": {
                "start_date": "2024-01-01",
                "end_date": "2024-01-31"
            }
        },
        
        "storage": {
            "comprehensive_storage": {
                "enabled": True,
                "base_path": "data",
                "trading_data_path": "data/trading",
                "prompts_data_path": "data/prompts",
                "visualizations_path": "data/visualizations",
                "exports_path": "data/exports",
                "backups_path": "data/backups",
                "database_path": "data/comprehensive_storage.db",
                "auto_backup_interval_hours": 24,
                "data_validation_enabled": True,
                "compression_enabled": True,
                "max_file_size_mb": 100
            }
        },
        
        "optimization": {
            "enabled": True,
            "max_iterations": 10,
            "improvement_threshold": 0.05
        },
        
        "evaluation": {
            "simulation_days": 30,
            "starting_cash": 100000,
            "analysis_cache_enabled": True
        },
        
        "logging": {
            "level": "INFO",
            "file_path": "logs/opro_system.log",
            "max_file_size_mb": 10,
            "backup_count": 5
        }
    }
    
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(default_config, f, indent=2, ensure_ascii=False)
        
        logger.info(f"✓ 默认配置文件已创建: {config_file}")
        return True
        
    except Exception as e:
        logger.error(f"✗ 创建配置文件失败: {e}")
        return False

def initialize_database():
    """初始化数据库"""
    logger = logging.getLogger(__name__)
    
    try:
        # 导入数据存储模块
        from data.comprehensive_storage_manager import ComprehensiveStorageManager
        
        # 加载配置
        with open('config/opro_config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 初始化存储管理器（这会自动创建数据库）
        storage_manager = ComprehensiveStorageManager(
            config=config.get("storage", {}),
            logger=logger
        )
        
        logger.info("✓ 数据库初始化成功")
        return True
        
    except Exception as e:
        logger.error(f"✗ 数据库初始化失败: {e}")
        return False

def run_basic_tests():
    """运行基本功能测试"""
    logger = logging.getLogger(__name__)
    
    try:
        # 导入必要模块
        from data.integrated_data_manager import IntegratedDataManager
        
        # 加载配置
        with open('config/opro_config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 初始化集成数据管理器
        data_manager = IntegratedDataManager(config=config, logger=logger)
        
        if not data_manager.enabled:
            logger.error("✗ 数据存储功能未启用")
            return False
        
        # 测试系统状态
        status = data_manager.get_system_status()
        if not status.get("components_initialized"):
            logger.error("✗ 组件初始化失败")
            return False
        
        # 测试备份功能
        backup_status = data_manager.get_backup_status()
        if not backup_status.get("enabled"):
            logger.warning("⚠ 备份功能未启用")
        
        # 测试报告生成
        report = data_manager.generate_comprehensive_report()
        if not report.get("enabled"):
            logger.error("✗ 报告生成功能异常")
            return False
        
        logger.info("✓ 基本功能测试通过")
        return True
        
    except Exception as e:
        logger.error(f"✗ 基本功能测试失败: {e}")
        return False

def check_disk_space():
    """检查磁盘空间"""
    logger = logging.getLogger(__name__)
    
    try:
        import shutil
        
        # 检查当前目录的可用空间
        total, used, free = shutil.disk_usage('.')
        free_gb = free / (1024**3)
        
        if free_gb < 2:
            logger.warning(f"⚠ 可用磁盘空间较少: {free_gb:.1f} GB")
            logger.warning("建议至少保留2GB空间用于数据存储")
        else:
            logger.info(f"✓ 磁盘空间充足: {free_gb:.1f} GB 可用")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ 检查磁盘空间失败: {e}")
        return False

def print_setup_summary():
    """打印设置摘要"""
    logger = logging.getLogger(__name__)
    
    logger.info("=" * 60)
    logger.info("数据存储系统设置完成")
    logger.info("=" * 60)
    logger.info("下一步操作:")
    logger.info("1. 运行演示: python examples/data_storage_demo.py")
    logger.info("2. 查看文档: docs/comprehensive_data_storage_guide.md")
    logger.info("3. 运行评估: python run_opro_system.py --mode evaluation")
    logger.info("4. 生成报告: python run_opro_system.py --data-report")
    logger.info("=" * 60)
    logger.info("配置文件位置: config/opro_config.json")
    logger.info("数据存储位置: data/")
    logger.info("日志文件位置: logs/")
    logger.info("=" * 60)

def main():
    """主安装函数"""
    logger = setup_logging()
    
    logger.info("开始设置OPRO数据存储系统...")
    
    # 检查步骤
    steps = [
        ("检查Python版本", check_python_version),
        ("检查磁盘空间", check_disk_space),
        ("安装依赖包", install_dependencies),
        ("创建目录结构", create_directory_structure),
        ("创建默认配置", create_default_config),
        ("初始化数据库", initialize_database),
        ("运行基本测试", run_basic_tests)
    ]
    
    failed_steps = []
    
    for step_name, step_func in steps:
        logger.info(f"执行步骤: {step_name}")
        try:
            if not step_func():
                failed_steps.append(step_name)
                logger.error(f"步骤失败: {step_name}")
            else:
                logger.info(f"步骤完成: {step_name}")
        except Exception as e:
            failed_steps.append(step_name)
            logger.error(f"步骤异常: {step_name} - {e}")
    
    # 总结结果
    if failed_steps:
        logger.error("=" * 60)
        logger.error("设置过程中遇到以下问题:")
        for step in failed_steps:
            logger.error(f"  ✗ {step}")
        logger.error("=" * 60)
        logger.error("请检查错误信息并重新运行设置脚本")
        return False
    else:
        print_setup_summary()
        return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
