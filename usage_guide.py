#!/usr/bin/env python3
"""
周期性Shapley值计算使用指南
"""

from contribution_assessment.trading_simulator import TradingSimulator
from contribution_assessment.analysis_cache import AnalysisCache

# 第一步：创建配置
config = {
    # 基础交易参数
    "ticker": "AAPL",
    "start_date": "2023-01-01", 
    "end_date": "2023-02-28",
    "initial_balance": 100000,
    
    # 启用周期性评估（关键配置）
    "weekly_evaluation_enabled": True,      # 开启周期性评估
    "trading_days_per_week": 5,            # 每周交易日数
    "risk_free_rate": 0.02,               # 无风险利率
}

# 第二步：初始化模拟器
simulator = TradingSimulator(config)

# 第三步：设置回调函数（可选）
def my_shapley_callback(coalition, week_data, all_weekly_data):
    print(f"第{week_data['week_number']}周完成")
    print(f"夏普比率: {week_data['sharpe_ratio']:.4f}")
    # 在这里添加你的Shapley值分析逻辑

simulator.set_weekly_shapley_callback(my_shapley_callback)

# 第四步：运行模拟
analysis_cache = AnalysisCache()
coalition = {"NAA", "TAA", "TRA"}  # 你的智能体组合

result = simulator.run_simulation_for_coalition(
    coalition=coalition,
    analysis_cache=analysis_cache,
    agents=your_agents_dict,  # 你的智能体实例字典
    simulation_days=30        # 模拟天数
)

print(f"最终结果: {result}")