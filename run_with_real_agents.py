#!/usr/bin/env python3
"""
使用真实LLM智能体运行贡献度评估

本脚本演示如何正确配置和运行贡献度评估系统，确保使用真实的LLM智能体
而不是模拟智能体。

使用前请确保：
1. 设置了正确的环境变量（如ZHIPUAI_API_KEY）
2. 安装了相应的LLM SDK（如zhipuai）

使用方法:
    python run_with_real_agents.py --llm-provider zhipuai
    python run_with_real_agents.py --llm-provider openai
"""

import os
import sys
import argparse
import logging
from typing import Dict, Any, Optional
from dotenv import load_dotenv

# 导入必要的模块
try:
    from contribution_assessment import ContributionAssessor
    from contribution_assessment.llm_interface import LLMInterface
    from agents.agent_factory import AgentFactory
except ImportError as e:
    print(f"❌ 导入模块失败: {e}", file=sys.stderr)
    print("请确保在项目根目录下运行此脚本", file=sys.stderr)
    sys.exit(1)


def setup_logging(verbose: bool = False) -> logging.Logger:
    """设置日志记录"""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)


def check_llm_environment(llm_provider: str) -> bool:
    """
    检查LLM环境配置
    
    参数:
        llm_provider: LLM提供商名称
        
    返回:
        是否配置正确
    """
    if llm_provider == "zhipuai":
        api_key = os.environ.get("ZHIPUAI_API_KEY")
        if not api_key:
            print("❌ 未找到 ZHIPUAI_API_KEY 环境变量")
            print("请设置: export ZHIPUAI_API_KEY=your_api_key")
            return False
        
        try:
            import zhipuai
            print("✅ ZhipuAI SDK 已安装")
        except ImportError:
            print("❌ ZhipuAI SDK 未安装")
            print("请安装: pip install zhipuai")
            return False
            
    elif llm_provider == "openai":
        api_key = os.environ.get("OPENAI_API_KEY")
        if not api_key:
            print("❌ 未找到 OPENAI_API_KEY 环境变量")
            print("请设置: export OPENAI_API_KEY=your_api_key")
            return False
        
        try:
            import openai
            print("✅ OpenAI SDK 已安装")
        except ImportError:
            print("❌ OpenAI SDK 未安装")
            print("请安装: pip install openai")
            return False
    else:
        print(f"❌ 不支持的LLM提供商: {llm_provider}")
        return False
    
    return True


def create_real_agents(llm_provider: str, logger: logging.Logger) -> Dict[str, Any]:
    """
    创建真实的LLM智能体实例
    
    参数:
        llm_provider: LLM提供商名称
        logger: 日志记录器
        
    返回:
        智能体实例字典
    """
    logger.info(f"正在创建LLM智能体实例 (提供商: {llm_provider})")
    
    # 创建LLM接口
    llm_interface = LLMInterface(provider=llm_provider, logger=logger)
    
    # 检查LLM接口是否成功初始化
    if not llm_interface.client:
        logger.error("LLM接口初始化失败")
        return {}
    
    logger.info("✅ LLM接口初始化成功")
    
    # 创建智能体工厂
    agent_factory = AgentFactory(llm_interface=llm_interface, logger=logger)
    
    # 创建所有智能体
    agent_instances = agent_factory.create_all_agents()
    
    if agent_instances:
        logger.info(f"✅ 成功创建 {len(agent_instances)} 个智能体实例")
        for agent_id in agent_instances.keys():
            logger.info(f"  - {agent_id}: {type(agent_instances[agent_id]).__name__}")
    else:
        logger.error("❌ 未能创建任何智能体实例")
    
    return agent_instances


def create_test_config() -> Dict[str, Any]:
    """创建测试配置"""
    return {
        "start_date": "2023-01-01",
        "end_date": "2023-01-10",  # 短期测试
        "stocks": ["AAPL"],
        "starting_cash": 100000,
        "risk_free_rate": 0.02,
        "simulation_days": 5,  # 短期模拟
        "fail_on_large_gaps": False,
        "fill_date_gaps": True,
        "verbose": True
    }


def run_assessment_with_real_agents(llm_provider: str, verbose: bool = False) -> Dict[str, Any]:
    """
    使用真实智能体运行贡献度评估
    
    参数:
        llm_provider: LLM提供商名称
        verbose: 是否显示详细日志
        
    返回:
        评估结果
    """
    logger = setup_logging(verbose)
    
    # 检查环境配置
    if not check_llm_environment(llm_provider):
        raise RuntimeError("LLM环境配置不正确")
    
    # 创建真实智能体实例
    agent_instances = create_real_agents(llm_provider, logger)
    
    if not agent_instances:
        raise RuntimeError("无法创建智能体实例")
    
    # 创建配置
    config = create_test_config()
    
    # 创建评估器，明确传入智能体实例和LLM提供商
    assessor = ContributionAssessor(
        config=config,
        agents=agent_instances,  # 关键：传入真实智能体实例
        logger=logger,
        llm_provider=llm_provider  # 关键：传入LLM提供商
    )
    
    # 运行评估，再次确保传入智能体实例
    logger.info("🚀 开始运行贡献度评估...")
    result = assessor.run(
        agents=agent_instances,  # 关键：再次传入智能体实例
        target_agents=["NAA", "TAA", "FAA"],  # 先测试分析层智能体
        max_coalitions=3  # 限制联盟数量以加快测试
    )
    
    return result


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="使用真实LLM智能体运行贡献度评估")
    parser.add_argument(
        "--llm-provider", 
        type=str, 
        required=True,
        choices=["zhipuai", "openai"],
        help="LLM提供商 (zhipuai 或 openai)"
    )
    parser.add_argument(
        "--verbose", 
        action="store_true",
        help="显示详细日志"
    )
    
    args = parser.parse_args()
    
    # 加载环境变量
    load_dotenv()
    
    try:
        print(f"🤖 使用 {args.llm_provider} 运行贡献度评估")
        print("=" * 60)
        
        result = run_assessment_with_real_agents(args.llm_provider, args.verbose)
        
        print("\n" + "=" * 60)
        print("📊 评估结果:")
        print("=" * 60)
        
        # 检查是否使用了真实智能体
        if result.get("cache_result", {}).get("method") == "mock_data":
            print("⚠️  警告: 系统仍在使用模拟数据!")
        else:
            print("✅ 成功使用真实LLM智能体!")
        
        # 显示Shapley值结果
        shapley_values = result.get("shapley_values", {})
        if shapley_values:
            print("\n🎯 Shapley值结果:")
            for agent, value in shapley_values.items():
                print(f"  {agent}: {value:.6f}")
        
        # 显示执行统计
        print(f"\n⏱️  执行时间: {result.get('total_execution_time', 0):.2f}秒")
        print(f"✅ 评估完成!")
        
    except Exception as e:
        print(f"❌ 评估失败: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
