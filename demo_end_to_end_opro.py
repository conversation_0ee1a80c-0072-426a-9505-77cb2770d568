#!/usr/bin/env python3
"""
端到端OPRO系统演示脚本 (End-to-End OPRO System Demo)

本脚本演示完整的OPRO优化流程，包括：
1. 数据准备和LLM智能体创建
2. 交易模拟和Shapley值计算
3. OPRO提示词优化
4. 结果验证和报告生成

使用方法:
    python demo_end_to_end_opro.py --provider zhipuai
"""

import argparse
import logging
import os
import sys
import time
from datetime import datetime
from typing import Dict, Any

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from contribution_assessment.assessor import ContributionAssessor
from contribution_assessment.llm_interface import LLMInterface

def setup_logging(verbose: bool = False) -> logging.Logger:
    """设置日志记录"""
    log_level = logging.DEBUG if verbose else logging.INFO
    
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(f'demo_opro_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
        ]
    )
    
    return logging.getLogger(__name__)

def load_config() -> Dict[str, Any]:
    """加载演示配置"""
    return {
        "start_date": "2023-01-01",
        "end_date": "2023-01-05",
        "stocks": ["AAPL"],
        "starting_cash": 1000000,
        "simulation_days": 3,
        "verbose": True,
        "enable_concurrent_execution": True
    }

def demonstrate_llm_trading_simulation(assessor: ContributionAssessor, logger: logging.Logger):
    """演示LLM交易模拟功能"""
    logger.info("=" * 80)
    logger.info("🔹 步骤1: LLM交易模拟演示")
    logger.info("=" * 80)
    
    logger.info("📊 运行快速评估以验证LLM交易模拟...")
    start_time = time.time()
    
    result = assessor.run_quick_test()
    
    if result.get("success", False):
        shapley_values = result.get("shapley_values", {})
        execution_time = result.get("execution_time", 0)
        
        logger.info(f"✅ LLM交易模拟成功完成 (耗时: {execution_time:.2f}s)")
        logger.info(f"📈 计算出 {len(shapley_values)} 个智能体的Shapley值:")
        
        for agent_id, score in shapley_values.items():
            logger.info(f"   📌 {agent_id}: {score:.6f}")
        
        return True
    else:
        logger.error(f"❌ LLM交易模拟失败: {result.get('error', '未知错误')}")
        return False

def demonstrate_subset_calculation(assessor: ContributionAssessor, logger: logging.Logger):
    """演示子集计算功能"""
    logger.info("=" * 80)
    logger.info("🔹 步骤2: 子集计算演示 (N+1方法)")
    logger.info("=" * 80)
    
    logger.info("🎯 使用部分智能体进行子集计算...")
    target_agents = ["NAA", "TAA", "TRA"]  # 新闻分析师、技术分析师、交易员
    
    start_time = time.time()
    
    result = assessor.run(
        target_agents=target_agents,
        max_coalitions=10  # 限制联盟数量以加快演示
    )
    
    if result.get("success", False):
        shapley_values = result.get("shapley_values", {})
        phase_results = result.get("phase_results", {})
        
        logger.info(f"✅ 子集计算成功完成")
        logger.info(f"📊 各阶段执行情况:")
        
        for phase, phase_result in phase_results.items():
            if isinstance(phase_result, dict):
                success = phase_result.get("success", False)
                time_taken = phase_result.get("execution_time", 0)
                status = "✅" if success else "❌"
                logger.info(f"   {status} {phase}: {time_taken:.2f}s")
        
        logger.info(f"📈 最终Shapley值:")
        for agent_id, score in shapley_values.items():
            logger.info(f"   📌 {agent_id}: {score:.6f}")
        
        return True, shapley_values
    else:
        logger.error(f"❌ 子集计算失败: {result.get('error', '未知错误')}")
        return False, {}

def demonstrate_opro_optimization(assessor: ContributionAssessor, logger: logging.Logger):
    """演示OPRO优化功能"""
    logger.info("=" * 80)
    logger.info("🔹 步骤3: OPRO智能提示词优化演示")
    logger.info("=" * 80)
    
    if not assessor.enable_opro:
        logger.error("❌ OPRO功能未启用，无法演示优化")
        return False
    
    logger.info("🚀 开始OPRO优化循环...")
    target_agents = ["TAA", "TRA"]  # 选择技术分析师和交易员进行优化
    
    start_time = time.time()
    
    result = assessor.run_opro_optimization_cycle(
        target_agents=target_agents,
        force_optimization=True
    )
    
    if result.get("success", False):
        optimization_result = result.get("optimization_result", {})
        successful_optimizations = optimization_result.get("successful_optimizations", 0)
        total_agents = optimization_result.get("total_agents", 0)
        total_time = optimization_result.get("total_time", 0)
        
        logger.info(f"✅ OPRO优化成功完成 (耗时: {total_time:.2f}s)")
        logger.info(f"📊 优化统计: {successful_optimizations}/{total_agents} 智能体优化成功")
        
        results = optimization_result.get("results", {})
        for agent_id, agent_result in results.items():
            if agent_result.get("success", False):
                improvement = agent_result.get("improvement", 0)
                logger.info(f"   🎯 {agent_id}: 预期改进 {improvement:.6f}")
            else:
                error = agent_result.get("error", "未知错误")
                logger.info(f"   ❌ {agent_id}: 优化失败 - {error}")
        
        return True
    else:
        logger.error(f"❌ OPRO优化失败: {result.get('error', '未知错误')}")
        return False

def demonstrate_returns_analysis(assessor: ContributionAssessor, logger: logging.Logger, shapley_values: Dict[str, float]):
    """演示收益率分析功能"""
    logger.info("=" * 80)
    logger.info("🔹 步骤3: 收益率分析演示")
    logger.info("=" * 80)
    
    logger.info("📊 使用已有的评估结果进行收益率分析...")
    
    # 运行一个简化的评估以获取收益率数据
    target_agents = ["NAA", "TAA", "TRA"]
    
    start_time = time.time()
    
    result = assessor.run(
        target_agents=target_agents,
        max_coalitions=8  # 限制联盟数量
    )
    
    if result.get("success", False):
        returns_analysis = result.get("returns_analysis", {})
        
        if returns_analysis.get("analysis_completed", False):
            logger.info("✅ 收益率分析成功完成")
            
            # 显示分析结果
            total_coalitions = returns_analysis.get("total_coalitions_analyzed", 0)
            logger.info(f"📊 分析结果:")
            logger.info(f"   分析的联盟数量: {total_coalitions}")
            
            # 最佳表现联盟
            best_coalition = returns_analysis.get("best_coalition", {})
            if best_coalition:
                coalition_id = best_coalition.get("coalition_id", "N/A")
                sharpe_ratio = best_coalition.get("sharpe_ratio", 0)
                annual_return = best_coalition.get("annualized_return", 0)
                
                logger.info(f"   🏆 最佳联盟: {coalition_id}")
                logger.info(f"   📈 夏普比率: {sharpe_ratio:.4f}")
                logger.info(f"   💰 年化收益: {annual_return:.2%}")
            
            # 图表生成情况
            plot_paths = returns_analysis.get("plot_paths", {})
            if plot_paths:
                logger.info(f"   📊 生成图表数量: {len(plot_paths)}")
                for plot_type, path in plot_paths.items():
                    if path:
                        logger.info(f"      📈 {plot_type}: {path}")
            
            # 性能报告
            report_path = returns_analysis.get("performance_report_path", "")
            if report_path:
                logger.info(f"   📄 性能报告: {report_path}")
            
            # 整体统计
            summary_stats = returns_analysis.get("summary_statistics", {})
            logger.info(f"   📊 整体统计:")
            logger.info(f"      平均夏普比率: {summary_stats.get('avg_sharpe_ratio', 0):.4f}")
            logger.info(f"      策略多样化: {summary_stats.get('strategy_diversification', 0):.4f}")
            
            return True
        else:
            logger.error(f"❌ 收益率分析失败: {returns_analysis.get('error', '未知错误')}")
            return False
    else:
        logger.error(f"❌ 评估失败: {result.get('error', '未知错误')}")
        return False

def demonstrate_integrated_workflow(assessor: ContributionAssessor, logger: logging.Logger):
    """演示完整集成工作流"""
    logger.info("=" * 80)
    logger.info("🔹 步骤4: 完整集成工作流演示")
    logger.info("=" * 80)
    
    if not assessor.enable_opro:
        logger.error("❌ OPRO功能未启用，无法演示集成工作流")
        return False
    
    logger.info("🔄 运行完整的评估+优化集成流程...")
    target_agents = ["NAA", "TAA", "TRA"]
    
    start_time = time.time()
    
    result = assessor.run_with_opro_integration(
        target_agents=target_agents,
        max_coalitions=8,  # 限制联盟数量
        run_optimization_before=False,  # 不进行评估前优化
        run_optimization_after=True   # 进行评估后优化
    )
    
    if result.get("success", False):
        total_time = result.get("total_execution_time", 0)
        
        logger.info(f"✅ 完整集成工作流成功完成 (耗时: {total_time:.2f}s)")
        
        # 分析评估结果
        evaluation_result = result.get("evaluation_result", {})
        if evaluation_result.get("success", False):
            shapley_values = evaluation_result.get("shapley_values", {})
            logger.info(f"📊 最终评估结果:")
            for agent_id, score in shapley_values.items():
                logger.info(f"   📌 {agent_id}: {score:.6f}")
        
        # 分析优化结果
        optimization_results = result.get("optimization_results", {})
        post_opt = optimization_results.get("post_evaluation", {})
        if post_opt.get("success", False):
            updated_agents = post_opt.get("updated_agents", [])
            logger.info(f"🔧 优化结果: {len(updated_agents)} 个智能体获得提示词更新")
            for agent_id in updated_agents:
                logger.info(f"   🎯 {agent_id}: 提示词已优化")
        
        return True
    else:
        logger.error(f"❌ 完整集成工作流失败: {result.get('error', '未知错误')}")
        return False

def generate_final_report(assessor: ContributionAssessor, logger: logging.Logger, demo_results: Dict[str, bool]):
    """生成最终演示报告"""
    logger.info("=" * 80)
    logger.info("🔹 最终演示报告")
    logger.info("=" * 80)
    
    # 统计演示结果
    total_demos = len(demo_results)
    successful_demos = sum(1 for success in demo_results.values() if success)
    
    logger.info(f"📊 演示统计:")
    logger.info(f"   总演示项目: {total_demos}")
    logger.info(f"   成功项目: {successful_demos}")
    logger.info(f"   成功率: {(successful_demos/total_demos)*100:.1f}%")
    
    logger.info(f"")
    logger.info(f"📝 各项演示结果:")
    for demo_name, success in demo_results.items():
        status = "✅ 成功" if success else "❌ 失败"
        logger.info(f"   {status} {demo_name}")
    
    # OPRO系统状态
    if assessor.enable_opro:
        logger.info(f"")
        logger.info(f"🔧 OPRO系统状态:")
        dashboard_data = assessor.get_opro_dashboard_data()
        
        system_stats = dashboard_data.get("system_stats", {})
        if "optimizer" in system_stats:
            opt_stats = system_stats["optimizer"]
            logger.info(f"   优化器统计:")
            logger.info(f"     总优化次数: {opt_stats.get('total_optimizations', 0)}")
            logger.info(f"     成功优化次数: {opt_stats.get('successful_optimizations', 0)}")
            logger.info(f"     成功率: {opt_stats.get('success_rate', 0):.1f}%")
    
    # 建议后续步骤
    logger.info(f"")
    logger.info(f"🎯 建议后续步骤:")
    if successful_demos == total_demos:
        logger.info(f"   🎉 所有演示成功！系统已准备就绪用于生产环境")
        logger.info(f"   💡 可以开始运行完整的OPRO优化周期")
        logger.info(f"   📈 可以启用自动化的定期优化任务")
    else:
        logger.info(f"   🔧 部分演示失败，需要检查和修复相关组件")
        logger.info(f"   📋 建议查看详细日志分析失败原因")
    
    return successful_demos == total_demos

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="端到端OPRO系统演示脚本")
    
    parser.add_argument(
        "--provider",
        type=str,
        default="zhipuai",
        choices=["zhipuai", "openai"],
        help="LLM提供商"
    )
    
    parser.add_argument(
        "--enable-opro",
        action="store_true",
        default=True,
        help="启用OPRO功能"
    )
    
    parser.add_argument(
        "--verbose",
        action="store_true",
        help="详细日志输出"
    )
    
    args = parser.parse_args()
    
    # 设置日志
    logger = setup_logging(args.verbose)
    
    logger.info("🚀" * 30)
    logger.info("端到端OPRO系统演示开始")
    logger.info("🚀" * 30)
    logger.info(f"LLM提供商: {args.provider}")
    logger.info(f"OPRO启用: {args.enable_opro}")
    logger.info(f"演示时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info("")
    
    try:
        # 初始化系统
        logger.info("🔧 初始化系统...")
        config = load_config()
        
        # 创建LLM接口
        llm_interface = LLMInterface(provider=args.provider, logger=logger)
        
        # 创建评估器
        assessor = ContributionAssessor(
            config=config,
            logger=logger,
            llm_provider=args.provider,
            enable_opro=args.enable_opro,
            opro_config={}
        )
        
        logger.info("✅ 系统初始化完成")
        logger.info("")
        
        # 开始演示
        demo_results = {}
        
        # 演示1: LLM交易模拟
        demo_results["LLM交易模拟"] = demonstrate_llm_trading_simulation(assessor, logger)
        
        # 演示2: 子集计算
        subset_success, shapley_values = demonstrate_subset_calculation(assessor, logger)
        demo_results["子集计算"] = subset_success
        
        # 演示3: 收益率分析
        demo_results["收益率分析"] = demonstrate_returns_analysis(assessor, logger, shapley_values)
        
        # 演示4: OPRO优化 (仅当启用时)
        if args.enable_opro:
            demo_results["OPRO优化"] = demonstrate_opro_optimization(assessor, logger)
            
            # 演示5: 完整集成工作流
            demo_results["完整集成工作流"] = demonstrate_integrated_workflow(assessor, logger)
        else:
            logger.info("⚠️ OPRO功能未启用，跳过OPRO相关演示")
        
        # 生成最终报告
        success = generate_final_report(assessor, logger, demo_results)
        
        logger.info("")
        logger.info("🏁" * 30)
        if success:
            logger.info("🎉 端到端OPRO系统演示成功完成！")
        else:
            logger.info("❌ 端到端OPRO系统演示部分失败！")
        logger.info("🏁" * 30)
        
        return 0 if success else 1
        
    except Exception as e:
        logger.error(f"💥 演示过程中发生异常: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())