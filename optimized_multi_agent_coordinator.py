"""
优化的多智能体协调器

基于有向图特性和数据可用性，智能地减少不必要的LLM调用，提升执行效率
同时支持Myerson值计算和POMDP优化
"""
import os
import json
import time
from datetime import datetime
from typing import Dict, List, Any, Set, Tuple, Optional
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import threading
import networkx as nx

from multi_agent_coordinator import MultiAgentCoordinator
from config import AGENT_GRAPH, AGENT_TYPES
from integration_myerson_reflection import MyersonPOMDPIntegration

class OptimizedMultiAgentCoordinator(MultiAgentCoordinator):
    """
    优化的多智能体协调器
    
    主要优化策略：
    1. 数据预检查 - 检查必要数据是否存在
    2. 分层执行 - 按依赖关系分层，失败则提前终止
    3. 智能跳过 - 根据数据可用性跳过某些智能体
    4. 并行优化 - 同层智能体并行执行
    5. 早期终止 - 关键路径失败时提前终止
    6. <PERSON>on值计算 - 可选的完整子集评估
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.verbose = config.get('verbose', False)  # 确保verbose属性被设置
        self.execution_stats = {
            "total_agents": len(AGENT_GRAPH),
            "executed_agents": 0,
            "skipped_agents": 0,
            "failed_agents": 0,
            "execution_time": 0,
            "llm_calls_saved": 0,
            "myerson_calculation_time": 0,
            "myerson_subset_evaluations": 0
        }
        
        # Myerson值计算配置
        self.enable_myerson_calculation = config.get('enable_myerson_calculation', False)
        self.myerson_integration = None
        
        # 智能体输出缓存（用于Myerson计算优化）
        self.agent_output_cache = {}
        self.cache_hit_count = 0
        self.cache_miss_count = 0
        
        # 初始化Myerson-POMDP集成（如果启用）
        if self.enable_myerson_calculation:
            self._init_myerson_integration()
    
    def _init_myerson_integration(self):
        """初始化Myerson-POMDP集成"""
        try:
            ticker = self.config.get('stocks', ['AAPL'])[0]  # 获取第一个股票代码
            self.myerson_integration = MyersonPOMDPIntegration(
                run_id=self.run_id,
                ticker=ticker,
                agents=self.agents,
                env=self.env,
                verbose=self.verbose
            )
            
            # 初始化POMDP优化器
            self.myerson_integration.initialize_optimizers()
            
            if self.verbose:
                print(f"✅ Myerson-POMDP集成已初始化，股票: {ticker}")
                
        except Exception as e:
            print(f"⚠️  Myerson-POMDP集成初始化失败: {e}")
            self.enable_myerson_calculation = False
    
    def _check_data_availability(self, state: Dict[str, Any]) -> Dict[str, bool]:
        """
        检查各类数据的可用性
        
        返回:
            数据可用性字典
        """
        availability = {
            "price_data": False,
            "news_data": False,
            "fundamental_data": False
        }
        
        # 检查价格数据
        price_history = state.get("price_history", {})
        if price_history:
            for ticker_data in price_history.values():
                if ticker_data and len(ticker_data) > 0:
                    availability["price_data"] = True
                    break
        
        # 检查新闻数据
        news_history = state.get("news_history", {})
        if news_history:
            for date_news in news_history.values():
                if date_news:
                    for ticker_news in date_news.values():
                        if ticker_news and len(ticker_news) > 0:
                            availability["news_data"] = True
                            break
                if availability["news_data"]:
                    break
        
        # 检查基本面数据
        fundamental_data = state.get("fundamental_data", {})
        if fundamental_data:
            for ticker_data in fundamental_data.values():
                if ticker_data and len(ticker_data) > 0:
                    availability["fundamental_data"] = True
                    break
        
        return availability
    
    def _get_agent_layers(self) -> List[List[str]]:
        """
        根据依赖关系图计算智能体的执行层级
        基础分析智能体（NAA、TAA、FAA）在第0层，依次向下
        
        返回:
            按层级分组的智能体列表
        """
        # 手动定义层级，基于信息流方向
        layer_0 = ["NAA", "TAA", "FAA"]  # 基础分析层
        layer_1 = ["BOA", "BeOA", "NOA"] # 展望分析层 (原layer_2提升为layer_1)
        layer_2 = ["TRA"]                # 交易决策层 (原layer_3变为layer_2)
        
        return [layer_0, layer_1, layer_2]
    
    def _should_skip_agent(self, agent_id: str, data_availability: Dict[str, bool]) -> Tuple[bool, str]:
        """
        基于数据可用性判断是否应该跳过某个智能体
        
        参数:
            agent_id: 智能体ID
            data_availability: 数据可用性字典
            
        返回:
            (是否跳过, 跳过原因)
        """
        skip_rules = {
            "NAA": ("news_data", "新闻数据不可用"),
            "TAA": ("price_data", "价格数据不可用"), 
            "FAA": ("fundamental_data", "基本面数据不可用"),
        }
        
        if agent_id in skip_rules:
            required_data, reason = skip_rules[agent_id]
            if not data_availability.get(required_data, False):
                return True, reason
        
        return False, ""
    
    def _execute_agent_layer_parallel(self, layer_agents: List[str], state: Dict[str, Any], 
                                    data_availability: Dict[str, bool]) -> Dict[str, Any]:
        """
        并行执行一层的智能体
        
        参数:
            layer_agents: 当前层的智能体列表
            state: 当前状态
            data_availability: 数据可用性
            
        返回:
            智能体输出字典
        """
        results = {}
        
        # 检查哪些智能体需要跳过
        agents_to_execute = []
        for agent_id in layer_agents:
            should_skip, skip_reason = self._should_skip_agent(agent_id, data_availability)
            if should_skip:
                if self.verbose:
                    print(f"⏭️  跳过智能体 {agent_id}: {skip_reason}")
                results[agent_id] = None
                self.execution_stats["skipped_agents"] += 1
                self.execution_stats["llm_calls_saved"] += 1
            else:
                agents_to_execute.append(agent_id)
        
        if not agents_to_execute:
            return results
        
        # 并行执行需要执行的智能体
        if len(agents_to_execute) == 1:
            # 单个智能体直接执行
            agent_id = agents_to_execute[0]
            if self.verbose:
                print(f"🔄 执行智能体 {agent_id}...")
            
            try:
                start_time = time.time()
                result = self.agents[agent_id].process(state)
                execution_time = time.time() - start_time
                
                results[agent_id] = result
                self.execution_stats["executed_agents"] += 1
                
                if self.verbose:
                    print(f"✅ 智能体 {agent_id} 执行完成 ({execution_time:.2f}s)")
                    
            except Exception as e:
                if self.verbose:
                    print(f"❌ 智能体 {agent_id} 执行失败: {e}")
                results[agent_id] = None
                self.execution_stats["failed_agents"] += 1
        else:
            # 多个智能体并行执行
            if self.verbose:
                print(f"🔄 并行执行 {len(agents_to_execute)} 个智能体: {', '.join(agents_to_execute)}")
            
            with ThreadPoolExecutor(max_workers=min(len(agents_to_execute), 4)) as executor:
                # 提交所有任务
                future_to_agent = {}
                for agent_id in agents_to_execute:
                    future = executor.submit(self._execute_single_agent, agent_id, state.copy())
                    future_to_agent[future] = agent_id
                
                # 收集结果
                for future in as_completed(future_to_agent):
                    agent_id = future_to_agent[future]
                    try:
                        result = future.result()
                        results[agent_id] = result
                        self.execution_stats["executed_agents"] += 1
                        
                        if self.verbose:
                            print(f"✅ 智能体 {agent_id} 并行执行完成")
                            
                    except Exception as e:
                        if self.verbose:
                            print(f"❌ 智能体 {agent_id} 并行执行失败: {e}")
                        results[agent_id] = None
                        self.execution_stats["failed_agents"] += 1
        
        return results
    
    def _execute_single_agent(self, agent_id: str, state: Dict[str, Any]) -> Any:
        """
        执行单个智能体
        
        参数:
            agent_id: 智能体ID
            state: 输入状态
            
        返回:
            智能体输出
        """
        if agent_id not in self.agents:
            raise ValueError(f"智能体 {agent_id} 不存在")
        
        return self.agents[agent_id].process(state)
    
    def _check_critical_path_viability(self, layer_results: Dict[str, Any], layer_idx: int) -> bool:
        """
        检查关键路径是否可行
        
        如果关键智能体失败，后续执行就没有意义了
        
        参数:
            layer_results: 当前层的执行结果
            layer_idx: 当前层索引
            
        返回:
            是否继续执行
        """
        # 第0层：基础分析层（NAA、TAA、FAA）
        if layer_idx == 0:
            successful_agents = [aid for aid, result in layer_results.items() if result is not None]
            if len(successful_agents) == 0:
                if self.verbose:
                    print("⚠️  所有基础分析智能体都失败，终止执行")
                return False
        
        # 第1层：展望分析层（BOA、BeOA、NOA） - 原layer_2提升为layer_1
        elif layer_idx == 1:
            if (layer_results.get("BOA") is None and 
                layer_results.get("BeOA") is None and 
                layer_results.get("NOA") is None):
                if self.verbose:
                    print("⚠️  所有展望智能体都失败，终止执行")
                return False
        
        # 第2层：交易决策层（TRA） - 原layer_3变为layer_2
        # 对于TRA这一层，通常不在此处进行关键路径检查，因为它是最终决策者
        # 如果TRA失败，会在_run_day_optimized的后续逻辑中处理（使用默认动作）
        
        return True
    
    def _run_day_optimized(self) -> Dict[str, Any]:
        """
        优化的单日执行逻辑
        
        返回:
            执行结果字典
        """
        start_time = time.time()
        
        # 初始化效率变量，确保在任何情况下都有值
        efficiency = 0.0
        all_results = {}
        myerson_results = None
        
        try:
            # 获取当前状态
            state, info = self.env.reset() if hasattr(self, 'env') else ({}, {})
            
            # 1. 数据可用性检查
            data_availability = self._check_data_availability(state)
            
            if self.verbose:
                print(f"\n📊 数据可用性检查:")
                for data_type, available in data_availability.items():
                    status = "✅" if available else "❌"
                    print(f"  {status} {data_type}: {'可用' if available else '不可用'}")
            
            # 2. 如果没有任何关键数据，直接返回
            if not any(data_availability.values()):
                if self.verbose:
                    print("⚠️  没有任何可用数据，跳过智能体执行")
                # 计算效率（全部智能体被跳过）
                efficiency = (self.execution_stats['total_agents'] / self.execution_stats['total_agents']) * 100 if self.execution_stats['total_agents'] > 0 else 0.0
                return {
                    "actions": {"AAPL": 0.0},  # 默认持有
                    "agent_outputs": {},
                    "execution_stats": self.execution_stats,
                    "early_termination": True,
                    "termination_reason": "没有可用数据",
                    "efficiency": efficiency
                }
            
            # 3. 获取执行层级
            layers = self._get_agent_layers()
            
            if self.verbose:
                print(f"\n🔄 执行层级:")
                for i, layer in enumerate(layers):
                    print(f"  层级 {i}: {', '.join(layer)}")
            
            # 4. 分层执行智能体
            for layer_idx, layer_agents in enumerate(layers):
                if self.verbose:
                    print(f"\n🚀 执行层级 {layer_idx}: {', '.join(layer_agents)}")
                
                # 执行当前层
                layer_results = self._execute_agent_layer_parallel(layer_agents, state, data_availability)
                all_results.update(layer_results)
                
                # 更新状态，传递给下一层
                for agent_id, result in layer_results.items():
                    if result is not None:
                        state[f"{agent_id}_output"] = result
                
                # 检查关键路径是否可行
                if not self._check_critical_path_viability(layer_results, layer_idx):
                    # 计算剩余未执行的智能体数量
                    remaining_agents = []
                    for remaining_layer in layers[layer_idx + 1:]:
                        remaining_agents.extend(remaining_layer)
                    
                    self.execution_stats["skipped_agents"] += len(remaining_agents)
                    self.execution_stats["llm_calls_saved"] += len(remaining_agents)
                    
                    if self.verbose:
                        print(f"⏭️  提前终止，跳过剩余 {len(remaining_agents)} 个智能体")
                    
                    # 计算当前效率
                    efficiency = (self.execution_stats['llm_calls_saved'] / self.execution_stats['total_agents']) * 100 if self.execution_stats['total_agents'] > 0 else 0.0
                    
                    return {
                        "actions": {"AAPL": 0.0},  # 默认持有
                        "agent_outputs": all_results,
                        "execution_stats": self.execution_stats,
                        "early_termination": True,
                        "termination_reason": "关键路径失败",
                        "efficiency": efficiency
                    }
            
            # 5. 提取交易动作
            trader_result = all_results.get("TRA")
            
            if trader_result is None:
                if self.verbose:
                    print("⚠️  交易者智能体执行失败，使用默认动作")
                actions = {"AAPL": 0.0}  # 默认持有
            else:
                # 解析交易者的输出
                try:
                    if isinstance(trader_result, dict):
                        trading_decisions = trader_result.get("trading_decisions", [])
                        actions = {}
                        
                        for decision in trading_decisions:
                            ticker = decision.get("ticker", "AAPL")
                            action_type = decision.get("action", "hold").lower()
                            
                            if action_type == "buy":
                                actions[ticker] = 0.5  # 买入50%
                            elif action_type == "sell":
                                actions[ticker] = -0.5  # 卖出50%
                            else:
                                actions[ticker] = 0.0  # 持有
                    else:
                        actions = {"AAPL": 0.0}
                except Exception as e:
                    if self.verbose:
                        print(f"解析交易决策失败: {e}")
                    actions = {"AAPL": 0.0}
            
            # 6. Myerson值计算（如果启用）
            if self.enable_myerson_calculation and self.myerson_integration:
                myerson_results = self._calculate_myerson_values_optimized(state, all_results, actions)
            
            # 7. 记录执行统计
            self.execution_stats["execution_time"] = time.time() - start_time
            
            if self.verbose:
                print(f"\n📈 执行统计:")
                print(f"  总智能体数: {self.execution_stats['total_agents']}")
                print(f"  执行智能体数: {self.execution_stats['executed_agents']}")
                print(f"  跳过智能体数: {self.execution_stats['skipped_agents']}")
                print(f"  失败智能体数: {self.execution_stats['failed_agents']}")
                print(f"  节省LLM调用: {self.execution_stats['llm_calls_saved']}")
                print(f"  执行时间: {self.execution_stats['execution_time']:.2f}s")
                if self.enable_myerson_calculation:
                    print(f"  Myerson计算时间: {self.execution_stats['myerson_calculation_time']:.2f}s")
                    print(f"  Myerson子集评估: {self.execution_stats['myerson_subset_evaluations']}")
                efficiency = (self.execution_stats['llm_calls_saved'] / self.execution_stats['total_agents']) * 100
                print(f"  效率提升: {efficiency:.1f}%")
            
            # 计算最终效率
            efficiency = (self.execution_stats['llm_calls_saved'] / self.execution_stats['total_agents']) * 100 if self.execution_stats['total_agents'] > 0 else 0.0
            
            return {
                "actions": actions,
                "agent_outputs": all_results,
                "execution_stats": self.execution_stats,
                "early_termination": False,
                "myerson_results": myerson_results,
                "efficiency": efficiency
            }
            
        except Exception as e:
            if self.verbose:
                print(f"❌ _run_day_optimized 执行失败: {e}")
                import traceback
                traceback.print_exc()
            
            # 在异常情况下也返回一个包含 efficiency 的字典
            self.execution_stats["execution_time"] = time.time() - start_time
            efficiency = (self.execution_stats['llm_calls_saved'] / self.execution_stats['total_agents']) * 100 if self.execution_stats['total_agents'] > 0 else 0.0
            
            return {
                "actions": {"AAPL": 0.0},  # 默认持有
                "agent_outputs": all_results,  # 返回已有的部分结果
                "execution_stats": self.execution_stats,
                "early_termination": True,
                "termination_reason": f"内部错误: {e}",
                "efficiency": efficiency
            }
    
    def _calculate_myerson_values_optimized(self, state: Dict[str, Any], 
                                          agent_outputs: Dict[str, Any], 
                                          actions: Dict[str, float]) -> Dict[str, Any]:
        """
        使用优化的方法计算Myerson值
        包含智能体输出缓存优化，避免重复LLM调用
        
        参数:
            state: 当前状态
            agent_outputs: 智能体输出
            actions: 交易动作
            
        返回:
            Myerson值计算结果
        """
        if not self.myerson_integration:
            return None
        
        try:
            myerson_start_time = time.time()
            
            if self.verbose:
                print(f"\n🧮 开始优化的Myerson值计算...")
                print(f"💾 使用智能体输出缓存减少LLM调用")
            
            # 步骤1：缓存所有智能体的输出（如果还没有缓存）
            if not self.agent_output_cache:
                if self.verbose:
                    print(f"🔄 第一次运行，缓存所有智能体输出...")
                
                self.agent_output_cache = agent_outputs.copy()
                
                # 统计缓存信息
                cached_agents = [aid for aid, output in self.agent_output_cache.items() if output is not None]
                if self.verbose:
                    print(f"✅ 已缓存 {len(cached_agents)} 个智能体的输出: {', '.join(cached_agents)}")
            
            # 步骤2：为MyersonPOMDPIntegration创建缓存优化的特征函数
            def cached_subset_return_calculator(agent_subset: Set[str]) -> float:
                """
                使用缓存的智能体输出计算子集收益
                避免重复的LLM调用
                """
                # 构建当前子集的状态
                subset_state = state.copy()
                
                # 对于每个智能体，如果在子集中使用缓存输出，否则使用None
                for agent_id in AGENT_GRAPH.keys():
                    if agent_id in agent_subset:
                        # 智能体在子集中，使用缓存的输出
                        if agent_id in self.agent_output_cache:
                            subset_state[f"{agent_id}_output"] = self.agent_output_cache[agent_id]
                            self.cache_hit_count += 1
                        else:
                            # 如果没有缓存（不应该发生），使用None
                            subset_state[f"{agent_id}_output"] = None
                            self.cache_miss_count += 1
                    else:
                        # 智能体不在子集中，使用None或默认行为
                        if agent_id == "TRA":
                            # 交易智能体的默认行为
                            subset_state[f"{agent_id}_output"] = {
                                "action": "hold",
                                "confidence": 0.0,
                                "reasoning": "智能体不在当前评估子集中"
                            }
                        else:
                            subset_state[f"{agent_id}_output"] = None
                
                # 基于状态计算预估收益（简化版本）
                trader_output = subset_state.get("TRA_output")
                if trader_output and isinstance(trader_output, dict):
                    action = trader_output.get("action", "hold")
                    confidence = trader_output.get("confidence", 0.0)
                    
                    if action == "buy":
                        return confidence * 0.02  # 最多2%正收益
                    elif action == "sell":
                        return -confidence * 0.02  # 最多2%负收益
                    else:
                        return 0.0
                else:
                    return 0.0
            
            # 模拟实际收益率（在实际应用中应从环境获取）
            actual_return = 0.01  # 默认1%，实际应从交易环境获取
            
            # 步骤3：开始新的评估期间
            period_id = f"optimized_day_{int(time.time())}"
            market_context = "优化版本的市场分析（使用缓存）"
            
            self.myerson_integration.start_period(period_id, market_context, actual_return, state)
            
            # 步骤4：使用缓存优化的方法计算Myerson值
            contributions = self._compute_myerson_values_with_cache(
                cached_subset_return_calculator, 
                actual_return
            )
            
            # 步骤5：计算贡献梯度
            gradients = self.myerson_integration.compute_contribution_gradients()
            
            # 步骤6：更新反思历史
            self.myerson_integration.update_reflection_histories(agent_outputs, contributions, gradients)
            
            # 步骤7：优化提示词
            optimization_results = self.myerson_integration.optimize_prompts(contributions, gradients)
            
            # 更新统计信息
            myerson_calculation_time = time.time() - myerson_start_time
            self.execution_stats["myerson_calculation_time"] = myerson_calculation_time
            
            # 计算实际的子集评估次数（基于缓存优化）
            num_agents = len(AGENT_GRAPH)
            theoretical_evaluations = num_agents * (2 ** (num_agents - 1))
            actual_llm_calls = len([aid for aid, output in self.agent_output_cache.items() if output is not None])
            self.execution_stats["myerson_subset_evaluations"] = theoretical_evaluations
            self.execution_stats["actual_myerson_llm_calls"] = actual_llm_calls
            
            if self.verbose:
                print(f"✅ 优化的Myerson值计算完成，耗时: {myerson_calculation_time:.2f}s")
                print(f"📊 缓存统计:")
                print(f"  缓存命中: {self.cache_hit_count}")
                print(f"  缓存失败: {self.cache_miss_count}")
                print(f"  理论子集评估: {theoretical_evaluations}")
                print(f"  实际LLM调用: {actual_llm_calls}")
                print(f"  LLM调用减少: {theoretical_evaluations - actual_llm_calls} ({(1 - actual_llm_calls/theoretical_evaluations)*100:.1f}%)")
                print(f"📊 各智能体的收益贡献:")
                for agent_id, contribution in sorted(contributions.items()):
                    percentage = contribution / actual_return * 100 if actual_return != 0 else 0
                    print(f"  {agent_id}: {contribution:.4f} ({percentage:.1f}%)")
            
            return {
                "contributions": contributions,
                "gradients": gradients,
                "optimization_results": optimization_results,
                "calculation_time": myerson_calculation_time,
                "subset_evaluations": theoretical_evaluations,
                "actual_llm_calls": actual_llm_calls,
                "cache_hit_count": self.cache_hit_count,
                "cache_miss_count": self.cache_miss_count,
                "llm_call_reduction": theoretical_evaluations - actual_llm_calls,
                "actual_return": actual_return
            }
            
        except Exception as e:
            print(f"❌ 优化的Myerson值计算失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def _compute_myerson_values_with_cache(self, cached_calculator, actual_return: float) -> Dict[str, float]:
        """
        使用缓存优化计算Myerson值
        
        参数:
            cached_calculator: 缓存优化的子集收益计算器
            actual_return: 实际收益率
            
        返回:
            各智能体的Myerson值字典
        """
        agents = list(AGENT_GRAPH.keys())
        n = len(agents)
        myerson_values = {agent_id: 0.0 for agent_id in agents}
        
        if self.verbose:
            print(f"开始缓存优化的Myerson值计算，智能体数: {n}")
        
        # 预计算权重
        weights = self._calculate_coalition_weights(n)
        
        # 为每个智能体计算其边际贡献
        for i, agent in enumerate(agents):
            if self.verbose and i < 3:  # 只打印前3个智能体的详细信息
                print(f"计算智能体 {agent} 的Myerson值...")
            
            # 遍历不包含当前智能体的所有子集
            other_agents = [a for a in agents if a != agent]
            subsets = self._powerset(other_agents)
            
            for subset_tuple in subsets:
                S = set(subset_tuple)
                S_with_agent = S.union({agent})
                
                # 使用缓存计算特征函数值
                v_S = cached_calculator(S)
                v_S_with_agent = cached_calculator(S_with_agent)
                
                # 计算边际贡献
                marginal_contribution = v_S_with_agent - v_S
                
                # 计算权重
                weight = weights[len(S)]
                
                # 累加到Myerson值
                myerson_values[agent] += weight * marginal_contribution
        
        # 标准化Myerson值为收益贡献
        total_myerson = sum(abs(v) for v in myerson_values.values())
        if total_myerson > 0:
            # 按比例分配实际收益
            contribution_ratios = {k: v / total_myerson for k, v in myerson_values.items()}
            contributions = {k: ratio * actual_return for k, ratio in contribution_ratios.items()}
        else:
            # 如果总Myerson值为0，平均分配
            contributions = {k: actual_return / len(agents) for k in agents}
        
        return contributions
    
    def _powerset(self, iterable: List[str]) -> List[Tuple[str, ...]]:
        """
        生成集合的所有子集
        
        参数:
            iterable: 输入集合
            
        返回:
            所有可能的子集列表
        """
        from itertools import combinations, chain
        s = list(iterable)
        return list(chain.from_iterable(combinations(s, r) for r in range(len(s) + 1)))
    
    def _calculate_coalition_weights(self, n: int) -> List[float]:
        """
        预计算联盟权重
        权重公式为: |S|! * (N-|S|-1)! / N!
        """
        import math
        
        if n == 0:
            return []
        
        weights = [0.0] * n
        n_factorial = math.factorial(n)
        
        for s_size in range(n):
            try:
                s_factorial = math.factorial(s_size)
                n_minus_s_minus_1_factorial = math.factorial(n - s_size - 1)
                weights[s_size] = (s_factorial * n_minus_s_minus_1_factorial) / n_factorial
            except ValueError:
                weights[s_size] = 0.0
                
        return weights
    
    def run(self, days: Optional[int] = None) -> Dict[str, Any]:
        """
        运行优化的多智能体系统
        
        参数:
            days: 运行天数，None表示运行所有可用天数
            
        返回:
            运行结果
        """
        if self.verbose:
            optimization_status = "启用" if self.enable_myerson_calculation else "禁用"
            print(f"\n🚀 开始优化的多智能体系统运行 - {self.run_id}")
            print(f"🧮 Myerson值计算: {optimization_status}")
        
        start_time = time.time()
        results = []
        efficiency = 0.0  # 初始化efficiency变量
        
        # 重置统计信息
        self.execution_stats = {
            "total_agents": len(AGENT_GRAPH),
            "executed_agents": 0,
            "skipped_agents": 0,
            "failed_agents": 0,
            "execution_time": 0,
            "llm_calls_saved": 0,
            "myerson_calculation_time": 0,
            "myerson_subset_evaluations": 0
        }
        
        try:
            max_days = days if days is not None else 10  # 默认10天
            
            for day in range(max_days):
                if self.verbose:
                    print(f"\n{'='*50}")
                    print(f"📅 运行第 {day + 1} 天")
                    print(f"{'='*50}")
                
                # 执行优化的单日逻辑
                day_result = self._run_day_optimized()
                
                # 如果提前终止，记录并继续下一天
                if day_result.get("early_termination", False):
                    if self.verbose:
                        print(f"⚠️  第 {day + 1} 天提前终止: {day_result.get('termination_reason', '未知原因')}")
                
                results.append({
                    "day": day + 1,
                    "result": day_result
                })
                
                # 模拟执行环境step（如果有的话）
                if hasattr(self, 'env') and hasattr(self.env, 'step'):
                    try:
                        self.env.step(day_result["actions"])
                    except Exception as e:
                        if self.verbose:
                            print(f"环境step执行失败: {e}")
        
        except Exception as e:
            if self.verbose:
                print(f"运行过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
        
        # 总体统计
        total_time = time.time() - start_time
        
        # 聚合所有Myerson值计算统计
        total_myerson_time = sum(r["result"]["execution_stats"]["myerson_calculation_time"] for r in results)
        total_subset_evaluations = sum(r["result"]["execution_stats"]["myerson_subset_evaluations"] for r in results)
        total_actual_myerson_calls = sum(r["result"]["execution_stats"].get("actual_myerson_llm_calls", 0) for r in results)
        
        # 初始化所有需要的变量
        total_possible_calls = len(AGENT_GRAPH) * len(results)
        total_actual_calls = sum(r["result"]["execution_stats"]["executed_agents"] for r in results)
        total_saved_calls = total_possible_calls - total_actual_calls
        efficiency = (total_saved_calls / total_possible_calls) * 100 if total_possible_calls > 0 else 0
        
        if self.verbose:
            print(f"\n{'='*50}")
            print(f"🏁 优化的多智能体系统运行完成")
            print(f"{'='*50}")
            print(f"总运行时间: {total_time:.2f}s")
            
            if self.enable_myerson_calculation:
                print(f"总Myerson计算时间: {total_myerson_time:.2f}s")
                print(f"理论子集评估次数: {total_subset_evaluations}")
                print(f"实际Myerson LLM调用: {total_actual_myerson_calls}")
                myerson_reduction = total_subset_evaluations - total_actual_myerson_calls
                myerson_efficiency = (myerson_reduction / total_subset_evaluations) * 100 if total_subset_evaluations > 0 else 0
                print(f"Myerson LLM调用减少: {myerson_reduction} ({myerson_efficiency:.1f}%)")
                print(f"Myerson值计算占比: {total_myerson_time/total_time*100:.1f}%")

            
            # 如果启用了Myerson值计算，显示完整的优化统计
            if self.enable_myerson_calculation:
                print(f"基础智能体LLM调用节省: {total_saved_calls}/{total_possible_calls} ({efficiency:.1f}%)")
                print(f"Myerson缓存优化节省: {myerson_reduction} 次LLM调用")
                total_theoretical_calls = total_possible_calls + total_subset_evaluations
                total_actual_all_calls = total_actual_calls + total_actual_myerson_calls
                overall_reduction = total_theoretical_calls - total_actual_all_calls
                overall_efficiency = (overall_reduction / total_theoretical_calls) * 100 if total_theoretical_calls > 0 else 0
                print(f"整体LLM调用优化: {overall_reduction}/{total_theoretical_calls} ({overall_efficiency:.1f}%)")
            else:
                print(f"总LLM调用节省: {total_saved_calls}/{total_possible_calls} ({efficiency:.1f}%)")
        
        # 计算overall_efficiency，确保变量存在
        if self.enable_myerson_calculation:
            total_theoretical_calls = total_possible_calls + total_subset_evaluations
            total_actual_all_calls = total_actual_calls + total_actual_myerson_calls
            overall_reduction = total_theoretical_calls - total_actual_all_calls
            overall_efficiency = (overall_reduction / total_theoretical_calls) * 100 if total_theoretical_calls > 0 else 0
        else:
            overall_efficiency = efficiency
        
        return {
            "run_id": self.run_id,
            "total_days": len(results),
            "total_time": total_time,
            "daily_results": results,
            "overall_stats": {
                "efficiency_improvement": efficiency if not self.enable_myerson_calculation else 0,
                "total_llm_calls_saved": total_saved_calls,
                "total_possible_calls": total_possible_calls,
                "total_actual_calls": total_actual_calls,
                "myerson_calculation_enabled": self.enable_myerson_calculation,
                "total_myerson_time": total_myerson_time,
                "total_subset_evaluations": total_subset_evaluations,
                "total_actual_myerson_calls": total_actual_myerson_calls,
                "myerson_llm_reduction": total_subset_evaluations - total_actual_myerson_calls if self.enable_myerson_calculation else 0,
                "overall_efficiency": overall_efficiency
            }
        } 