#!/usr/bin/env python3
"""
LLM环境配置检查工具

本脚本用于检查LLM环境是否正确配置，包括：
1. 环境变量设置
2. SDK安装情况
3. API连接测试
4. 智能体创建测试

使用方法:
    python check_llm_setup.py --provider zhipuai
    python check_llm_setup.py --provider openai
"""

import os
import sys
import argparse
from typing import Optional, Dict, Any
from dotenv import load_dotenv


def check_environment_variables(provider: str) -> bool:
    """检查环境变量"""
    print(f"🔍 检查 {provider} 环境变量...")
    
    if provider == "zhipuai":
        api_key = os.environ.get("ZHIPUAI_API_KEY")
        if api_key:
            print(f"✅ ZHIPUAI_API_KEY: {'*' * (len(api_key) - 4) + api_key[-4:]}")
            return True
        else:
            print("❌ 未找到 ZHIPUAI_API_KEY 环境变量")
            print("请设置: export ZHIPUAI_API_KEY=your_api_key")
            return False
            
    elif provider == "openai":
        api_key = os.environ.get("OPENAI_API_KEY")
        if api_key:
            print(f"✅ OPENAI_API_KEY: {'*' * (len(api_key) - 4) + api_key[-4:]}")
            return True
        else:
            print("❌ 未找到 OPENAI_API_KEY 环境变量")
            print("请设置: export OPENAI_API_KEY=your_api_key")
            return False
    
    return False


def check_sdk_installation(provider: str) -> bool:
    """检查SDK安装"""
    print(f"📦 检查 {provider} SDK安装...")
    
    if provider == "zhipuai":
        try:
            import zhipuai
            print(f"✅ zhipuai SDK 已安装 (版本: {getattr(zhipuai, '__version__', '未知')})")
            return True
        except ImportError:
            print("❌ zhipuai SDK 未安装")
            print("请安装: pip install zhipuai")
            return False
            
    elif provider == "openai":
        try:
            import openai
            print(f"✅ openai SDK 已安装 (版本: {getattr(openai, '__version__', '未知')})")
            return True
        except ImportError:
            print("❌ openai SDK 未安装")
            print("请安装: pip install openai")
            return False
    
    return False


def test_llm_interface(provider: str) -> bool:
    """测试LLM接口"""
    print(f"🔗 测试 {provider} LLM接口...")
    
    try:
        from contribution_assessment.llm_interface import LLMInterface
        
        llm_interface = LLMInterface(provider=provider)
        
        if llm_interface.client:
            print("✅ LLM接口初始化成功")
            
            # 尝试简单的分析调用
            test_prompt = "请简单回答：今天天气如何？"
            result = llm_interface.analyze(test_prompt, model="glm-4-flash" if provider == "zhipuai" else "gpt-3.5-turbo")
            
            if result:
                print("✅ LLM API调用成功")
                print(f"   响应示例: {str(result)[:100]}...")
                return True
            else:
                print("❌ LLM API调用失败")
                return False
        else:
            print("❌ LLM接口初始化失败")
            return False
            
    except Exception as e:
        print(f"❌ LLM接口测试失败: {e}")
        return False


def test_agent_creation(provider: str) -> bool:
    """测试智能体创建"""
    print(f"🤖 测试智能体创建...")
    
    try:
        from contribution_assessment.llm_interface import LLMInterface
        from agents.agent_factory import AgentFactory
        
        # 创建LLM接口
        llm_interface = LLMInterface(provider=provider)
        
        if not llm_interface.client:
            print("❌ LLM接口不可用，无法创建智能体")
            return False
        
        # 创建智能体工厂
        agent_factory = AgentFactory(llm_interface=llm_interface)
        
        # 测试创建单个智能体
        test_agent = agent_factory.create_agent("NAA")
        
        if test_agent:
            print("✅ 智能体创建成功")
            print(f"   智能体类型: {type(test_agent).__name__}")
            print(f"   智能体ID: {test_agent.agent_id}")
            
            # 测试智能体是否有LLM接口
            if hasattr(test_agent, 'llm_interface') and test_agent.llm_interface:
                print("✅ 智能体已正确配置LLM接口")
                return True
            else:
                print("❌ 智能体未正确配置LLM接口")
                return False
        else:
            print("❌ 智能体创建失败")
            return False
            
    except Exception as e:
        print(f"❌ 智能体创建测试失败: {e}")
        return False


def test_contribution_assessor(provider: str) -> bool:
    """测试贡献度评估器"""
    print(f"📊 测试贡献度评估器...")
    
    try:
        from contribution_assessment import ContributionAssessor
        from contribution_assessment.llm_interface import LLMInterface
        from agents.agent_factory import AgentFactory
        
        # 创建智能体实例
        llm_interface = LLMInterface(provider=provider)
        if not llm_interface.client:
            print("❌ LLM接口不可用")
            return False
        
        agent_factory = AgentFactory(llm_interface=llm_interface)
        agent_instances = {"NAA": agent_factory.create_agent("NAA")}
        
        # 创建简单配置
        config = {
            "start_date": "2023-01-01",
            "end_date": "2023-01-02",
            "stocks": ["AAPL"],
            "starting_cash": 10000,
            "simulation_days": 1
        }
        
        # 创建评估器
        assessor = ContributionAssessor(
            config=config,
            agents=agent_instances,
            llm_provider=provider
        )
        
        # 检查评估器配置
        if assessor.llm_interface and assessor.llm_interface.client:
            print("✅ 贡献度评估器LLM接口配置正确")
        else:
            print("❌ 贡献度评估器LLM接口配置错误")
            return False
        
        if assessor.agents:
            print(f"✅ 贡献度评估器智能体配置正确 ({len(assessor.agents)} 个智能体)")
            return True
        else:
            print("❌ 贡献度评估器智能体配置错误")
            return False
            
    except Exception as e:
        print(f"❌ 贡献度评估器测试失败: {e}")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="LLM环境配置检查工具")
    parser.add_argument(
        "--provider", 
        type=str, 
        required=True,
        choices=["zhipuai", "openai"],
        help="LLM提供商 (zhipuai 或 openai)"
    )
    
    args = parser.parse_args()
    
    # 加载环境变量
    load_dotenv()
    
    print(f"🔧 LLM环境配置检查 - {args.provider}")
    print("=" * 60)
    
    checks = [
        ("环境变量", lambda: check_environment_variables(args.provider)),
        ("SDK安装", lambda: check_sdk_installation(args.provider)),
        ("LLM接口", lambda: test_llm_interface(args.provider)),
        ("智能体创建", lambda: test_agent_creation(args.provider)),
        ("评估器配置", lambda: test_contribution_assessor(args.provider))
    ]
    
    results = []
    
    for check_name, check_func in checks:
        print(f"\n{'-' * 40}")
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"❌ {check_name} 检查时出错: {e}")
            results.append((check_name, False))
    
    # 总结结果
    print(f"\n{'=' * 60}")
    print("📋 检查结果总结:")
    print("=" * 60)
    
    passed = 0
    for check_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{check_name:15} : {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 项检查通过")
    
    if passed == len(results):
        print("\n🎉 所有检查通过！您的LLM环境配置正确。")
        print("现在可以运行: python run_with_real_agents.py --llm-provider", args.provider)
    else:
        print(f"\n⚠️  有 {len(results) - passed} 项检查失败，请根据上述提示进行修复。")
        sys.exit(1)


if __name__ == "__main__":
    main()
