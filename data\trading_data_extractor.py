#!/usr/bin/env python3
"""
交易数据提取器 (Trading Data Extractor)

从现有的交易系统和评估结果中提取数据，转换为标准化格式
用于与新的数据存储系统集成

作者: AI Assistant
创建时间: 2025-07-04
"""

import os
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
import sqlite3

from .trading_data_collector import (
    TradingDataCollector,
    AgentInputData,
    AgentOutputData,
    MarketConditions
)

class TradingDataExtractor:
    """
    交易数据提取器
    
    从现有系统中提取交易数据并转换为标准格式
    """
    
    def __init__(self, 
                 data_collector: TradingDataCollector,
                 logger: Optional[logging.Logger] = None):
        """
        初始化数据提取器
        
        参数:
            data_collector: 交易数据收集器
            logger: 日志记录器
        """
        self.data_collector = data_collector
        self.logger = logger or self._create_default_logger()
        
        self.logger.info("交易数据提取器初始化完成")
    
    def _create_default_logger(self) -> logging.Logger:
        """创建默认日志记录器"""
        logger = logging.getLogger(f"{__name__}.TradingDataExtractor")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def extract_from_assessment_result(self, assessment_result: Dict[str, Any]) -> bool:
        """
        从评估结果中提取交易数据
        
        参数:
            assessment_result: 贡献度评估结果
            
        返回:
            是否提取成功
        """
        try:
            # 开始新的交易会话
            session_id = self.data_collector.start_trading_session(
                f"assessment_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            )
            
            # 提取Shapley值作为性能指标
            shapley_values = assessment_result.get("shapley_values", {})
            
            # 提取模拟结果
            simulation_result = assessment_result.get("simulation_result", {})
            coalition_results = simulation_result.get("coalition_results", {})
            
            # 为每个智能体创建输入和输出数据
            for agent_id in shapley_values.keys():
                # 创建模拟的智能体输入数据
                input_data = self._create_agent_input_from_assessment(
                    agent_id, assessment_result
                )
                if input_data:
                    self.data_collector.collect_agent_input(input_data)
                
                # 创建智能体输出数据
                output_data = self._create_agent_output_from_assessment(
                    agent_id, assessment_result
                )
                if output_data:
                    self.data_collector.collect_agent_output(output_data)
                
                # 记录盈亏（使用Shapley值作为代理）
                shapley_score = shapley_values.get(agent_id, 0.0)
                self.data_collector.record_profit_loss(
                    agent_id, 
                    shapley_score * 1000,  # 转换为货币单位
                    {"shapley_value": shapley_score}
                )
            
            # 创建市场条件数据
            market_conditions = self._create_market_conditions_from_assessment(
                assessment_result
            )
            if market_conditions:
                self.data_collector.collect_market_conditions(market_conditions)
            
            # 结束会话
            session_summary = {
                "source": "assessment_result",
                "total_agents": len(shapley_values),
                "total_coalitions": len(coalition_results),
                "assessment_success": assessment_result.get("success", False)
            }
            
            success = self.data_collector.end_trading_session(session_summary)
            
            if success:
                self.logger.info(f"从评估结果提取交易数据成功: {session_id}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"从评估结果提取交易数据失败: {e}")
            return False
    
    def extract_from_stock_data(self, stock_symbol: str, date: str) -> bool:
        """
        从股票数据库中提取市场数据
        
        参数:
            stock_symbol: 股票代码
            date: 日期 (YYYY-MM-DD)
            
        返回:
            是否提取成功
        """
        try:
            # 获取股票数据库路径
            project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            db_path = os.path.join(project_root, "data", "tickers", stock_symbol, f"{stock_symbol}_data.db")
            
            if not os.path.exists(db_path):
                self.logger.warning(f"股票数据库不存在: {db_path}")
                return False
            
            # 查询股票数据
            with sqlite3.connect(db_path) as conn:
                cursor = conn.cursor()
                
                # 查询OHLCV数据
                cursor.execute("""
                    SELECT ticker, trade_date, Open, High, Low, Close, Adj_Close, Volume
                    FROM ohlcv 
                    WHERE ticker = ? AND trade_date = ?
                """, (stock_symbol, date))
                
                ohlcv_data = cursor.fetchone()
                
                if not ohlcv_data:
                    self.logger.warning(f"未找到股票数据: {stock_symbol} on {date}")
                    return False
                
                # 查询新闻数据
                cursor.execute("""
                    SELECT title, summary, sentiment_score, published_date
                    FROM news 
                    WHERE ticker = ? AND DATE(published_date) = ?
                    LIMIT 10
                """, (stock_symbol, date))
                
                news_data = cursor.fetchall()
            
            # 创建市场条件数据
            market_conditions = MarketConditions(
                timestamp=f"{date}T16:00:00",  # 假设收盘时间
                stock_prices={
                    stock_symbol: {
                        "open": float(ohlcv_data[2]),
                        "high": float(ohlcv_data[3]),
                        "low": float(ohlcv_data[4]),
                        "close": float(ohlcv_data[5]),
                        "adj_close": float(ohlcv_data[6]),
                        "volume": float(ohlcv_data[7])
                    }
                },
                market_indicators={
                    "daily_return": (float(ohlcv_data[5]) - float(ohlcv_data[2])) / float(ohlcv_data[2]),
                    "volume_ratio": float(ohlcv_data[7]) / 1000000  # 简化的成交量比率
                },
                volatility_metrics={
                    "daily_range": (float(ohlcv_data[3]) - float(ohlcv_data[4])) / float(ohlcv_data[5])
                },
                news_sentiment={
                    "total_news": len(news_data),
                    "avg_sentiment": sum(row[2] for row in news_data if row[2]) / max(len(news_data), 1),
                    "news_titles": [row[0] for row in news_data[:5]]  # 前5条新闻标题
                },
                trading_volume={
                    stock_symbol: float(ohlcv_data[7])
                },
                metadata={
                    "data_source": "stock_database",
                    "extraction_date": datetime.now().isoformat()
                }
            )
            
            # 开始会话并收集数据
            session_id = self.data_collector.start_trading_session(
                f"market_data_{stock_symbol}_{date}"
            )
            
            success = self.data_collector.collect_market_conditions(market_conditions)
            
            if success:
                # 结束会话
                session_summary = {
                    "source": "stock_database",
                    "stock_symbol": stock_symbol,
                    "date": date,
                    "data_points": 1
                }
                success = self.data_collector.end_trading_session(session_summary)
            
            if success:
                self.logger.info(f"从股票数据提取市场数据成功: {stock_symbol} on {date}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"从股票数据提取市场数据失败: {e}")
            return False
    
    def _create_agent_input_from_assessment(self, agent_id: str, assessment_result: Dict[str, Any]) -> Optional[AgentInputData]:
        """从评估结果创建智能体输入数据"""
        try:
            # 从评估结果中提取相关信息
            config = assessment_result.get("config", {})
            
            return AgentInputData(
                agent_id=agent_id,
                market_signals={
                    "start_date": config.get("start_date"),
                    "end_date": config.get("end_date"),
                    "stocks": config.get("stocks", []),
                    "starting_cash": config.get("starting_cash", 0)
                },
                analysis_inputs={
                    "simulation_days": config.get("simulation_days"),
                    "analysis_cache_enabled": True
                },
                decision_factors={
                    "agent_type": self._get_agent_type(agent_id),
                    "coalition_participation": True
                },
                timestamp=datetime.now().isoformat(),
                metadata={
                    "source": "assessment_extraction",
                    "assessment_timestamp": assessment_result.get("execution_info", {}).get("timestamp")
                }
            )
            
        except Exception as e:
            self.logger.error(f"创建智能体输入数据失败: {e}")
            return None
    
    def _create_agent_output_from_assessment(self, agent_id: str, assessment_result: Dict[str, Any]) -> Optional[AgentOutputData]:
        """从评估结果创建智能体输出数据"""
        try:
            shapley_values = assessment_result.get("shapley_values", {})
            shapley_score = shapley_values.get(agent_id, 0.0)
            
            return AgentOutputData(
                agent_id=agent_id,
                trading_decisions={
                    "participation_decision": "participate" if shapley_score > 0 else "abstain",
                    "contribution_level": abs(shapley_score)
                },
                recommendations=[
                    {
                        "type": "coalition_participation",
                        "recommendation": "participate" if shapley_score > 0 else "review_strategy",
                        "confidence": min(abs(shapley_score) * 10, 1.0)
                    }
                ],
                confidence_scores={
                    "overall_confidence": min(abs(shapley_score) * 10, 1.0),
                    "decision_confidence": 0.8 if shapley_score != 0 else 0.3
                },
                reasoning=f"基于Shapley值分析，该智能体的贡献度为 {shapley_score:.6f}",
                timestamp=datetime.now().isoformat(),
                metadata={
                    "shapley_value": shapley_score,
                    "source": "assessment_extraction"
                }
            )
            
        except Exception as e:
            self.logger.error(f"创建智能体输出数据失败: {e}")
            return None
    
    def _create_market_conditions_from_assessment(self, assessment_result: Dict[str, Any]) -> Optional[MarketConditions]:
        """从评估结果创建市场条件数据"""
        try:
            config = assessment_result.get("config", {})
            summary = assessment_result.get("summary", {})
            
            return MarketConditions(
                timestamp=datetime.now().isoformat(),
                stock_prices={
                    # 使用配置中的股票信息
                    stock: {"close": 100.0, "volume": 1000000}  # 模拟数据
                    for stock in config.get("stocks", ["AAPL"])
                },
                market_indicators={
                    "system_performance": summary.get("total_system_value", 0.0),
                    "coalition_success_rate": summary.get("simulation_success_rate", 0.0)
                },
                volatility_metrics={
                    "assessment_volatility": 0.1  # 模拟波动率
                },
                news_sentiment={
                    "assessment_sentiment": "neutral"
                },
                trading_volume={
                    stock: 1000000.0 for stock in config.get("stocks", ["AAPL"])
                },
                metadata={
                    "source": "assessment_extraction",
                    "total_agents": summary.get("total_agents", 0),
                    "total_coalitions": summary.get("total_coalitions_generated", 0)
                }
            )
            
        except Exception as e:
            self.logger.error(f"创建市场条件数据失败: {e}")
            return None
    
    def _get_agent_type(self, agent_id: str) -> str:
        """根据智能体ID确定类型"""
        if agent_id in ["NAA", "TAA", "FAA"]:
            return "analyst"
        elif agent_id in ["BOA", "BeOA", "NOA"]:
            return "outlook"
        elif agent_id == "TRA":
            return "trader"
        else:
            return "unknown"
