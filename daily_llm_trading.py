#!/usr/bin/env python3
"""
每日LLM交易系统 (Daily LLM Trading System)

实现真正的每日LLM交易：每个交易日都会调用LLM进行分析和决策
"""

import argparse
import sys
import logging
from datetime import datetime
from typing import Dict, Any, Optional

# 导入必要的模块
try:
    from contribution_assessment import ContributionAssessor
    from contribution_assessment.llm_interface import LLMInterface
    from agents.agent_factory import AgentFactory
except ImportError as e:
    print(f"❌ 导入模块失败: {e}", file=sys.stderr)
    print("请确保在项目根目录下运行此脚本", file=sys.stderr)
    sys.exit(1)


def setup_logging(verbose: bool = False) -> logging.Logger:
    """设置日志配置"""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    return logging.getLogger(__name__)


def create_config(args) -> Dict[str, Any]:
    """创建配置字典"""
    return {
        "start_date": args.start_date,
        "end_date": args.end_date,
        "stocks": args.stocks,
        "starting_cash": args.starting_cash,
        "simulation_days": args.simulation_days,
        
        # 周期性评估配置
        "weekly_evaluation_enabled": args.weekly_evaluation,
        "trading_days_per_week": args.trading_days_per_week,
        "performance_threshold": args.performance_threshold,
        
        # 每日LLM配置
        "daily_llm_enabled": True,
        "show_llm_io": True,
        
        # 交易配置
        "trading_fee_rate": 0.001,
        "fail_on_large_gaps": False
    }


def run_daily_llm_trading(config: Dict[str, Any], 
                         llm_provider: str,
                         target_agents: list,
                         logger: logging.Logger) -> Dict[str, Any]:
    """
    运行每日LLM交易
    
    参数:
        config: 配置字典
        llm_provider: LLM提供商
        target_agents: 目标智能体列表
        logger: 日志记录器
        
    返回:
        交易结果
    """
    logger.info("🚀 启动每日LLM交易系统")
    
    # 1. 初始化LLM接口
    logger.info(f"初始化LLM接口: {llm_provider}")
    llm_interface = LLMInterface(provider=llm_provider, logger=logger)
    
    if not llm_interface.client:
        raise RuntimeError(f"LLM接口初始化失败: {llm_provider}")
    
    # 2. 创建智能体工厂和智能体
    logger.info("创建智能体实例...")
    agent_factory = AgentFactory(llm_interface=llm_interface, logger=logger)
    agents = agent_factory.create_agents_subset(target_agents)
    
    # 验证智能体
    validation_results = agent_factory.validate_agents(agents)
    valid_agents = {k: v for k, v in agents.items() if validation_results[k]}
    
    logger.info(f"成功创建 {len(valid_agents)} 个智能体: {list(valid_agents.keys())}")
    
    if not valid_agents:
        raise RuntimeError("没有有效的智能体可用")
    
    # 3. 创建评估器
    logger.info("初始化贡献度评估器...")
    assessor = ContributionAssessor(
        config=config,
        llm_provider=llm_provider,
        logger=logger
    )
    
    # 4. 运行每日LLM交易评估
    logger.info("开始每日LLM交易评估...")
    logger.info("=" * 80)
    logger.info(f"📅 交易期间: {config['start_date']} 至 {config['end_date']}")
    logger.info(f"📈 股票代码: {config['stocks']}")
    logger.info(f"🤖 智能体: {list(valid_agents.keys())}")
    logger.info(f"🧠 LLM模式: 每日调用")
    logger.info("=" * 80)
    
    # 执行评估
    start_time = datetime.now()
    result = assessor.run(
        agents=valid_agents,
        target_agents=target_agents,
        max_coalitions=20  # 限制联盟数量以控制执行时间
    )
    end_time = datetime.now()
    
    # 添加执行统计
    result["execution_time"] = (end_time - start_time).total_seconds()
    result["llm_provider"] = llm_provider
    result["daily_llm_enabled"] = True
    result["agent_count"] = len(valid_agents)
    
    return result


def display_results(result: Dict[str, Any], logger: logging.Logger):
    """显示结果"""
    logger.info("=" * 80)
    logger.info("📊 每日LLM交易结果")
    logger.info("=" * 80)
    
    shapley_values = result.get("shapley_values", {})
    if shapley_values:
        logger.info("🏆 智能体贡献度排名 (Shapley值):")
        sorted_agents = sorted(shapley_values.items(), key=lambda x: x[1], reverse=True)
        for i, (agent, value) in enumerate(sorted_agents, 1):
            logger.info(f"  {i}. {agent}: {value:.6f}")
    
    # 显示执行统计
    stats = result.get("stats", {})
    if stats:
        logger.info("\n📈 执行摘要:")
        logger.info(f"  • 总执行时间: {result.get('execution_time', 0):.2f}s")
        logger.info(f"  • LLM提供商: {result.get('llm_provider', 'unknown')}")
        logger.info(f"  • 智能体数量: {result.get('agent_count', 0)}")
        logger.info(f"  • 模拟联盟数: {stats.get('coalitions_evaluated', 0)}")
        logger.info(f"  • 成功率: {stats.get('success_rate', 0):.1f}%")
    
    logger.info("=" * 80)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="每日LLM交易系统 - 真正的每日AI交易体验",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    # 基本配置
    parser.add_argument("--start-date", type=str, default="2025-01-01", help="开始日期")
    parser.add_argument("--end-date", type=str, default="2025-01-03", help="结束日期")
    parser.add_argument("--stocks", nargs="+", default=["AAPL"], help="股票代码列表")
    parser.add_argument("--starting-cash", type=float, default=1000000, help="初始资金")
    parser.add_argument("--simulation-days", type=int, default=3, help="模拟天数")
    
    # LLM配置
    parser.add_argument("--llm-provider", type=str, required=True, 
                       choices=["zhipuai", "openai"], help="LLM提供商")
    
    # 智能体配置
    parser.add_argument("--agents", nargs="+", 
                       default=["NAA", "TAA", "FAA", "TRA"],
                       help="目标智能体列表")
    
    # 周期性评估配置
    parser.add_argument("--weekly-evaluation", action="store_true", help="启用周期性评估")
    parser.add_argument("--trading-days-per-week", type=int, default=5, help="每周交易日数")
    parser.add_argument("--performance-threshold", type=float, default=0.8, help="性能阈值")
    
    # 其他配置
    parser.add_argument("--verbose", action="store_true", help="详细日志输出")
    
    args = parser.parse_args()
    
    # 设置日志
    logger = setup_logging(args.verbose)
    
    try:
        # 创建配置
        config = create_config(args)
        
        # 运行每日LLM交易
        result = run_daily_llm_trading(
            config=config,
            llm_provider=args.llm_provider,
            target_agents=args.agents,
            logger=logger
        )
        
        # 显示结果
        display_results(result, logger)
        
        logger.info("✅ 每日LLM交易完成")
        
    except KeyboardInterrupt:
        logger.info("❌ 用户中断执行")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ 执行失败: {e}")
        if args.verbose:
            import traceback
            logger.error(traceback.format_exc())
        sys.exit(1)


if __name__ == "__main__":
    main()