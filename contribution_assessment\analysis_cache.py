"""
分析缓存模块 (Analysis Cache Module)

本模块实现了分析层智能体输出的缓存机制，用于存储和检索NAA、TAA、FAA等
分析智能体的输出结果，避免在Shapley值计算过程中重复运行昂贵的分析操作。

主要功能：
1. 存储分析智能体的输出结果
2. 按智能体ID检索缓存的分析结果
3. 一次性运行所有分析智能体并缓存结果
4. 提供缓存状态查询和管理功能
"""

import time
import copy
from typing import Dict, Any, Optional, Set
from datetime import datetime
import logging


class AnalysisCache:
    """
    分析缓存类
    
    用于存储和检索分析层智能体（NAA, TAA, FAA）的输出结果，
    支持高效的缓存管理和数据一致性保证。
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        初始化分析缓存
        
        参数:
            logger: 日志记录器，如果为None则创建默认记录器
        """
        self.logger = logger or self._create_default_logger()
        
        # 缓存存储：智能体ID -> 分析结果
        self._cache: Dict[str, Any] = {}
        
        # 缓存元数据
        self._metadata: Dict[str, Dict[str, Any]] = {}
        
        # 缓存统计信息
        self._stats = {
            "total_stores": 0,
            "total_gets": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "last_populated": None,
            "populated_agents": set(),
            "refresh_count": 0,
            "last_refresh": None
        }
        
        # 支持的分析智能体列表
        self.analyst_agents = ["NAA", "TAA", "FAA", "BOA", "BeOA", "NOA", "TRA"]
        
        self.logger.info("分析缓存初始化完成")
    
    def _create_default_logger(self) -> logging.Logger:
        """创建默认日志记录器"""
        logger = logging.getLogger(f"{__name__}.AnalysisCache")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def store(self, agent_id: str, data: Any) -> None:
        """
        存储智能体的分析结果
        
        参数:
            agent_id: 智能体ID（如"NAA", "TAA", "FAA"）
            data: 智能体的分析输出数据
            
        异常:
            ValueError: 当agent_id不是支持的分析智能体时
        """
        if agent_id not in self.analyst_agents:
            raise ValueError(f"不支持的智能体ID: {agent_id}。支持的智能体: {self.analyst_agents}")
        
        # 深拷贝数据以避免外部修改影响缓存
        cached_data = copy.deepcopy(data)
        
        # 存储数据
        self._cache[agent_id] = cached_data
        
        # 更新元数据
        self._metadata[agent_id] = {
            "stored_at": datetime.now(),
            "data_type": type(data).__name__,
            "data_size": len(str(data)) if data is not None else 0
        }
        
        # 更新统计信息
        self._stats["total_stores"] += 1
        self._stats["populated_agents"].add(agent_id)
        
        self.logger.debug(f"已缓存智能体 {agent_id} 的分析结果")
    
    def get(self, agent_id: str) -> Any:
        """
        获取智能体的缓存分析结果
        
        参数:
            agent_id: 智能体ID
            
        返回:
            智能体的分析结果，如果不存在则返回None
        """
        self._stats["total_gets"] += 1
        
        if agent_id in self._cache:
            self._stats["cache_hits"] += 1
            self.logger.debug(f"缓存命中: {agent_id}")
            # 返回深拷贝以避免外部修改影响缓存
            return copy.deepcopy(self._cache[agent_id])
        else:
            self._stats["cache_misses"] += 1
            self.logger.debug(f"缓存未命中: {agent_id}")
            return None
    
    def populate_from_agents(self, analyst_agents: Dict[str, Any], state: Dict[str, Any]) -> Dict[str, Any]:
        """
        一次性运行所有分析智能体并缓存其结果
        
        这是核心方法，用于在Shapley值计算开始前预先运行所有分析智能体，
        避免在后续的联盟模拟中重复执行昂贵的分析操作。
        
        参数:
            analyst_agents: 分析智能体字典，格式为 {agent_id: agent_instance}
            state: 智能体执行所需的状态信息
            
        返回:
            执行结果字典，包含成功/失败的智能体信息
        """
        start_time = time.time()
        results = {
            "successful_agents": [],
            "failed_agents": [],
            "execution_time": 0,
            "total_agents": len(analyst_agents)
        }
        
        self.logger.info(f"开始填充分析缓存，共 {len(analyst_agents)} 个智能体")
        
        # 清空现有缓存
        self.clear()
        
        # 逐个执行分析智能体
        for agent_id, agent_instance in analyst_agents.items():
            if agent_id not in self.analyst_agents:
                self.logger.warning(f"跳过非分析智能体: {agent_id}")
                continue
            
            try:
                self.logger.info(f"执行分析智能体: {agent_id}")
                agent_start_time = time.time()
                
                # 执行智能体的process方法
                analysis_result = agent_instance.process(state)
                
                agent_execution_time = time.time() - agent_start_time
                
                # 缓存结果
                self.store(agent_id, analysis_result)
                
                results["successful_agents"].append({
                    "agent_id": agent_id,
                    "execution_time": agent_execution_time
                })
                
                self.logger.info(f"智能体 {agent_id} 执行成功 ({agent_execution_time:.2f}s)")
                
            except Exception as e:
                self.logger.error(f"智能体 {agent_id} 执行失败: {e}")
                results["failed_agents"].append({
                    "agent_id": agent_id,
                    "error": str(e)
                })
        
        # 更新统计信息
        total_execution_time = time.time() - start_time
        results["execution_time"] = total_execution_time
        self._stats["last_populated"] = datetime.now()
        
        self.logger.info(
            f"分析缓存填充完成: "
            f"成功 {len(results['successful_agents'])} 个, "
            f"失败 {len(results['failed_agents'])} 个, "
            f"总耗时 {total_execution_time:.2f}s"
        )
        
        return results
    
    def is_populated(self) -> bool:
        """
        检查缓存是否已填充所有分析智能体的结果
        
        返回:
            如果所有分析智能体都已缓存则返回True，否则返回False
        """
        return all(agent_id in self._cache for agent_id in self.analyst_agents)
    
    def get_cached_agents(self) -> Set[str]:
        """
        获取已缓存的智能体ID集合
        
        返回:
            已缓存的智能体ID集合
        """
        return set(self._cache.keys())
    
    def clear(self) -> None:
        """清空所有缓存数据"""
        self._cache.clear()
        self._metadata.clear()
        self._stats["populated_agents"].clear()
        self.logger.info("分析缓存已清空")
    
    def refresh_cache(self, analyst_agents: Dict[str, Any], state: Dict[str, Any]) -> Dict[str, Any]:
        """
        刷新缓存数据，重新运行分析智能体
        
        与populate_from_agents的区别：
        - 这是为周期性刷新设计的方法
        - 会更新刷新统计信息
        - 提供更详细的刷新日志
        
        参数:
            analyst_agents: 分析智能体字典
            state: 当前状态信息
            
        返回:
            刷新结果字典
        """
        self.logger.info("开始周期性缓存刷新...")
        
        # 记录刷新统计
        self._stats["refresh_count"] += 1
        self._stats["last_refresh"] = datetime.now()
        
        # 调用核心填充方法
        refresh_result = self.populate_from_agents(analyst_agents, state)
        
        # 添加刷新特定信息
        refresh_result["refresh_number"] = self._stats["refresh_count"]
        refresh_result["refresh_timestamp"] = self._stats["last_refresh"].isoformat()
        
        self.logger.info(f"第 {self._stats['refresh_count']} 次缓存刷新完成")
        
        return refresh_result
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取缓存统计信息
        
        返回:
            包含缓存使用统计的字典
        """
        stats = copy.deepcopy(self._stats)
        stats["cache_size"] = len(self._cache)
        stats["hit_rate"] = (
            self._stats["cache_hits"] / self._stats["total_gets"] 
            if self._stats["total_gets"] > 0 else 0.0
        )
        return stats
    
    def get_cache_info(self) -> Dict[str, Any]:
        """
        获取详细的缓存信息
        
        返回:
            包含缓存内容和元数据的详细信息
        """
        return {
            "cached_agents": list(self._cache.keys()),
            "metadata": copy.deepcopy(self._metadata),
            "stats": self.get_stats(),
            "is_populated": self.is_populated()
        }
