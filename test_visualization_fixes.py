#!/usr/bin/env python3
"""
测试可视化修复脚本

用于验证图表可视化问题的修复效果：
1. 中文字符显示
2. 文本重叠问题
3. 图表布局优化
"""

import os
import sys
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.ticker import MaxNLocator
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def setup_chinese_fonts():
    """设置中文字体支持"""
    import platform
    
    # 根据操作系统选择合适的中文字体
    system = platform.system()
    
    if system == "Windows":
        chinese_fonts = ['SimHei', 'Microsoft YaHei', 'SimSun', 'KaiTi']
    elif system == "Darwin":  # macOS
        chinese_fonts = ['Arial Unicode MS', 'PingFang SC', 'Heiti SC', 'STHeiti']
    else:  # Linux
        chinese_fonts = ['WenQuanYi Micro Hei', 'DejaVu Sans', 'Liberation Sans']
    
    chinese_fonts.extend(['DejaVu Sans', 'Arial', 'sans-serif'])
    
    # 设置matplotlib参数
    plt.rcParams['font.sans-serif'] = chinese_fonts
    plt.rcParams['axes.unicode_minus'] = False
    
    # 改进的图表样式设置
    plt.rcParams['figure.figsize'] = (12, 8)
    plt.rcParams['figure.dpi'] = 100
    plt.rcParams['savefig.dpi'] = 300
    plt.rcParams['savefig.bbox'] = 'tight'
    plt.rcParams['savefig.pad_inches'] = 0.2
    
    # 字体大小设置
    plt.rcParams['font.size'] = 10
    plt.rcParams['axes.titlesize'] = 14
    plt.rcParams['axes.labelsize'] = 12
    plt.rcParams['xtick.labelsize'] = 10
    plt.rcParams['ytick.labelsize'] = 10
    plt.rcParams['legend.fontsize'] = 10
    
    # 布局设置
    plt.rcParams['figure.autolayout'] = True
    plt.rcParams['axes.grid'] = True
    
    print(f"已设置中文字体: {chinese_fonts[0]}")
    return chinese_fonts[0]

def create_test_data():
    """创建测试数据"""
    # 生成30天的测试数据
    dates = pd.date_range(start='2025-01-01', periods=30, freq='D')
    
    # 模拟股价数据
    np.random.seed(42)
    base_price = 100
    price_changes = np.random.normal(0, 2, 30)
    prices = [base_price]
    
    for change in price_changes[1:]:
        new_price = prices[-1] + change
        prices.append(max(new_price, 10))  # 确保价格不为负
    
    # 创建DataFrame
    df = pd.DataFrame({
        'date': dates,
        'close_price': prices,
        'volume': np.random.randint(1000000, 5000000, 30),
        'open_price': [p + np.random.normal(0, 1) for p in prices],
        'high_price': [p + abs(np.random.normal(0, 2)) for p in prices],
        'low_price': [p - abs(np.random.normal(0, 2)) for p in prices]
    })
    
    return df

def format_date_axis(ax, dates, max_ticks=10):
    """改进的日期轴格式化"""
    # 设置日期格式
    ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
    
    # 智能设置日期标签数量
    if len(dates) > max_ticks:
        # 如果数据点太多，减少标签数量
        ax.xaxis.set_major_locator(MaxNLocator(nbins=max_ticks))
    
    # 设置标签旋转和字体大小
    plt.setp(ax.xaxis.get_majorticklabels(), 
            rotation=45,
            fontsize=10,
            ha='right')  # 右对齐避免重叠
    
    # 设置y轴标签字体大小
    plt.setp(ax.yaxis.get_majorticklabels(), fontsize=10)

def test_improved_chart(df):
    """测试改进的图表"""
    print("生成改进的测试图表...")
    
    # 创建图表，使用改进的配置
    fig, (ax1, ax2) = plt.subplots(
        2, 1, 
        figsize=(14, 9),
        gridspec_kw={'height_ratios': [3, 1], 'hspace': 0.3}
    )
    
    # 设置整体布局
    fig.subplots_adjust(
        left=0.1,
        right=0.95,
        top=0.9,
        bottom=0.15
    )
    
    # 价格线图
    ax1.plot(df['date'], df['close_price'], label='收盘价', 
            linewidth=2, alpha=0.8, color='blue')
    ax1.plot(df['date'], df['open_price'], label='开盘价', 
            linewidth=1, alpha=0.6, color='green')
    
    ax1.set_title('AAPL 股价走势图 - 中文字符测试', 
                 fontsize=16, fontweight='bold')
    ax1.set_ylabel('价格 ($)', fontsize=12)
    ax1.legend(fontsize=10)
    ax1.grid(True, alpha=0.3)
    
    # 成交量图
    ax2.bar(df['date'], df['volume'], 
           alpha=0.6, color='orange')
    ax2.set_ylabel('成交量', fontsize=12)
    ax2.set_xlabel('日期', fontsize=12)
    
    # 改进的日期轴格式化
    format_date_axis(ax1, df['date'])
    format_date_axis(ax2, df['date'])
    
    # 使用改进的布局
    plt.tight_layout(pad=0.3)
    
    # 保存图表
    os.makedirs('results', exist_ok=True)
    chart_path = f"results/test_improved_chart_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
    
    plt.savefig(chart_path, 
               dpi=300, 
               bbox_inches='tight',
               pad_inches=0.2,
               facecolor='white',
               edgecolor='none')
    
    print(f"改进的图表已保存至: {chart_path}")
    plt.close()
    
    return chart_path

def test_chinese_characters():
    """测试中文字符显示"""
    print("测试中文字符显示...")
    
    # 创建测试数据
    categories = ['智能体A', '智能体B', '智能体C', '智能体D', '智能体E']
    values = [0.85, 0.72, 0.91, 0.68, 0.79]
    
    fig, ax = plt.subplots(figsize=(10, 6))
    
    bars = ax.bar(categories, values, alpha=0.7, color=['red', 'green', 'blue', 'orange', 'purple'])
    
    ax.set_title('智能体贡献度分析 - 中文字符测试', fontsize=16, fontweight='bold')
    ax.set_ylabel('贡献度分数', fontsize=12)
    ax.set_xlabel('智能体名称', fontsize=12)
    
    # 在柱状图上添加数值标签
    for bar, value in zip(bars, values):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                f'{value:.2f}', ha='center', va='bottom', fontsize=10)
    
    # 设置y轴范围
    ax.set_ylim(0, 1.0)
    
    # 旋转x轴标签避免重叠
    plt.setp(ax.xaxis.get_majorticklabels(), rotation=45, ha='right')
    
    plt.tight_layout()
    
    # 保存图表
    chart_path = f"results/test_chinese_chars_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
    plt.savefig(chart_path, dpi=300, bbox_inches='tight', facecolor='white')
    
    print(f"中文字符测试图表已保存至: {chart_path}")
    plt.close()
    
    return chart_path

def main():
    """主函数"""
    print("=" * 60)
    print("可视化修复测试")
    print("=" * 60)
    
    # 设置中文字体
    primary_font = setup_chinese_fonts()
    
    # 创建测试数据
    df = create_test_data()
    print(f"已创建测试数据: {len(df)} 个数据点")
    
    # 测试改进的图表
    chart1 = test_improved_chart(df)
    
    # 测试中文字符显示
    chart2 = test_chinese_characters()
    
    print("=" * 60)
    print("测试完成!")
    print(f"生成的图表:")
    print(f"  1. 改进的股价图表: {chart1}")
    print(f"  2. 中文字符测试图表: {chart2}")
    print("=" * 60)
    print("请检查生成的图表是否解决了以下问题:")
    print("  ✓ 中文字符正确显示")
    print("  ✓ 文本标签无重叠")
    print("  ✓ 图表布局合理")
    print("  ✓ 日期轴格式清晰")

if __name__ == "__main__":
    main()
