"""
交易模拟引擎 (Trading Simulation Engine)

本模块实现了针对特定智能体联盟的交易模拟功能，用于计算联盟的特征函数值v(S)。
通过集成交易环境和智能体协调逻辑，为Shapley值计算提供准确的性能评估。

主要功能：
1. 为指定联盟运行完整的交易模拟
2. 按依赖关系动态执行智能体
3. 利用分析缓存避免重复计算
4. 计算并返回夏普比率作为特征函数值
"""

import numpy as np
import pandas as pd
import time
import copy
from typing import Dict, Any, List, Set, Optional, Union
from datetime import datetime
import logging

# 导入项目依赖
from stock_trading_env import StockTradingEnv
from .analysis_cache import AnalysisCache


class TradingSimulator:
    """
    交易模拟器
    
    负责为特定智能体联盟运行交易模拟，计算联盟的性能指标。
    支持缓存优化和依赖关系管理，确保模拟的准确性和效率。
    """
    
    def __init__(self, 
                 base_config: Dict[str, Any],
                 logger: Optional[logging.Logger] = None):
        """
        初始化交易模拟器
        
        参数:
            base_config: 基础配置，包含交易环境配置
            logger: 日志记录器，如果为None则创建默认记录器
        """
        self.logger = logger or self._create_default_logger()
        self.base_config = base_config
        
        # 周期性评估配置
        self.weekly_evaluation_enabled = base_config.get("weekly_evaluation_enabled", True)
        self.trading_days_per_week = base_config.get("trading_days_per_week", 5)
        self.weekly_shapley_callback = None  # 周末Shapley值计算回调函数
        
        # 周期性评估统计
        self._weekly_stats = {
            "completed_weeks": 0,
            "shapley_calculations": 0,
            "strategy_adjustments": 0,
            "last_weekly_evaluation": None
        }
        
        # 模拟统计信息
        self._stats = {
            "total_simulations": 0,
            "successful_simulations": 0,
            "failed_simulations": 0,
            "total_simulation_time": 0.0,
            "average_simulation_time": 0.0,
            "last_simulation": None
        }
        
        # 智能体依赖关系图（与CoalitionManager保持一致）
        self.agent_graph = {
            "NAA": [],  # 新闻分析智能体，无依赖
            "TAA": [],  # 技术分析智能体，无依赖
            "FAA": [],  # 基本面分析智能体，无依赖
            "BOA": ["NAA", "TAA", "FAA"],  # 看多展望智能体，依赖分析层
            "BeOA": ["NAA", "TAA", "FAA"], # 看空展望智能体，依赖分析层
            "NOA": ["NAA", "TAA", "FAA"],  # 中立观察智能体，依赖分析层
            "TRA": ["BOA", "BeOA", "NOA"]  # 交易智能体，依赖展望层
        }
        
        # 智能体层级（用于按序执行）
        self.agent_layers = [
            ["NAA", "TAA", "FAA"],  # 分析层
            ["BOA", "BeOA", "NOA"], # 展望层
            ["TRA"]                 # 决策层
        ]
        
        # 风险无关收益率（用于夏普比率计算）
        self.risk_free_rate = base_config.get("risk_free_rate", 0.02)  # 默认2%年化
        
        self.logger.info("交易模拟器初始化完成")
    
    def _create_default_logger(self) -> logging.Logger:
        """创建默认日志记录器"""
        logger = logging.getLogger(f"{__name__}.TradingSimulator")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def run_simulation_for_coalition(self, 
                                   coalition: Union[Set[str], List[str]], 
                                   analysis_cache: AnalysisCache,
                                   agents: Optional[Dict[str, Any]] = None,
                                   simulation_days: Optional[int] = None) -> float:
        """
        为指定联盟运行交易模拟并计算夏普比率
        
        这是核心方法，执行以下步骤：
        1. 初始化交易环境
        2. 按依赖顺序执行联盟中的智能体
        3. 运行完整的交易周期（支持周期性评估）
        4. 计算并返回夏普比率
        
        参数:
            coalition: 智能体联盟（智能体ID集合或列表）
            analysis_cache: 已填充的分析缓存
            agents: 智能体实例字典，如果为None则使用模拟智能体
            simulation_days: 模拟天数，如果为None则使用配置中的默认值
            
        返回:
            联盟的夏普比率（特征函数值v(S)）
        """
        start_time = time.time()
        coalition_set = set(coalition) if isinstance(coalition, list) else coalition
        
        self.logger.info(f"开始联盟交易模拟: {coalition_set}")
        
        try:
            # 验证联盟有效性
            if not self._validate_coalition(coalition_set):
                self.logger.warning(f"无效联盟: {coalition_set}")
                return 0.0
            
            # 初始化交易环境
            env = self._create_trading_environment(simulation_days)
            
            # 收集每日收益率和周级数据
            daily_returns = []
            weekly_data = []
            current_week_returns = []
            
            # 运行交易模拟
            # 修复：使用 env.total_days - 1 避免索引越界
            for day in range(env.total_days - 1):
                try:
                    # 获取当前状态
                    state = env.get_state()

                    # 检查是否需要周期性重新评估（每周开始时）
                    if self._should_refresh_cache(day):
                        self.logger.info(f"第 {day + 1} 天开始周期性重新评估")
                        self._refresh_analysis_cache(analysis_cache, state, agents)

                    # 执行联盟中的智能体（启用每日LLM）
                    agent_outputs = self._execute_coalition_agents(
                        coalition_set, state, analysis_cache, agents, use_daily_llm=True
                    )

                    # 获取交易决策
                    trading_action = self._get_trading_action(agent_outputs, coalition_set)

                    # 执行交易
                    _, _, done, info = env.step(trading_action)

                    # 记录日收益率
                    daily_return = info.get("daily_return", 0.0)
                    daily_returns.append(daily_return)
                    current_week_returns.append(daily_return)

                    # 检查是否完成一周的交易
                    if self._is_week_end(day):
                        week_data = self._process_weekly_data(
                            current_week_returns, coalition_set, day
                        )
                        weekly_data.append(week_data)
                        
                        # 执行周末Shapley值计算
                        if self.weekly_evaluation_enabled:
                            self._trigger_weekly_shapley_calculation(
                                coalition_set, week_data, weekly_data
                            )
                        
                        current_week_returns = []  # 重置周收益率

                    if done:
                        break

                except Exception as e:
                    self.logger.error(f"第 {day + 1} 天模拟失败: {e}")
                    daily_returns.append(0.0)  # 失败时记录0收益
                    current_week_returns.append(0.0)
            
            # 处理最后不完整的周
            if current_week_returns:
                week_data = self._process_weekly_data(
                    current_week_returns, coalition_set, len(daily_returns) - 1
                )
                weekly_data.append(week_data)
                
                if self.weekly_evaluation_enabled:
                    self._trigger_weekly_shapley_calculation(
                        coalition_set, week_data, weekly_data
                    )
            
            # 计算夏普比率
            sharpe_ratio = self._calculate_sharpe_ratio(daily_returns)
            
            # 更新统计信息
            simulation_time = time.time() - start_time
            self._update_stats(simulation_time, success=True)
            
            self.logger.info(f"联盟 {coalition_set} 模拟完成: 夏普比率 = {sharpe_ratio:.4f}, 完成周数 = {len(weekly_data)}")
            
            # 返回增强格式，包含每日收益数据
            return {
                "sharpe_ratio": sharpe_ratio,
                "daily_returns": daily_returns,
                "weekly_data": weekly_data,
                "total_days": len(daily_returns),
                "simulation_time": simulation_time
            }
            
        except Exception as e:
            self.logger.error(f"联盟 {coalition_set} 模拟失败: {e}")
            simulation_time = time.time() - start_time
            self._update_stats(simulation_time, success=False)
            return {
                "sharpe_ratio": 0.0,
                "daily_returns": [],
                "weekly_data": [],
                "total_days": 0,
                "simulation_time": simulation_time,
                "error": str(e)
            }
    
    def _validate_coalition(self, coalition: Set[str]) -> bool:
        """
        验证联盟的有效性
        
        检查联盟是否满足基本约束：
        1. 必须包含TRA（交易智能体）
        2. 必须至少包含一个分析智能体
        
        参数:
            coalition: 智能体联盟
            
        返回:
            联盟是否有效
        """
        # 检查是否包含交易智能体
        if "TRA" not in coalition:
            return False
        
        # 检查是否包含至少一个分析智能体
        analyst_agents = {"NAA", "TAA", "FAA"}
        if not coalition.intersection(analyst_agents):
            return False
        
        return True
    
    def _create_trading_environment(self, simulation_days: Optional[int] = None) -> StockTradingEnv:
        """
        创建交易环境实例
        
        参数:
            simulation_days: 模拟天数
            
        返回:
            交易环境实例
        """
        # 复制基础配置
        env_config = copy.deepcopy(self.base_config)
        
        # 设置模拟天数（如果指定）
        if simulation_days is not None:
            # 调整结束日期以匹配模拟天数
            start_date = pd.to_datetime(env_config.get("start_date", "2023-01-01"))
            end_date = start_date + pd.Timedelta(days=simulation_days * 1.5)  # 考虑非交易日
            env_config["end_date"] = end_date.strftime("%Y-%m-%d")
        
        # 创建环境实例
        env = StockTradingEnv(env_config)
        return env
    
    def _execute_coalition_agents(self, 
                                coalition: Set[str], 
                                state: Dict[str, Any], 
                                analysis_cache: AnalysisCache,
                                agents: Optional[Dict[str, Any]] = None,
                                use_daily_llm: bool = True) -> Dict[str, Any]:
        """
        按依赖顺序执行联盟中的智能体
        
        参数:
            coalition: 智能体联盟
            state: 当前环境状态
            analysis_cache: 分析缓存
            agents: 智能体实例字典
            use_daily_llm: 是否使用每日LLM分析
            
        返回:
            智能体输出字典
        """
        agent_outputs = {}
        
        # 按层级执行智能体
        for layer in self.agent_layers:
            for agent_id in layer:
                if agent_id not in coalition:
                    continue
                
                try:
                    if agent_id in ["NAA", "TAA", "FAA"] and not use_daily_llm:
                        # 传统模式：分析层智能体从缓存获取结果
                        cached_result = analysis_cache.get(agent_id)
                        if cached_result is not None:
                            agent_outputs[agent_id] = cached_result
                        else:
                            self.logger.warning(f"分析智能体 {agent_id} 缓存未命中")
                            agent_outputs[agent_id] = self._get_default_analysis_output(agent_id)
                    
                    else:
                        # 每日LLM模式：所有智能体都实时执行
                        if agents and agent_id in agents:
                            # 使用真实智能体
                            enhanced_state = self._enhance_state_with_outputs(state, agent_outputs)
                            
                            self.logger.info(f"🤖 执行智能体 {agent_id} (使用LLM)")
                            agent_output = agents[agent_id].process(enhanced_state)
                            agent_outputs[agent_id] = agent_output
                            
                            # 如果是交易智能体，显示交易决策
                            if agent_id == "TRA" and "action" in agent_output:
                                action = agent_output.get("action", "hold")
                                confidence = agent_output.get("confidence", 0.0)
                                reasoning = agent_output.get("reasoning", "无理由")
                                self.logger.info(f"📊 交易决策: {action} (信心度: {confidence:.2f})")
                                self.logger.info(f"📝 决策理由: {reasoning}")
                        else:
                            # 使用模拟智能体
                            agent_outputs[agent_id] = self._simulate_agent_output(
                                agent_id, state, agent_outputs
                            )
                
                except Exception as e:
                    self.logger.error(f"智能体 {agent_id} 执行失败: {e}")
                    agent_outputs[agent_id] = self._get_default_agent_output(agent_id)
        
        return agent_outputs
    
    def _enhance_state_with_outputs(self, 
                                  state: Dict[str, Any], 
                                  agent_outputs: Dict[str, Any]) -> Dict[str, Any]:
        """
        将智能体输出添加到状态中
        
        参数:
            state: 原始状态
            agent_outputs: 智能体输出
            
        返回:
            增强后的状态
        """
        enhanced_state = copy.deepcopy(state)
        enhanced_state["previous_outputs"] = agent_outputs
        return enhanced_state
    
    def _get_trading_action(self, 
                          agent_outputs: Dict[str, Any], 
                          coalition: Set[str]) -> Dict[str, float]:
        """
        从智能体输出中提取交易动作
        
        参数:
            agent_outputs: 智能体输出字典
            coalition: 智能体联盟
            
        返回:
            交易动作字典
        """
        if "TRA" not in coalition or "TRA" not in agent_outputs:
            # 如果没有交易智能体，返回持有动作
            return {"__HOLD__": 1.0}
        
        tra_output = agent_outputs["TRA"]
        
        # 处理不同格式的TRA输出
        if isinstance(tra_output, dict):
            # 优先使用LLM智能体生成的trading_actions
            if "trading_actions" in tra_output:
                return tra_output["trading_actions"]
            elif "actions" in tra_output:
                return tra_output["actions"]
            elif "action" in tra_output:
                # LLM格式的动作
                action = tra_output["action"].lower()
                position_size = tra_output.get("position_size", 0.5)  # 默认半仓
                
                if action == "buy":
                    return {"AAPL": position_size}
                elif action == "sell":
                    return {"AAPL": -position_size}
                else:  # hold
                    return {"__HOLD__": 1.0}
        
        # 默认持有
        return {"__HOLD__": 1.0}
    
    def _calculate_sharpe_ratio(self, daily_returns: List[float]) -> float:
        """
        计算夏普比率
        
        参数:
            daily_returns: 每日收益率列表
            
        返回:
            夏普比率
        """
        if not daily_returns or len(daily_returns) < 2:
            return 0.0
        
        returns_array = np.array(daily_returns)
        
        # 计算年化收益率和波动率
        mean_return = np.mean(returns_array) * 252  # 年化收益率
        std_return = np.std(returns_array) * np.sqrt(252)  # 年化波动率
        
        # 计算夏普比率
        if std_return == 0:
            return 0.0
        
        sharpe_ratio = (mean_return - self.risk_free_rate) / std_return
        return sharpe_ratio
    
    def _simulate_agent_output(self,
                             agent_id: str,
                             state: Dict[str, Any],
                             previous_outputs: Dict[str, Any]) -> Dict[str, Any]:
        """
        模拟智能体输出（当真实智能体不可用时）

        参数:
            agent_id: 智能体ID
            state: 当前状态
            previous_outputs: 之前的智能体输出

        返回:
            模拟的智能体输出
        """
        # 使用参数避免未使用警告
        _ = state, previous_outputs

        if agent_id in ["BOA", "BeOA", "NOA"]:
            # 展望层智能体的模拟输出
            return {
                "outlook": "neutral",
                "confidence": 0.5,
                "reasoning": f"模拟的{agent_id}输出"
            }
        elif agent_id == "TRA":
            # 交易智能体的模拟输出
            return {
                "action": "hold",
                "confidence": 0.5,
                "reasoning": "模拟的交易决策"
            }
        else:
            return self._get_default_agent_output(agent_id)
    
    def _get_default_analysis_output(self, agent_id: str) -> Dict[str, Any]:
        """获取分析智能体的默认输出"""
        if agent_id == "NAA":
            return {"sentiment": 0.0, "summary": "无新闻数据"}
        elif agent_id == "TAA":
            return {"trend": "neutral", "indicators": {}}
        elif agent_id == "FAA":
            return {"valuation": "fair", "metrics": {}}
        else:
            return {}
    
    def _get_default_agent_output(self, agent_id: str) -> Dict[str, Any]:
        """获取智能体的默认输出"""
        return {
            "agent_id": agent_id,
            "output": "default",
            "confidence": 0.0,
            "reasoning": f"默认输出 - {agent_id}"
        }
    
    def _update_stats(self, simulation_time: float, success: bool) -> None:
        """更新模拟统计信息"""
        self._stats["total_simulations"] += 1
        self._stats["total_simulation_time"] += simulation_time
        self._stats["last_simulation"] = datetime.now()
        
        if success:
            self._stats["successful_simulations"] += 1
        else:
            self._stats["failed_simulations"] += 1
        
        # 计算平均模拟时间
        if self._stats["total_simulations"] > 0:
            self._stats["average_simulation_time"] = (
                self._stats["total_simulation_time"] / self._stats["total_simulations"]
            )
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取模拟统计信息
        
        返回:
            包含统计信息的字典
        """
        stats = self._stats.copy()
        if stats["total_simulations"] > 0:
            stats["success_rate"] = (
                stats["successful_simulations"] / stats["total_simulations"]
            ) * 100
        else:
            stats["success_rate"] = 0.0
        
        # 添加周期性评估统计
        stats["weekly_stats"] = self._weekly_stats.copy()
        return stats
    
    def set_weekly_shapley_callback(self, callback_func):
        """
        设置周末Shapley值计算回调函数
        
        参数:
            callback_func: 回调函数，接收(coalition, week_data, all_weekly_data)参数
        """
        self.weekly_shapley_callback = callback_func
        self.logger.info("已设置周末Shapley值计算回调函数")
    
    def _should_refresh_cache(self, day: int) -> bool:
        """
        判断是否应该刷新分析缓存
        
        参数:
            day: 当前交易日（从0开始）
            
        返回:
            是否需要刷新缓存
        """
        # 每周的第一个交易日刷新缓存（除了第0天）
        return day > 0 and (day % self.trading_days_per_week == 0)
    
    def _is_week_end(self, day: int) -> bool:
        """
        判断是否为一周的最后一个交易日
        
        参数:
            day: 当前交易日（从0开始）
            
        返回:
            是否为周末
        """
        return (day + 1) % self.trading_days_per_week == 0
    
    def _refresh_analysis_cache(self, 
                               analysis_cache: AnalysisCache, 
                               current_state: Dict[str, Any], 
                               agents: Optional[Dict[str, Any]]) -> None:
        """
        刷新分析缓存，重新运行分析智能体
        
        参数:
            analysis_cache: 分析缓存实例
            current_state: 当前市场状态
            agents: 智能体实例字典
        """
        try:
            # 如果有真实智能体，使用refresh_cache方法
            if agents:
                analyst_agents = {k: v for k, v in agents.items() 
                                if k in ["NAA", "TAA", "FAA"]}
                if analyst_agents:
                    refresh_result = analysis_cache.refresh_cache(analyst_agents, current_state)
                    
                    # 记录刷新结果
                    successful_count = len(refresh_result.get("successful_agents", []))
                    failed_count = len(refresh_result.get("failed_agents", []))
                    execution_time = refresh_result.get("execution_time", 0)
                    
                    self.logger.info(
                        f"缓存刷新完成: 成功 {successful_count} 个, "
                        f"失败 {failed_count} 个, 耗时 {execution_time:.2f}s"
                    )
            else:
                # 使用模拟数据重新填充缓存
                self.logger.info("使用模拟数据刷新分析缓存...")
                analysis_cache.clear()
                
                for agent_id in ["NAA", "TAA", "FAA"]:
                    simulated_output = self._get_default_analysis_output(agent_id)
                    # 添加刷新特定信息
                    simulated_output["refresh_timestamp"] = datetime.now().isoformat()
                    simulated_output["market_state"] = current_state.get("current_price", 0.0)
                    simulated_output["refresh_type"] = "simulated"
                    analysis_cache.store(agent_id, simulated_output)
                
                # 手动更新刷新统计
                analysis_cache._stats["refresh_count"] += 1
                analysis_cache._stats["last_refresh"] = datetime.now()
                
                self.logger.info("模拟数据缓存刷新完成")
            
        except Exception as e:
            self.logger.error(f"刷新分析缓存失败: {e}")
    
    def _process_weekly_data(self, 
                           week_returns: List[float], 
                           coalition: Set[str], 
                           end_day: int) -> Dict[str, Any]:
        """
        处理一周的交易数据
        
        参数:
            week_returns: 一周的每日收益率
            coalition: 当前联盟
            end_day: 周结束日
            
        返回:
            周级数据汇总
        """
        if not week_returns:
            return {
                "week_number": (end_day // self.trading_days_per_week) + 1,
                "coalition": list(coalition),
                "trading_days": 0,
                "total_return": 0.0,
                "average_daily_return": 0.0,
                "volatility": 0.0,
                "sharpe_ratio": 0.0,
                "end_day": end_day
            }
        
        week_returns_array = np.array(week_returns)
        total_return = np.sum(week_returns_array)
        avg_return = np.mean(week_returns_array)
        volatility = np.std(week_returns_array) if len(week_returns) > 1 else 0.0
        
        # 计算周级夏普比率
        if volatility > 0:
            weekly_sharpe = (avg_return * 252 - self.risk_free_rate) / (volatility * np.sqrt(252))
        else:
            weekly_sharpe = 0.0
        
        return {
            "week_number": (end_day // self.trading_days_per_week) + 1,
            "coalition": list(coalition),
            "trading_days": len(week_returns),
            "total_return": total_return,
            "average_daily_return": avg_return,
            "volatility": volatility,
            "sharpe_ratio": weekly_sharpe,
            "daily_returns": week_returns.copy(),
            "end_day": end_day,
            "timestamp": datetime.now().isoformat()
        }
    
    def _trigger_weekly_shapley_calculation(self, 
                                          coalition: Set[str], 
                                          week_data: Dict[str, Any], 
                                          all_weekly_data: List[Dict[str, Any]]) -> None:
        """
        触发周末Shapley值计算
        
        参数:
            coalition: 当前联盟
            week_data: 当前周数据
            all_weekly_data: 所有周数据列表
        """
        try:
            self.logger.info(f"触发第 {week_data['week_number']} 周Shapley值计算")
            
            # 更新统计信息
            self._weekly_stats["completed_weeks"] += 1
            self._weekly_stats["last_weekly_evaluation"] = datetime.now()
            
            # 如果设置了回调函数，调用它
            if self.weekly_shapley_callback:
                try:
                    self.weekly_shapley_callback(coalition, week_data, all_weekly_data)
                    self._weekly_stats["shapley_calculations"] += 1
                    self.logger.info("周末Shapley值计算回调执行成功")
                except Exception as e:
                    self.logger.error(f"周末Shapley值计算回调执行失败: {e}")
            else:
                # 默认行为：记录周级性能分析
                self._log_weekly_performance_analysis(coalition, week_data, all_weekly_data)
                self._weekly_stats["shapley_calculations"] += 1
            
        except Exception as e:
            self.logger.error(f"触发周末Shapley值计算失败: {e}")
    
    def _log_weekly_performance_analysis(self, 
                                       coalition: Set[str], 
                                       week_data: Dict[str, Any], 
                                       all_weekly_data: List[Dict[str, Any]]) -> None:
        """
        记录周级性能分析日志
        
        参数:
            coalition: 联盟
            week_data: 当前周数据
            all_weekly_data: 所有周数据
        """
        week_num = week_data["week_number"]
        sharpe = week_data["sharpe_ratio"]
        total_return = week_data["total_return"]
        
        self.logger.info("=" * 60)
        self.logger.info(f"第 {week_num} 周性能分析 - 联盟: {coalition}")
        self.logger.info(f"周总收益率: {total_return:.4f}")
        self.logger.info(f"周夏普比率: {sharpe:.4f}")
        self.logger.info(f"交易天数: {week_data['trading_days']}")
        
        # 与历史周表现比较
        if len(all_weekly_data) > 1:
            historical_sharpes = [w["sharpe_ratio"] for w in all_weekly_data[:-1]]
            avg_historical_sharpe = np.mean(historical_sharpes)
            performance_trend = "改善" if sharpe > avg_historical_sharpe else "下降"
            
            self.logger.info(f"相比历史均值 ({avg_historical_sharpe:.4f})：{performance_trend}")
            
            # 策略诊断建议
            if sharpe < avg_historical_sharpe * 0.8:
                self.logger.warning("⚠️  策略性能显著下降，建议进行Shapley值深度分析")
                self._weekly_stats["strategy_adjustments"] += 1
        
        self.logger.info("=" * 60)
