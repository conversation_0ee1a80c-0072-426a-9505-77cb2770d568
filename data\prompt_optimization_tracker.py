#!/usr/bin/env python3
"""
提示词优化跟踪器 (Prompt Optimization Tracker)

扩展现有的OPRO系统，提供增强的提示词优化跟踪功能：
1. 原始提示词保存和版本管理
2. 优化历史记录和性能跟踪
3. 优化原因分析和决策记录
4. 性能指标对比和趋势分析

作者: AI Assistant
创建时间: 2025-07-04
"""

import os
import json
import logging
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
import uuid

from .comprehensive_storage_manager import (
    ComprehensiveStorageManager,
    PromptOptimizationRecord
)

@dataclass
class PromptVersion:
    """提示词版本信息"""
    version_id: str
    prompt_text: str
    timestamp: str
    source: str  # "original", "opro", "manual", "ab_test"
    performance_metrics: Dict[str, float]
    metadata: Dict[str, Any]

@dataclass
class OptimizationAnalysis:
    """优化分析结果"""
    optimization_id: str
    agent_id: str
    analysis_timestamp: str
    performance_improvement: float
    key_changes: List[str]
    optimization_strategy: str
    confidence_score: float
    recommendations: List[str]

class PromptOptimizationTracker:
    """
    提示词优化跟踪器
    
    提供全面的提示词优化跟踪和分析功能
    """
    
    def __init__(self, 
                 storage_manager: ComprehensiveStorageManager,
                 logger: Optional[logging.Logger] = None):
        """
        初始化提示词优化跟踪器
        
        参数:
            storage_manager: 存储管理器实例
            logger: 日志记录器
        """
        self.storage_manager = storage_manager
        self.logger = logger or self._create_default_logger()
        
        # 提示词版本缓存
        self.prompt_versions = {}  # {agent_id: [PromptVersion]}
        self.current_prompts = {}  # {agent_id: current_prompt_text}
        
        # 加载现有数据
        self._load_existing_data()
        
        self.logger.info("提示词优化跟踪器初始化完成")
    
    def _create_default_logger(self) -> logging.Logger:
        """创建默认日志记录器"""
        logger = logging.getLogger(f"{__name__}.PromptOptimizationTracker")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def _load_existing_data(self):
        """加载现有的提示词数据"""
        try:
            # 从存储管理器加载提示词优化记录
            optimizations = self.storage_manager.get_prompt_optimizations()
            
            for opt in optimizations:
                agent_id = opt["agent_id"]
                if agent_id not in self.prompt_versions:
                    self.prompt_versions[agent_id] = []
                
                # 添加原始版本（如果不存在）
                if opt["original_prompt"] and not any(
                    v.prompt_text == opt["original_prompt"] 
                    for v in self.prompt_versions[agent_id]
                ):
                    original_version = PromptVersion(
                        version_id=f"{agent_id}_original_{opt['optimization_id'][:8]}",
                        prompt_text=opt["original_prompt"],
                        timestamp=opt["timestamp"],
                        source="original",
                        performance_metrics={},
                        metadata={"optimization_id": opt["optimization_id"]}
                    )
                    self.prompt_versions[agent_id].append(original_version)
                
                # 添加优化版本
                if opt["optimized_prompt"]:
                    optimized_version = PromptVersion(
                        version_id=f"{agent_id}_optimized_{opt['optimization_id'][:8]}",
                        prompt_text=opt["optimized_prompt"],
                        timestamp=opt["timestamp"],
                        source="opro",
                        performance_metrics=opt.get("performance_metrics", {}),
                        metadata={
                            "optimization_id": opt["optimization_id"],
                            "optimization_reason": opt.get("optimization_reason", "")
                        }
                    )
                    self.prompt_versions[agent_id].append(optimized_version)
                    
                    # 更新当前提示词
                    self.current_prompts[agent_id] = opt["optimized_prompt"]
            
            self.logger.info(f"加载了 {len(self.prompt_versions)} 个智能体的提示词历史")
            
        except Exception as e:
            self.logger.error(f"加载现有提示词数据失败: {e}")
    
    def track_original_prompt(self, agent_id: str, prompt_text: str, metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        跟踪原始提示词
        
        参数:
            agent_id: 智能体ID
            prompt_text: 提示词文本
            metadata: 元数据
            
        返回:
            版本ID
        """
        try:
            version_id = f"{agent_id}_original_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            version = PromptVersion(
                version_id=version_id,
                prompt_text=prompt_text,
                timestamp=datetime.now().isoformat(),
                source="original",
                performance_metrics={},
                metadata=metadata or {}
            )
            
            # 添加到版本历史
            if agent_id not in self.prompt_versions:
                self.prompt_versions[agent_id] = []
            
            self.prompt_versions[agent_id].append(version)
            self.current_prompts[agent_id] = prompt_text
            
            self.logger.info(f"跟踪原始提示词: {agent_id} -> {version_id}")
            return version_id
            
        except Exception as e:
            self.logger.error(f"跟踪原始提示词失败: {e}")
            return ""
    
    def track_optimization(self, 
                         agent_id: str,
                         original_prompt: str,
                         optimized_prompt: str,
                         optimization_reason: str,
                         performance_metrics: Dict[str, float],
                         optimization_metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        跟踪提示词优化
        
        参数:
            agent_id: 智能体ID
            original_prompt: 原始提示词
            optimized_prompt: 优化后提示词
            optimization_reason: 优化原因
            performance_metrics: 性能指标
            optimization_metadata: 优化元数据
            
        返回:
            优化记录ID
        """
        try:
            optimization_id = f"opt_{agent_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{uuid.uuid4().hex[:8]}"
            
            # 创建优化记录
            optimization_record = PromptOptimizationRecord(
                optimization_id=optimization_id,
                agent_id=agent_id,
                timestamp=datetime.now().isoformat(),
                original_prompt=original_prompt,
                optimized_prompt=optimized_prompt,
                optimization_reason=optimization_reason,
                performance_metrics=performance_metrics,
                version_info={
                    "original_version": self._get_prompt_hash(original_prompt),
                    "optimized_version": self._get_prompt_hash(optimized_prompt),
                    "optimization_type": "opro"
                },
                metadata=optimization_metadata or {}
            )
            
            # 保存到存储管理器
            success = self.storage_manager.store_prompt_optimization(optimization_record)
            
            if success:
                # 添加到版本历史
                optimized_version = PromptVersion(
                    version_id=f"{agent_id}_optimized_{optimization_id[:8]}",
                    prompt_text=optimized_prompt,
                    timestamp=datetime.now().isoformat(),
                    source="opro",
                    performance_metrics=performance_metrics,
                    metadata={
                        "optimization_id": optimization_id,
                        "optimization_reason": optimization_reason
                    }
                )
                
                if agent_id not in self.prompt_versions:
                    self.prompt_versions[agent_id] = []
                
                self.prompt_versions[agent_id].append(optimized_version)
                self.current_prompts[agent_id] = optimized_prompt
                
                self.logger.info(f"跟踪提示词优化: {agent_id} -> {optimization_id}")
                return optimization_id
            else:
                self.logger.error("保存优化记录失败")
                return ""
                
        except Exception as e:
            self.logger.error(f"跟踪提示词优化失败: {e}")
            return ""
    
    def analyze_optimization_effectiveness(self, agent_id: str, weeks: int = 4) -> OptimizationAnalysis:
        """
        分析优化效果
        
        参数:
            agent_id: 智能体ID
            weeks: 分析周数
            
        返回:
            优化分析结果
        """
        try:
            # 获取最近的优化记录
            end_date = datetime.now()
            start_date = end_date - timedelta(weeks=weeks)
            
            optimizations = self.storage_manager.get_prompt_optimizations(
                agent_id=agent_id,
                start_date=start_date.strftime('%Y-%m-%d'),
                end_date=end_date.strftime('%Y-%m-%d')
            )
            
            if not optimizations:
                return self._create_empty_analysis(agent_id)
            
            # 分析性能改进
            performance_improvements = []
            key_changes = []
            
            for opt in optimizations:
                metrics = opt.get("performance_metrics", {})
                if "improvement" in metrics:
                    performance_improvements.append(metrics["improvement"])
                
                # 分析提示词变化
                changes = self._analyze_prompt_changes(
                    opt.get("original_prompt", ""),
                    opt.get("optimized_prompt", "")
                )
                key_changes.extend(changes)
            
            # 计算平均改进
            avg_improvement = sum(performance_improvements) / len(performance_improvements) if performance_improvements else 0.0
            
            # 生成建议
            recommendations = self._generate_optimization_recommendations(
                agent_id, optimizations, avg_improvement
            )
            
            analysis = OptimizationAnalysis(
                optimization_id=f"analysis_{agent_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                agent_id=agent_id,
                analysis_timestamp=datetime.now().isoformat(),
                performance_improvement=avg_improvement,
                key_changes=list(set(key_changes)),  # 去重
                optimization_strategy="opro_based",
                confidence_score=min(len(optimizations) / 10.0, 1.0),  # 基于优化次数的置信度
                recommendations=recommendations
            )
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"分析优化效果失败: {e}")
            return self._create_empty_analysis(agent_id)
    
    def get_prompt_history(self, agent_id: str) -> List[Dict[str, Any]]:
        """
        获取提示词历史
        
        参数:
            agent_id: 智能体ID
            
        返回:
            提示词历史列表
        """
        try:
            if agent_id not in self.prompt_versions:
                return []
            
            history = []
            for version in sorted(self.prompt_versions[agent_id], key=lambda x: x.timestamp):
                history.append({
                    "version_id": version.version_id,
                    "prompt_text": version.prompt_text,
                    "timestamp": version.timestamp,
                    "source": version.source,
                    "performance_metrics": version.performance_metrics,
                    "metadata": version.metadata,
                    "prompt_length": len(version.prompt_text),
                    "prompt_hash": self._get_prompt_hash(version.prompt_text)
                })
            
            return history
            
        except Exception as e:
            self.logger.error(f"获取提示词历史失败: {e}")
            return []
    
    def compare_prompt_versions(self, agent_id: str, version1_id: str, version2_id: str) -> Dict[str, Any]:
        """
        比较两个提示词版本
        
        参数:
            agent_id: 智能体ID
            version1_id: 版本1 ID
            version2_id: 版本2 ID
            
        返回:
            比较结果
        """
        try:
            versions = self.prompt_versions.get(agent_id, [])
            
            version1 = next((v for v in versions if v.version_id == version1_id), None)
            version2 = next((v for v in versions if v.version_id == version2_id), None)
            
            if not version1 or not version2:
                return {"error": "版本未找到"}
            
            # 文本比较
            text_changes = self._analyze_prompt_changes(version1.prompt_text, version2.prompt_text)
            
            # 性能比较
            performance_comparison = {}
            for metric in set(list(version1.performance_metrics.keys()) + list(version2.performance_metrics.keys())):
                val1 = version1.performance_metrics.get(metric, 0.0)
                val2 = version2.performance_metrics.get(metric, 0.0)
                performance_comparison[metric] = {
                    "version1": val1,
                    "version2": val2,
                    "difference": val2 - val1,
                    "improvement_percentage": ((val2 - val1) / val1 * 100) if val1 != 0 else 0
                }
            
            return {
                "agent_id": agent_id,
                "version1": {
                    "id": version1.version_id,
                    "timestamp": version1.timestamp,
                    "source": version1.source,
                    "length": len(version1.prompt_text)
                },
                "version2": {
                    "id": version2.version_id,
                    "timestamp": version2.timestamp,
                    "source": version2.source,
                    "length": len(version2.prompt_text)
                },
                "text_changes": text_changes,
                "performance_comparison": performance_comparison,
                "overall_improvement": sum(
                    comp["difference"] for comp in performance_comparison.values()
                ) / len(performance_comparison) if performance_comparison else 0
            }
            
        except Exception as e:
            self.logger.error(f"比较提示词版本失败: {e}")
            return {"error": str(e)}
    
    def _get_prompt_hash(self, prompt_text: str) -> str:
        """计算提示词哈希值"""
        return hashlib.md5(prompt_text.encode('utf-8')).hexdigest()
    
    def _analyze_prompt_changes(self, original: str, optimized: str) -> List[str]:
        """分析提示词变化"""
        changes = []
        
        # 长度变化
        len_diff = len(optimized) - len(original)
        if abs(len_diff) > 10:
            changes.append(f"长度变化: {len_diff:+d} 字符")
        
        # 关键词变化
        original_words = set(original.lower().split())
        optimized_words = set(optimized.lower().split())
        
        added_words = optimized_words - original_words
        removed_words = original_words - optimized_words
        
        if added_words:
            changes.append(f"新增关键词: {', '.join(list(added_words)[:5])}")
        
        if removed_words:
            changes.append(f"删除关键词: {', '.join(list(removed_words)[:5])}")
        
        return changes
    
    def _generate_optimization_recommendations(self, agent_id: str, optimizations: List[Dict], avg_improvement: float) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        if avg_improvement > 0.05:
            recommendations.append("优化效果显著，建议继续当前优化策略")
        elif avg_improvement > 0:
            recommendations.append("优化效果一般，建议尝试不同的优化方法")
        else:
            recommendations.append("优化效果不佳，建议重新评估优化策略")
        
        if len(optimizations) < 3:
            recommendations.append("优化次数较少，建议增加优化频率")
        
        return recommendations
    
    def _create_empty_analysis(self, agent_id: str) -> OptimizationAnalysis:
        """创建空的分析结果"""
        return OptimizationAnalysis(
            optimization_id=f"empty_analysis_{agent_id}",
            agent_id=agent_id,
            analysis_timestamp=datetime.now().isoformat(),
            performance_improvement=0.0,
            key_changes=[],
            optimization_strategy="none",
            confidence_score=0.0,
            recommendations=["暂无优化数据，建议开始提示词优化"]
        )
