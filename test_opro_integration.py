#!/usr/bin/env python3
"""
OPRO集成测试脚本 (OPRO Integration Test Script)

测试OPRO优化功能与多智能体交易系统的集成，验证各个组件的功能。

使用方法:
    python test_opro_integration.py --provider zhipuai --test-mode basic
    python test_opro_integration.py --provider zhipuai --test-mode full --enable-opro
    python test_opro_integration.py --provider zhipuai --test-mode opro-only
"""

import argparse
import logging
import json
import os
import sys
from typing import Dict, Any, List
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入核心模块
from contribution_assessment.assessor import ContributionAssessor
from contribution_assessment.llm_interface import LLMInterface

# 尝试导入OPRO模块
try:
    from contribution_assessment.opro_optimizer import OPROOptimizer
    from contribution_assessment.historical_score_manager import HistoricalScoreManager
    from agents.opro_base_agent import OPROBaseAgent
    OPRO_AVAILABLE = True
except ImportError as e:
    print(f"警告: OPRO模块导入失败: {e}")
    OPRO_AVAILABLE = False

def setup_logging(verbose: bool = False) -> logging.Logger:
    """设置日志记录"""
    log_level = logging.DEBUG if verbose else logging.INFO
    
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(f'test_opro_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
        ]
    )
    
    return logging.getLogger(__name__)

def load_opro_config(config_path: str = "config/opro_config.json") -> Dict[str, Any]:
    """加载OPRO配置"""
    try:
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            return config
        else:
            print(f"配置文件不存在: {config_path}，使用默认配置")
            return {}
    except Exception as e:
        print(f"加载配置文件失败: {e}，使用默认配置")
        return {}

def test_basic_system(assessor: ContributionAssessor, logger: logging.Logger) -> Dict[str, Any]:
    """测试基础系统功能（不含OPRO）"""
    logger.info("=" * 60)
    logger.info("测试1: 基础系统功能")
    logger.info("=" * 60)
    
    try:
        # 运行快速测试
        result = assessor.run_quick_test()
        
        if result.get("success", False):
            logger.info("✅ 基础系统测试通过")
            logger.info(f"   Shapley值计算成功: {len(result.get('shapley_values', {}))}")
            logger.info(f"   执行时间: {result.get('execution_time', 0):.2f}s")
            
            return {
                "test_name": "basic_system",
                "success": True,
                "result": result,
                "message": "基础系统功能正常"
            }
        else:
            logger.error("❌ 基础系统测试失败")
            return {
                "test_name": "basic_system",
                "success": False,
                "error": result.get("error", "未知错误"),
                "message": "基础系统功能异常"
            }
            
    except Exception as e:
        logger.error(f"❌ 基础系统测试异常: {e}")
        return {
            "test_name": "basic_system",
            "success": False,
            "error": str(e),
            "message": "基础系统测试异常"
        }

def test_opro_components(assessor: ContributionAssessor, logger: logging.Logger) -> Dict[str, Any]:
    """测试OPRO组件功能"""
    logger.info("=" * 60)
    logger.info("测试2: OPRO组件功能")
    logger.info("=" * 60)
    
    if not assessor.enable_opro:
        logger.warning("❌ OPRO功能未启用，跳过测试")
        return {
            "test_name": "opro_components",
            "success": False,
            "error": "OPRO功能未启用",
            "message": "OPRO组件测试跳过"
        }
    
    try:
        test_results = {}
        
        # 测试历史得分管理器
        logger.info("测试历史得分管理器...")
        if assessor.historical_score_manager:
            summary_stats = assessor.historical_score_manager.get_summary_stats()
            test_results["historical_score_manager"] = {
                "available": True,
                "stats": summary_stats
            }
            logger.info("✅ 历史得分管理器正常")
        else:
            test_results["historical_score_manager"] = {
                "available": False,
                "error": "历史得分管理器未初始化"
            }
            logger.error("❌ 历史得分管理器不可用")
        
        # 测试OPRO优化器
        logger.info("测试OPRO优化器...")
        if assessor.opro_optimizer:
            optimizer_stats = assessor.opro_optimizer.get_stats()
            test_results["opro_optimizer"] = {
                "available": True,
                "stats": optimizer_stats
            }
            logger.info("✅ OPRO优化器正常")
        else:
            test_results["opro_optimizer"] = {
                "available": False,
                "error": "OPRO优化器未初始化"
            }
            logger.error("❌ OPRO优化器不可用")
        
        # 测试仪表板数据
        logger.info("测试OPRO仪表板...")
        dashboard_data = assessor.get_opro_dashboard_data()
        test_results["dashboard"] = {
            "available": dashboard_data.get("opro_enabled", False),
            "data": dashboard_data
        }
        
        if dashboard_data.get("opro_enabled", False):
            logger.info("✅ OPRO仪表板正常")
        else:
            logger.error("❌ OPRO仪表板异常")
        
        # 总体评估
        all_components_ok = all(
            result.get("available", False) 
            for result in test_results.values()
        )
        
        return {
            "test_name": "opro_components",
            "success": all_components_ok,
            "test_results": test_results,
            "message": "OPRO组件功能正常" if all_components_ok else "部分OPRO组件异常"
        }
        
    except Exception as e:
        logger.error(f"❌ OPRO组件测试异常: {e}")
        return {
            "test_name": "opro_components",
            "success": False,
            "error": str(e),
            "message": "OPRO组件测试异常"
        }

def test_opro_optimization_cycle(assessor: ContributionAssessor, logger: logging.Logger) -> Dict[str, Any]:
    """测试OPRO优化循环"""
    logger.info("=" * 60)
    logger.info("测试3: OPRO优化循环")
    logger.info("=" * 60)
    
    if not assessor.enable_opro:
        logger.warning("❌ OPRO功能未启用，跳过测试")
        return {
            "test_name": "opro_optimization_cycle",
            "success": False,
            "error": "OPRO功能未启用",
            "message": "OPRO优化循环测试跳过"
        }
    
    try:
        # 选择少量智能体进行测试
        test_agents = ["TAA", "TRA"]  # 技术分析师和交易员
        
        logger.info(f"测试智能体优化: {test_agents}")
        
        # 运行优化循环
        optimization_result = assessor.run_opro_optimization_cycle(
            target_agents=test_agents,
            force_optimization=True
        )
        
        if optimization_result.get("success", False):
            opt_result = optimization_result.get("optimization_result", {})
            successful_optimizations = opt_result.get("successful_optimizations", 0)
            total_agents = opt_result.get("total_agents", 0)
            
            logger.info("✅ OPRO优化循环测试通过")
            logger.info(f"   成功优化: {successful_optimizations}/{total_agents}")
            logger.info(f"   执行时间: {opt_result.get('total_time', 0):.2f}s")
            
            # 显示优化结果详情
            results = opt_result.get("results", {})
            for agent_id, result in results.items():
                if result.get("success", False):
                    improvement = result.get("improvement", 0)
                    logger.info(f"   {agent_id}: 预期改进 {improvement:.6f}")
                else:
                    error = result.get("error", "未知错误")
                    logger.warning(f"   {agent_id}: 优化失败 - {error}")
            
            return {
                "test_name": "opro_optimization_cycle",
                "success": True,
                "result": optimization_result,
                "message": "OPRO优化循环功能正常"
            }
        else:
            logger.error("❌ OPRO优化循环测试失败")
            error = optimization_result.get("error", "未知错误")
            logger.error(f"   错误: {error}")
            
            return {
                "test_name": "opro_optimization_cycle",
                "success": False,
                "error": error,
                "message": "OPRO优化循环功能异常"
            }
            
    except Exception as e:
        logger.error(f"❌ OPRO优化循环测试异常: {e}")
        return {
            "test_name": "opro_optimization_cycle",
            "success": False,
            "error": str(e),
            "message": "OPRO优化循环测试异常"
        }

def test_full_integration(assessor: ContributionAssessor, logger: logging.Logger) -> Dict[str, Any]:
    """测试完整集成功能"""
    logger.info("=" * 60)
    logger.info("测试4: 完整OPRO集成")
    logger.info("=" * 60)
    
    if not assessor.enable_opro:
        logger.warning("❌ OPRO功能未启用，跳过测试")
        return {
            "test_name": "full_integration",
            "success": False,
            "error": "OPRO功能未启用",
            "message": "完整集成测试跳过"
        }
    
    try:
        # 运行集成评估（包含优化）
        result = assessor.run_with_opro_integration(
            target_agents=["TAA", "TRA"],  # 使用少量智能体
            max_coalitions=5,  # 限制联盟数量
            run_optimization_before=False,  # 不进行评估前优化
            run_optimization_after=True   # 进行评估后优化
        )
        
        if result.get("success", False):
            logger.info("✅ 完整OPRO集成测试通过")
            
            # 分析结果
            evaluation_result = result.get("evaluation_result", {})
            optimization_results = result.get("optimization_results", {})
            
            # 评估结果
            if evaluation_result.get("success", False):
                shapley_values = evaluation_result.get("shapley_values", {})
                logger.info(f"   评估成功: {len(shapley_values)} 个智能体")
                for agent_id, score in shapley_values.items():
                    logger.info(f"     {agent_id}: {score:.6f}")
            
            # 优化结果
            post_opt = optimization_results.get("post_evaluation", {})
            if post_opt.get("success", False):
                logger.info("   评估后优化成功")
                updated_agents = post_opt.get("updated_agents", [])
                logger.info(f"   更新智能体: {updated_agents}")
            
            logger.info(f"   总执行时间: {result.get('total_execution_time', 0):.2f}s")
            
            return {
                "test_name": "full_integration",
                "success": True,
                "result": result,
                "message": "完整OPRO集成功能正常"
            }
        else:
            logger.error("❌ 完整OPRO集成测试失败")
            error = result.get("error", "未知错误")
            logger.error(f"   错误: {error}")
            
            return {
                "test_name": "full_integration",
                "success": False,
                "error": error,
                "message": "完整OPRO集成功能异常"
            }
            
    except Exception as e:
        logger.error(f"❌ 完整OPRO集成测试异常: {e}")
        return {
            "test_name": "full_integration",
            "success": False,
            "error": str(e),
            "message": "完整OPRO集成测试异常"
        }

def create_test_agents(llm_interface, logger: logging.Logger) -> Dict[str, Any]:
    """创建测试用的OPRO智能体"""
    logger.info("创建测试智能体...")
    
    try:
        from agents.analyst_agents import TechnicalAnalystAgent
        from agents.trader_agent import TraderAgent
        
        # 创建基于OPRO的测试智能体
        test_agents = {}
        
        # 技术分析师
        if OPRO_AVAILABLE:
            test_agents["TAA"] = OPROBaseAgent(
                agent_id="TAA",
                llm_interface=llm_interface,
                logger=logger,
                opro_enabled=True
            )
            # 设置默认提示词
            test_agents["TAA"].current_prompt = "你是一个专业的技术分析师，通过图表模式和技术指标分析股票趋势。"
        else:
            test_agents["TAA"] = TechnicalAnalystAgent(
                llm_interface=llm_interface,
                logger=logger
            )
        
        # 交易员
        if OPRO_AVAILABLE:
            test_agents["TRA"] = OPROBaseAgent(
                agent_id="TRA", 
                llm_interface=llm_interface,
                logger=logger,
                opro_enabled=True
            )
            test_agents["TRA"].current_prompt = "你是一个专业的交易员，综合各种分析做出最终交易决策。"
        else:
            test_agents["TRA"] = TraderAgent(
                llm_interface=llm_interface,
                logger=logger
            )
        
        logger.info(f"✅ 创建测试智能体成功: {list(test_agents.keys())}")
        return test_agents
        
    except Exception as e:
        logger.error(f"❌ 创建测试智能体失败: {e}")
        return {}

def run_test_suite(test_mode: str, 
                  llm_provider: str, 
                  enable_opro: bool = False,
                  verbose: bool = False) -> Dict[str, Any]:
    """运行测试套件"""
    
    logger = setup_logging(verbose)
    
    logger.info("=" * 80)
    logger.info("OPRO集成测试开始")
    logger.info("=" * 80)
    logger.info(f"测试模式: {test_mode}")
    logger.info(f"LLM提供商: {llm_provider}")
    logger.info(f"OPRO启用: {enable_opro}")
    logger.info(f"OPRO可用: {OPRO_AVAILABLE}")
    
    # 验证OPRO可用性
    if enable_opro and not OPRO_AVAILABLE:
        logger.error("❌ 请求启用OPRO但模块不可用")
        return {
            "success": False,
            "error": "OPRO模块不可用",
            "tests_run": 0,
            "tests_passed": 0
        }
    
    # 加载配置
    opro_config = load_opro_config() if enable_opro else {}
    
    # 创建测试配置
    test_config = {
        "start_date": "2023-01-01",
        "end_date": "2023-01-03",
        "stocks": ["AAPL"],
        "starting_cash": 100000,
        "simulation_days": 2,
        "verbose": verbose,
        "enable_concurrent_execution": False
    }
    
    # 初始化系统
    try:
        # 创建LLM接口
        llm_interface = LLMInterface(provider=llm_provider, logger=logger)
        
        # 创建测试智能体
        test_agents = create_test_agents(llm_interface, logger)
        
        # 创建评估器
        assessor = ContributionAssessor(
            config=test_config,
            agents=test_agents,
            logger=logger,
            llm_provider=llm_provider,
            enable_opro=enable_opro,
            opro_config=opro_config.get("optimization", {}) if opro_config else {}
        )
        
        logger.info("✅ 系统初始化成功")
        
    except Exception as e:
        logger.error(f"❌ 系统初始化失败: {e}")
        return {
            "success": False,
            "error": f"系统初始化失败: {e}",
            "tests_run": 0,
            "tests_passed": 0
        }
    
    # 运行测试
    test_results = []
    
    try:
        if test_mode in ["basic", "full"]:
            # 基础系统测试
            result = test_basic_system(assessor, logger)
            test_results.append(result)
        
        if test_mode in ["opro-only", "full"] and enable_opro:
            # OPRO组件测试
            result = test_opro_components(assessor, logger)
            test_results.append(result)
            
            # OPRO优化循环测试
            result = test_opro_optimization_cycle(assessor, logger)
            test_results.append(result)
            
            # 完整集成测试
            if test_mode == "full":
                result = test_full_integration(assessor, logger)
                test_results.append(result)
        
        # 汇总结果
        tests_run = len(test_results)
        tests_passed = sum(1 for result in test_results if result.get("success", False))
        
        logger.info("=" * 80)
        logger.info("测试结果汇总")
        logger.info("=" * 80)
        
        for result in test_results:
            test_name = result.get("test_name", "未知测试")
            success = result.get("success", False)
            message = result.get("message", "")
            status = "✅ 通过" if success else "❌ 失败"
            logger.info(f"{status} {test_name}: {message}")
            
            if not success and "error" in result:
                logger.info(f"    错误: {result['error']}")
        
        logger.info("-" * 80)
        logger.info(f"总计: {tests_passed}/{tests_run} 测试通过")
        logger.info("=" * 80)
        
        # 生成最终报告
        final_result = {
            "success": tests_passed == tests_run,
            "tests_run": tests_run,
            "tests_passed": tests_passed,
            "test_results": test_results,
            "test_mode": test_mode,
            "opro_enabled": enable_opro,
            "opro_available": OPRO_AVAILABLE,
            "llm_provider": llm_provider,
            "timestamp": datetime.now().isoformat()
        }
        
        return final_result
        
    except Exception as e:
        logger.error(f"❌ 测试执行异常: {e}")
        return {
            "success": False,
            "error": f"测试执行异常: {e}",
            "tests_run": len(test_results),
            "tests_passed": sum(1 for result in test_results if result.get("success", False)),
            "test_results": test_results
        }

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="OPRO集成测试脚本")
    
    parser.add_argument(
        "--provider", 
        type=str, 
        default="zhipuai",
        choices=["zhipuai", "openai"],
        help="LLM提供商"
    )
    
    parser.add_argument(
        "--test-mode",
        type=str,
        default="basic",
        choices=["basic", "opro-only", "full"],
        help="测试模式: basic(基础测试), opro-only(仅OPRO测试), full(完整测试)"
    )
    
    parser.add_argument(
        "--enable-opro",
        action="store_true",
        help="启用OPRO功能"
    )
    
    parser.add_argument(
        "--verbose",
        action="store_true", 
        help="详细日志输出"
    )
    
    parser.add_argument(
        "--output",
        type=str,
        help="测试结果输出文件路径"
    )
    
    args = parser.parse_args()
    
    # 如果测试模式包含OPRO，自动启用OPRO
    if args.test_mode in ["opro-only", "full"]:
        args.enable_opro = True
    
    # 运行测试
    result = run_test_suite(
        test_mode=args.test_mode,
        llm_provider=args.provider,
        enable_opro=args.enable_opro,
        verbose=args.verbose
    )
    
    # 保存结果
    if args.output:
        try:
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)
            print(f"测试结果已保存至: {args.output}")
        except Exception as e:
            print(f"保存测试结果失败: {e}")
    
    # 输出最终状态
    success = result.get("success", False)
    tests_passed = result.get("tests_passed", 0)
    tests_run = result.get("tests_run", 0)
    
    if success:
        print(f"🎉 所有测试通过! ({tests_passed}/{tests_run})")
        sys.exit(0)
    else:
        print(f"❌ 测试失败! ({tests_passed}/{tests_run})")
        if "error" in result:
            print(f"错误: {result['error']}")
        sys.exit(1)

if __name__ == "__main__":
    main()