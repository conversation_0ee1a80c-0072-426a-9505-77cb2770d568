#!/usr/bin/env python3
"""
时间格式标准化工具

统一处理新闻数据中的时间格式，将所有时间转换为ISO 8601标准格式
支持的输入格式：
- 20240106T141242 (15字符)
- 2025-01-01 18:00:19 (19字符)
输出格式：YYYY-MM-DD HH:MM:SS
"""

import re
from datetime import datetime
from typing import Optional, Tuple


class TimeFormatNormalizer:
    """时间格式标准化器"""
    
    # 支持的时间格式模式
    PATTERNS = {
        'compact': r'^(\d{8})T(\d{6})$',  # 20240106T141242
        'iso_like': r'^(\d{4}-\d{2}-\d{2}) (\d{2}:\d{2}:\d{2})$',  # 2025-01-01 18:00:19
        'iso_standard': r'^(\d{4}-\d{2}-\d{2}) (\d{2}:\d{2}:\d{2})$'  # 已经是标准格式
    }
    
    @classmethod
    def detect_format(cls, time_str: str) -> Optional[str]:
        """
        检测时间字符串的格式类型
        
        Args:
            time_str: 时间字符串
            
        Returns:
            格式类型名称，如果无法识别则返回None
        """
        if not time_str or not isinstance(time_str, str):
            return None
            
        time_str = time_str.strip()
        
        for format_name, pattern in cls.PATTERNS.items():
            if re.match(pattern, time_str):
                return format_name
                
        return None
    
    @classmethod
    def normalize_time_format(cls, time_str: str) -> str:
        """
        统一时间格式转换函数
        
        Args:
            time_str: 原始时间字符串
            
        Returns:
            标准化后的时间字符串 (YYYY-MM-DD HH:MM:SS)
            
        Raises:
            ValueError: 如果时间格式无法识别或转换失败
        """
        if not time_str or not isinstance(time_str, str):
            raise ValueError(f"Invalid time string: {time_str}")
            
        time_str = time_str.strip()
        format_type = cls.detect_format(time_str)
        
        if format_type is None:
            raise ValueError(f"Unrecognized time format: {time_str}")
        
        try:
            if format_type == 'compact':
                # 20240106T141242 -> 2024-01-06 14:12:42
                return cls._convert_compact_format(time_str)
            elif format_type in ['iso_like', 'iso_standard']:
                # 2025-01-01 18:00:19 -> 2025-01-01 18:00:19 (已经是标准格式)
                return cls._validate_iso_format(time_str)
            else:
                raise ValueError(f"Unsupported format type: {format_type}")
                
        except Exception as e:
            raise ValueError(f"Failed to convert time '{time_str}': {str(e)}")
    
    @classmethod
    def _convert_compact_format(cls, time_str: str) -> str:
        """
        转换紧凑格式时间
        20240106T141242 -> 2024-01-06 14:12:42
        """
        match = re.match(cls.PATTERNS['compact'], time_str)
        if not match:
            raise ValueError(f"Invalid compact format: {time_str}")
            
        date_part, time_part = match.groups()
        
        # 解析日期部分 YYYYMMDD
        year = date_part[:4]
        month = date_part[4:6]
        day = date_part[6:8]
        
        # 解析时间部分 HHMMSS
        hour = time_part[:2]
        minute = time_part[2:4]
        second = time_part[4:6]
        
        # 验证日期时间的有效性
        datetime_obj = datetime(
            int(year), int(month), int(day),
            int(hour), int(minute), int(second)
        )
        
        return datetime_obj.strftime('%Y-%m-%d %H:%M:%S')
    
    @classmethod
    def _validate_iso_format(cls, time_str: str) -> str:
        """
        验证并返回ISO格式时间
        2025-01-01 18:00:19 -> 2025-01-01 18:00:19
        """
        try:
            # 验证时间格式的有效性
            datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S')
            return time_str
        except ValueError as e:
            raise ValueError(f"Invalid ISO format: {time_str}, error: {str(e)}")
    
    @classmethod
    def batch_normalize(cls, time_strings: list) -> Tuple[list, list]:
        """
        批量标准化时间格式
        
        Args:
            time_strings: 时间字符串列表
            
        Returns:
            (成功转换的列表, 失败的列表)
        """
        success_list = []
        error_list = []
        
        for i, time_str in enumerate(time_strings):
            try:
                normalized = cls.normalize_time_format(time_str)
                success_list.append((i, time_str, normalized))
            except Exception as e:
                error_list.append((i, time_str, str(e)))
                
        return success_list, error_list
    
    @classmethod
    def validate_normalized_format(cls, time_str: str) -> bool:
        """
        验证时间字符串是否为标准化格式
        
        Args:
            time_str: 时间字符串
            
        Returns:
            True如果是标准格式，False否则
        """
        try:
            datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S')
            return True
        except (ValueError, TypeError):
            return False


def main():
    """测试时间格式标准化功能"""
    test_cases = [
        "20240106T141242",
        "2025-01-01 18:00:19",
        "20231225T235959",
        "2024-12-31 23:59:59",
        "invalid_format",
        "",
        None
    ]
    
    print("时间格式标准化测试:")
    print("=" * 50)
    
    normalizer = TimeFormatNormalizer()
    
    for test_case in test_cases:
        try:
            if test_case is None:
                print(f"输入: None -> 错误: Invalid input")
                continue
                
            format_type = normalizer.detect_format(test_case)
            normalized = normalizer.normalize_time_format(test_case)
            print(f"输入: {test_case} ({format_type}) -> 输出: {normalized}")
        except Exception as e:
            print(f"输入: {test_case} -> 错误: {str(e)}")
    
    # 批量测试
    print("\n批量转换测试:")
    print("=" * 50)
    
    batch_test = [
        "20240106T141242",
        "2025-01-01 18:00:19",
        "20231225T235959",
        "invalid_format"
    ]
    
    success_list, error_list = normalizer.batch_normalize(batch_test)
    
    print(f"成功转换 {len(success_list)} 条:")
    for idx, original, normalized in success_list:
        print(f"  [{idx}] {original} -> {normalized}")
    
    print(f"转换失败 {len(error_list)} 条:")
    for idx, original, error in error_list:
        print(f"  [{idx}] {original} -> 错误: {error}")


if __name__ == "__main__":
    main() 