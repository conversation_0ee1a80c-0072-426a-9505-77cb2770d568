# LLM 分析扩展计划

## 问题描述

当前系统在运行 `run_contribution_assessment.py` 脚本时，LLM 分析缓存阶段仅对新闻分析智能体（`NAA`）、技术分析智能体（`TAA`）和基本面分析智能体（`FAA`）进行。然而，展望层智能体（`BOA`、`BeOA`、`NOA`）和交易智能体（`TRA`）也需要进行 LLM 分析，以确保其决策基于 LLM 的洞察。

## 根本原因分析

在 `contribution_assessment/assessor.py` 文件中：
1.  `self.analyst_agents` 列表被硬编码为只包含 `NAA`、`TAA` 和 `FAA`。
2.  `_create_llm_prompt` 方法也只为这三个智能体提供了具体的提示模板。

## 解决方案计划

本计划旨在扩展 LLM 分析缓存的范围，将展望层智能体（`BOA`、`BeOA`、`NOA`）和交易智能体（`TRA`）纳入其中，并确保它们能够接收到正确的输入并生成预期的输出。

### 计划概述

1.  **扩展 `analyst_agents` 列表：** 将 `BOA`, `BeOA`, `NOA`, `TRA` 添加到 `contribution_assessment/assessor.py` 中的 `self.analyst_agents` 列表，以便它们也能进入 LLM 分析缓存阶段。
2.  **修改 `_create_llm_prompt` 方法：**
    *   为 `BOA` (看涨展望智能体) 创建一个提示模板，其输入应包含 `NAA`, `TAA`, `FAA` 的分析结果，输出应是看涨展望。
    *   为 `BeOA` (看跌展望智能体) 创建一个提示模板，其输入应包含 `NAA`, `TAA`, `FAA` 的分析结果，输出应是看跌展望。
    *   为 `NOA` (中性展望智能体) 创建一个提示模板，其输入应包含 `NAA`, `TAA`, `FAA` 的分析结果，输出应是中性展望。
    *   为 `TRA` (交易智能体) 创建一个提示模板，其输入应包含所有展望层智能体（`BOA`, `BeOA`, `NOA`）的分析结果，输出应是交易决策。
3.  **调整 `_create_mock_state` 和 `_get_mock_analysis_data`：** 确保模拟状态和模拟分析数据能够支持新的智能体类型，并反映它们之间的依赖关系。

### 详细计划步骤

#### 步骤 1: 修改 `contribution_assessment/assessor.py`

*   **目标：** 扩展 `self.analyst_agents` 列表，并为新的智能体添加 LLM 提示逻辑。
*   **文件：** `contribution_assessment/assessor.py`

    *   **修改 `__init__` 方法 (约行 69-70):**
        将 `self.analyst_agents` 扩展为包含所有需要 LLM 分析的智能体。

        ```python
        # 默认智能体配置
        self.default_agents = ["NAA", "TAA", "FAA", "BOA", "BeOA", "NOA", "TRA"]
        self.analyst_agents = ["NAA", "TAA", "FAA", "BOA", "BeOA", "NOA", "TRA"] # 扩展此列表
        self.trader_agent = "TRA"
        ```

    *   **修改 `_create_llm_prompt` 方法 (约行 307-308 之后):**
        为 `BOA`, `BeOA`, `NOA`, `TRA` 添加 `elif` 分支，定义它们的 LLM 提示。这些提示需要能够接收来自其他智能体的分析结果作为输入。

        ```python
        # ... (现有 NAA, TAA, FAA 的提示) ...
        elif agent_id == "BOA":
            return base_prompt + """
            作为看涨展望智能体 (BOA), 你的报告应基于新闻、技术和基本面分析，提供看涨的市场展望。
            报告应包含:
            - "outlook": 市场展望 (看涨).
            - "reasoning": 看涨理由 (基于NAA, TAA, FAA的分析).
            - "confidence": 分析置信度 (0.0 to 1.0).
            - "source": "llm_analysis".
            
            请严格按照此JSON格式输出。
            """
        elif agent_id == "BeOA":
            return base_prompt + """
            作为看跌展望智能体 (BeOA), 你的报告应基于新闻、技术和基本面分析，提供看跌的市场展望。
            报告应包含:
            - "outlook": 市场展望 (看跌).
            - "reasoning": 看跌理由 (基于NAA, TAA, FAA的分析).
            - "confidence": 分析置信度 (0.0 to 1.0).
            - "source": "llm_analysis".
            
            请严格按照此JSON格式输出。
            """
        elif agent_id == "NOA":
            return base_prompt + """
            作为中性展望智能体 (NOA), 你的报告应基于新闻、技术和基本面分析，提供中性的市场展望。
            报告应包含:
            - "outlook": 市场展望 (中性).
            - "reasoning": 中性理由 (基于NAA, TAA, FAA的分析).
            - "confidence": 分析置信度 (0.0 to 1.0).
            - "source": "llm_analysis".
            
            请严格按照此JSON格式输出。
            """
        elif agent_id == "TRA":
            return base_prompt + """
            作为交易智能体 (TRA), 你的报告应基于所有展望层智能体 (BOA, BeOA, NOA) 的分析，提供最终的交易决策。
            报告应包含:
            - "action": 交易动作 (BUY/SELL/HOLD).
            - "quantity": 交易数量 (如果为BUY/SELL).
            - "reasoning": 决策理由 (基于BOA, BeOA, NOA的展望).
            - "confidence": 决策置信度 (0.0 to 1.0).
            - "source": "llm_analysis".
            
            请严格按照此JSON格式输出。
            """
        else:
            return base_prompt
        ```

    *   **修改 `_create_mock_state` 方法 (约行 535-550):**
        `_create_mock_state` 方法目前只包含基本的市场数据。为了让展望层和交易层智能体的 LLM 分析能够接收到分析层智能体的输出，我们需要在 `mock_state` 中模拟这些分析结果。

        ```python
        def _create_mock_state(self) -> Dict[str, Any]:
            """
            创建模拟状态（用于智能体执行）
            返回:
                模拟的环境状态字典
            """
            # 模拟分析层智能体的输出
            mock_naa_analysis = self._get_mock_analysis_data("NAA")
            mock_taa_analysis = self._get_mock_analysis_data("TAA")
            mock_faa_analysis = self._get_mock_analysis_data("FAA")

            # 模拟展望层智能体的输出 (TRA需要这些作为输入)
            mock_boa_outlook = self._get_mock_analysis_data("BOA")
            mock_beoa_outlook = self._get_mock_analysis_data("BeOA")
            mock_noa_outlook = self._get_mock_analysis_data("NOA")

            return {
                "date": datetime.now().strftime("%Y-%m-%d"),
                "cash": self.config.get("starting_cash", 1000000),
                "positions": {},
                "position_values": {},
                "price_history": {},
                "news_history": {},
                "fundamental_data": {},
                # 添加分析层智能体的模拟输出
                "analyst_outputs": {
                    "NAA": mock_naa_analysis,
                    "TAA": mock_taa_analysis,
                    "FAA": mock_faa_analysis,
                },
                # 添加展望层智能体的模拟输出 (供TRA使用)
                "outlook_outputs": {
                    "BOA": mock_boa_outlook,
                    "BeOA": mock_beoa_outlook,
                    "NOA": mock_noa_outlook,
                }
            }
        ```

    *   **修改 `_get_mock_analysis_data` 方法 (约行 582-588 之后):**
        为 `BOA`, `BeOA`, `NOA`, `TRA` 添加模拟数据，以确保在 LLM 分析失败时有备用数据。

        ```python
        # ... (现有 NAA, TAA, FAA 的模拟数据) ...
        elif agent_id == "BOA":
            return {
                "outlook": "bullish",
                "reasoning": "模拟看涨展望：基于积极新闻和技术指标。",
                "confidence": 0.6,
                "source": "mock_data"
            }
        elif agent_id == "BeOA":
            return {
                "outlook": "bearish",
                "reasoning": "模拟看跌展望：基于负面基本面和下跌趋势。",
                "confidence": 0.6,
                "source": "mock_data"
            }
        elif agent_id == "NOA":
            return {
                "outlook": "neutral",
                "reasoning": "模拟中性展望：市场信号混杂，无明显趋势。",
                "confidence": 0.6,
                "source": "mock_data"
            }
        elif agent_id == "TRA":
            return {
                "action": "HOLD",
                "quantity": 0,
                "reasoning": "模拟交易决策：展望层意见不一，保持观望。",
                "confidence": 0.5,
                "source": "mock_data"
            }
        else:
            return {
                "output": "default",
                "confidence": 0.0,
                "source": "mock_data"
            }
        ```

### Mermaid 图示

```mermaid
graph TD
    subgraph 原始流程
        A[run_contribution_assessment.py] --> B(ContributionAssessor)
        B --> C{LLMInterface存在?}
        C -- 是 --> D[_run_llm_analysis_phase]
        C -- 否 --> E[_run_analysis_caching_phase]
        D --> F{NAA, TAA, FAA LLM分析}
        E --> G{NAA, TAA, FAA 模拟分析}
        F & G --> H[AnalysisCache]
        H --> I[_run_coalition_generation_phase]
        I --> J[_run_trading_simulation_phase]
        J --> K[_run_shapley_calculation_phase]
        K --> L[输出结果]
    end

    subgraph 改进后的LLM分析流程
        M[_run_llm_analysis_phase] --> N{遍历 self.analyst_agents}
        N --> O{为每个Agent生成Prompt}
        O --> P[LLMInterface.analyze]
        P --> Q[AnalysisCache.store]
        Q --> R{所有Agent分析完成}
        R --> S[返回缓存结果]
    end

    subgraph 智能体依赖关系
        NAA_out[NAA分析输出]
        TAA_out[TAA分析输出]
        FAA_out[FAA分析输出]

        NAA_out & TAA_out & FAA_out --> BOA_in[BOA输入]
        NAA_out & TAA_out & FAA_out --> BeOA_in[BeOA输入]
        NAA_out & TAA_out & FAA_out --> NOA_in[NOA输入]

        BOA_out[BOA展望输出]
        BeOA_out[BeOA展望输出]
        NOA_out[NOA展望输出]

        BOA_out & BeOA_out & NOA_out --> TRA_in[TRA输入]
        TRA_in --> TRA_out[TRA交易决策输出]
    end

    subgraph _create_mock_state 改进
        T[_create_mock_state] --> U[模拟NAA, TAA, FAA分析结果]
        U --> V[模拟BOA, BeOA, NOA展望结果]
        V --> W[构建包含所有模拟结果的mock_state]
    end

    style F fill:#f9f,stroke:#333,stroke-width:2px
    style G fill:#f9f,stroke:#333,stroke-width:2px
    style N fill:#f9f,stroke:#333,stroke-width:2px
    style O fill:#f9f,stroke:#333,stroke-width:2px
    style P fill:#f9f,stroke:#333,stroke-width:2px
    style Q fill:#f9f,stroke:#333,stroke-width:2px
    style T fill:#f9f,stroke:#333,stroke-width:2px
    style U fill:#f9f,stroke:#333,stroke-width:2px
    style V fill:#f9f,stroke:#333,stroke-width:2px
    style W fill:#f9f,stroke:#333,stroke-width:2px