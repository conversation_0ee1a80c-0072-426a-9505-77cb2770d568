#!/usr/bin/env python3
"""
周期性Shapley值计算示例 (Weekly Shapley Calculation Example)

本示例展示如何使用增强的交易模拟引擎，实现周期性Shapley值计算功能。
解决了原系统仅依赖首日数据缓存的问题，提供了更准确的策略诊断能力。

主要功能：
1. 配置周期性评估参数
2. 设置周末Shapley值计算回调
3. 运行包含周期性评估的交易模拟
4. 分析策略性能变化趋势
"""

import logging
from typing import Dict, Any, List, Set
from contribution_assessment.trading_simulator import TradingSimulator
from contribution_assessment.analysis_cache import AnalysisCache

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def create_enhanced_config() -> Dict[str, Any]:
    """
    创建增强的交易配置，启用周期性评估功能
    
    返回:
        包含周期性评估配置的交易配置字典
    """
    base_config = {
        # 基础交易配置
        "ticker": "AAPL",
        "start_date": "2023-01-01",
        "end_date": "2023-03-31",
        "initial_balance": 100000,
        "commission": 0.001,
        
        # 周期性评估配置（新增）
        "weekly_evaluation_enabled": True,
        "trading_days_per_week": 5,
        "risk_free_rate": 0.02,
        
        # 缓存刷新配置
        "cache_refresh_enabled": True,
        "refresh_on_week_start": True,
        
        # 策略诊断配置
        "performance_threshold": 0.8,  # 性能下降阈值（相对于历史均值）
        "auto_shapley_calculation": True
    }
    
    return base_config


def create_weekly_shapley_callback():
    """
    创建周末Shapley值计算回调函数
    
    该回调函数将在每周交易结束时被调用，用于：
    1. 分析联盟性能变化
    2. 计算智能体贡献度
    3. 提供策略调整建议
    
    返回:
        周末Shapley值计算回调函数
    """
    
    def weekly_shapley_callback(coalition: Set[str], 
                              week_data: Dict[str, Any], 
                              all_weekly_data: List[Dict[str, Any]]) -> None:
        """
        周末Shapley值计算回调函数
        
        参数:
            coalition: 当前联盟
            week_data: 当前周数据
            all_weekly_data: 所有周数据列表
        """
        week_num = week_data["week_number"]
        logger.info(f"🔍 执行第 {week_num} 周Shapley值深度分析")
        
        # 1. 分析当前周性能
        current_sharpe = week_data["sharpe_ratio"]
        current_return = week_data["total_return"]
        
        logger.info(f"当前周表现 - 夏普比率: {current_sharpe:.4f}, 总收益: {current_return:.4f}")
        
        # 2. 与历史表现对比
        if len(all_weekly_data) > 1:
            historical_data = all_weekly_data[:-1]  # 排除当前周
            avg_historical_sharpe = sum(w["sharpe_ratio"] for w in historical_data) / len(historical_data)
            avg_historical_return = sum(w["total_return"] for w in historical_data) / len(historical_data)
            
            performance_change = current_sharpe / avg_historical_sharpe if avg_historical_sharpe != 0 else 1.0
            
            logger.info(f"历史均值 - 夏普比率: {avg_historical_sharpe:.4f}, 总收益: {avg_historical_return:.4f}")
            logger.info(f"性能变化比率: {performance_change:.2f}")
            
            # 3. 策略诊断和建议
            if performance_change < 0.8:
                logger.warning("⚠️  策略性能显著下降，建议进行以下分析：")
                logger.warning("   1. 检查智能体贡献度分布是否发生变化")
                logger.warning("   2. 分析市场环境对不同智能体的影响")
                logger.warning("   3. 考虑调整联盟组合或智能体权重")
                
                # 触发详细的Shapley值计算
                perform_detailed_shapley_analysis(coalition, week_data, all_weekly_data)
                
            elif performance_change > 1.2:
                logger.info("✅ 策略性能显著提升，当前联盟配置表现良好")
                
        # 4. 预测下周表现
        predict_next_week_performance(all_weekly_data)
        
        logger.info("=" * 70)
    
    return weekly_shapley_callback


def perform_detailed_shapley_analysis(coalition: Set[str], 
                                    week_data: Dict[str, Any], 
                                    all_weekly_data: List[Dict[str, Any]]) -> None:
    """
    执行详细的Shapley值分析
    
    参数:
        coalition: 联盟
        week_data: 当前周数据
        all_weekly_data: 所有周数据
    """
    logger.info("🔬 开始详细Shapley值分析...")
    
    # 分析智能体组合的效果
    agent_combinations = [
        {"NAA", "TRA"},           # 新闻+交易
        {"TAA", "TRA"},           # 技术+交易
        {"FAA", "TRA"},           # 基本面+交易
        {"NAA", "TAA", "TRA"},    # 新闻+技术+交易
        {"NAA", "FAA", "TRA"},    # 新闻+基本面+交易
        {"TAA", "FAA", "TRA"},    # 技术+基本面+交易
    ]
    
    logger.info("分析不同智能体组合的潜在表现:")
    for combo in agent_combinations:
        if combo.issubset(coalition):
            contribution_score = estimate_combination_contribution(combo, week_data)
            logger.info(f"  {combo}: 贡献度评分 {contribution_score:.3f}")
    
    # 提供具体的优化建议
    provide_optimization_suggestions(coalition, week_data, all_weekly_data)


def estimate_combination_contribution(combination: Set[str], week_data: Dict[str, Any]) -> float:
    """
    估算智能体组合的贡献度
    
    参数:
        combination: 智能体组合
        week_data: 周数据
        
    返回:
        贡献度评分
    """
    # 基于组合的复杂度和实际表现计算贡献度
    # 这里使用简化的启发式方法
    base_score = week_data["sharpe_ratio"]
    complexity_bonus = len(combination) * 0.1  # 复杂度奖励
    
    # 特定智能体的权重调整
    weights = {"NAA": 1.2, "TAA": 1.1, "FAA": 1.0, "TRA": 1.5}
    weighted_score = sum(weights.get(agent, 1.0) for agent in combination)
    
    return base_score * (1 + complexity_bonus) * (weighted_score / len(combination))


def provide_optimization_suggestions(coalition: Set[str], 
                                   week_data: Dict[str, Any], 
                                   all_weekly_data: List[Dict[str, Any]]) -> None:
    """
    提供优化建议
    
    参数:
        coalition: 当前联盟
        week_data: 当前周数据
        all_weekly_data: 所有周数据
    """
    logger.info("📋 策略优化建议:")
    
    # 基于性能趋势提供建议
    if len(all_weekly_data) >= 3:
        recent_performance = [w["sharpe_ratio"] for w in all_weekly_data[-3:]]
        if all(recent_performance[i] < recent_performance[i-1] for i in range(1, len(recent_performance))):
            logger.info("  • 连续性能下降，建议考虑:")
            logger.info("    - 减少交易频率，采用更保守策略")
            logger.info("    - 增加风险控制智能体")
            logger.info("    - 重新评估市场环境适应性")
    
    # 基于联盟组成提供建议
    if "BOA" not in coalition and "BeOA" not in coalition:
        logger.info("  • 缺少展望层智能体，建议:")
        logger.info("    - 加入BOA或BeOA提供市场方向判断")
        logger.info("    - 增强策略的前瞻性和适应性")
    
    if len(coalition) < 4:
        logger.info("  • 联盟规模较小，考虑:")
        logger.info("    - 增加智能体提供更全面的分析")
        logger.info("    - 平衡不同类型智能体的比例")


def predict_next_week_performance(all_weekly_data: List[Dict[str, Any]]) -> None:
    """
    预测下周表现
    
    参数:
        all_weekly_data: 所有周数据
    """
    if len(all_weekly_data) < 2:
        return
    
    # 简单的趋势预测
    recent_sharpes = [w["sharpe_ratio"] for w in all_weekly_data[-min(3, len(all_weekly_data)):]]
    trend = sum(recent_sharpes[i] - recent_sharpes[i-1] for i in range(1, len(recent_sharpes)))
    
    logger.info(f"📈 下周预测:")
    if trend > 0:
        logger.info(f"  • 预期性能继续改善 (趋势: +{trend:.4f})")
    elif trend < 0:
        logger.info(f"  • 预期性能可能下降 (趋势: {trend:.4f})")
    else:
        logger.info(f"  • 预期性能保持稳定")


def main():
    """
    主函数：演示周期性Shapley值计算功能
    """
    logger.info("🚀 启动周期性Shapley值计算示例")
    
    # 1. 创建增强配置
    config = create_enhanced_config()
    logger.info(f"配置已创建: 周期性评估={config['weekly_evaluation_enabled']}")
    
    # 2. 初始化交易模拟器
    simulator = TradingSimulator(config, logger=logger)
    
    # 3. 设置周末Shapley值计算回调
    weekly_callback = create_weekly_shapley_callback()
    simulator.set_weekly_shapley_callback(weekly_callback)
    
    # 4. 初始化分析缓存
    analysis_cache = AnalysisCache(logger=logger)
    
    # 5. 定义测试联盟
    test_coalition = {"NAA", "TAA", "FAA", "BOA", "TRA"}
    
    # 6. 运行带周期性评估的交易模拟
    logger.info(f"开始模拟联盟: {test_coalition}")
    
    try:
        # 注意：这里需要真实的智能体实例，此处为演示目的使用None
        # 在实际使用中，应该传入真实的智能体字典
        sharpe_ratio = simulator.run_simulation_for_coalition(
            coalition=test_coalition,
            analysis_cache=analysis_cache,
            agents=None,  # 实际使用时需要提供智能体实例
            simulation_days=21  # 3周的交易
        )
        
        logger.info(f"✅ 模拟完成，最终夏普比率: {sharpe_ratio:.4f}")
        
        # 7. 获取并展示统计信息
        stats = simulator.get_stats()
        logger.info("📊 最终统计信息:")
        logger.info(f"  • 总模拟次数: {stats['total_simulations']}")
        logger.info(f"  • 成功率: {stats['success_rate']:.2f}%")
        logger.info(f"  • 完成周数: {stats['weekly_stats']['completed_weeks']}")
        logger.info(f"  • Shapley计算次数: {stats['weekly_stats']['shapley_calculations']}")
        logger.info(f"  • 策略调整次数: {stats['weekly_stats']['strategy_adjustments']}")
        
        # 8. 展示缓存统计
        cache_stats = analysis_cache.get_stats()
        logger.info("💾 缓存统计信息:")
        logger.info(f"  • 缓存命中率: {cache_stats['hit_rate']:.2f}%")
        logger.info(f"  • 刷新次数: {cache_stats['refresh_count']}")
        
    except Exception as e:
        logger.error(f"❌ 模拟失败: {e}")
        raise
    
    logger.info("🎉 周期性Shapley值计算示例完成")


if __name__ == "__main__":
    main()