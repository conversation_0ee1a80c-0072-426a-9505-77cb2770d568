"""
历史得分管理器 (Historical Score Manager)

本模块负责管理智能体的历史Shapley值得分，为OPRO优化提供反馈数据。
主要功能包括：
1. 读取和解析周期性Shapley值结果
2. 维护提示词与性能的映射关系
3. 提供历史得分趋势分析
4. 存储和检索优化结果
"""

import os
import json
import glob
import logging
import sqlite3
import hashlib
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from collections import defaultdict, deque

class HistoricalScoreManager:
    """
    历史得分管理器
    
    管理智能体的历史Shapley值得分，维护提示词优化的反馈循环，
    提供历史数据分析和趋势预测功能。
    """
    
    def __init__(self, 
                 results_base_path: str = "results/periodic_shapley",
                 db_path: str = "results/opro_optimization.db",
                 logger: Optional[logging.Logger] = None):
        """
        初始化历史得分管理器
        
        参数:
            results_base_path: 周期性Shapley结果文件的基础路径
            db_path: SQLite数据库路径，用于存储优化历史
            logger: 日志记录器
        """
        self.results_base_path = results_base_path
        self.db_path = db_path
        self.logger = logger or self._create_default_logger()
        
        # 缓存数据
        self._shapley_cache = {}
        self._cache_timestamp = None
        self._cache_ttl = 300  # 缓存5分钟
        
        # 初始化数据库
        self._init_database()
        
        # 加载最新数据
        self._load_latest_data()
        
        self.logger.info("历史得分管理器初始化完成")
    
    def _create_default_logger(self) -> logging.Logger:
        """创建默认日志记录器"""
        logger = logging.getLogger(f"{__name__}.HistoricalScoreManager")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def _init_database(self):
        """初始化SQLite数据库"""
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 创建优化历史表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS optimization_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    agent_id TEXT NOT NULL,
                    prompt_hash TEXT NOT NULL,
                    prompt_text TEXT NOT NULL,
                    shapley_score REAL,
                    optimization_date TEXT NOT NULL,
                    evaluation_date TEXT,
                    is_active BOOLEAN DEFAULT FALSE,
                    metadata TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 创建周期性得分表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS weekly_scores (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    agent_id TEXT NOT NULL,
                    week_number INTEGER NOT NULL,
                    shapley_score REAL NOT NULL,
                    trading_days TEXT NOT NULL,
                    result_file TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(agent_id, week_number, result_file)
                )
            """)
            
            # 创建索引
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_optimization_agent ON optimization_history(agent_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_optimization_date ON optimization_history(optimization_date)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_weekly_agent ON weekly_scores(agent_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_weekly_week ON weekly_scores(week_number)")
            
            conn.commit()
        
        self.logger.info("数据库初始化完成")
    
    def _load_latest_data(self):
        """加载最新的Shapley值数据"""
        try:
            self._refresh_shapley_cache()
            self.logger.info("最新Shapley数据加载完成")
        except Exception as e:
            self.logger.error(f"加载最新数据失败: {e}")
    
    def _refresh_shapley_cache(self):
        """刷新Shapley值缓存"""
        current_time = datetime.now()
        
        # 检查缓存是否需要刷新
        if (self._cache_timestamp and 
            (current_time - self._cache_timestamp).seconds < self._cache_ttl):
            return
        
        self.logger.debug("刷新Shapley值缓存...")
        
        # 清空缓存
        self._shapley_cache = defaultdict(list)
        
        # 获取所有结果文件
        pattern = os.path.join(self.results_base_path, "periodic_shapley_*.json")
        result_files = glob.glob(pattern)
        
        if not result_files:
            self.logger.warning(f"未找到Shapley结果文件: {pattern}")
            return
        
        # 按时间排序文件
        result_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
        
        # 处理最近的文件
        for file_path in result_files[:10]:  # 只处理最近10个文件
            try:
                self._process_shapley_file(file_path)
            except Exception as e:
                self.logger.error(f"处理文件 {file_path} 失败: {e}")
                continue
        
        self._cache_timestamp = current_time
        self.logger.debug(f"缓存刷新完成，共加载 {len(self._shapley_cache)} 个智能体的数据")
    
    def _process_shapley_file(self, file_path: str):
        """处理单个Shapley结果文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if not isinstance(data, list):
                self.logger.warning(f"文件 {file_path} 格式不正确")
                return
            
            file_basename = os.path.basename(file_path)
            
            # 提取文件中的时间戳
            timestamp_str = file_basename.replace("periodic_shapley_", "").replace(".json", "")
            try:
                file_datetime = datetime.strptime(timestamp_str, "%Y%m%d_%H%M%S")
            except ValueError:
                file_datetime = datetime.fromtimestamp(os.path.getmtime(file_path))
            
            # 处理每周的数据
            for week_data in data:
                if not isinstance(week_data, dict) or not week_data.get("success", False):
                    continue
                
                shapley_values = week_data.get("shapley_values", {})
                week_number = week_data.get("week", 0)
                trading_days = week_data.get("trading_days", "")
                
                # 存储到缓存
                for agent_id, score in shapley_values.items():
                    if isinstance(score, (int, float)):
                        self._shapley_cache[agent_id].append({
                            "score": float(score),
                            "week": week_number,
                            "trading_days": trading_days,
                            "file_path": file_path,
                            "file_datetime": file_datetime,
                            "timestamp": file_datetime.isoformat()
                        })
                
                # 存储到数据库
                self._store_weekly_scores_to_db(shapley_values, week_number, trading_days, file_basename)
            
        except Exception as e:
            self.logger.error(f"处理Shapley文件 {file_path} 时出错: {e}")
    
    def _store_weekly_scores_to_db(self, shapley_values: Dict[str, float], 
                                  week_number: int, trading_days: str, file_name: str):
        """将周期性得分存储到数据库"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                for agent_id, score in shapley_values.items():
                    cursor.execute("""
                        INSERT OR IGNORE INTO weekly_scores 
                        (agent_id, week_number, shapley_score, trading_days, result_file)
                        VALUES (?, ?, ?, ?, ?)
                    """, (agent_id, week_number, float(score), trading_days, file_name))
                
                conn.commit()
                
        except Exception as e:
            self.logger.error(f"存储周期性得分到数据库失败: {e}")
    
    def get_agent_score_history(self, agent_id: str, weeks: int = 10) -> List[Dict[str, Any]]:
        """
        获取指定智能体的历史得分
        
        参数:
            agent_id: 智能体ID
            weeks: 返回最近几周的数据
            
        返回:
            历史得分列表，按时间排序
        """
        self._refresh_shapley_cache()
        
        if agent_id not in self._shapley_cache:
            self.logger.warning(f"未找到智能体 {agent_id} 的历史数据")
            return []
        
        # 获取该智能体的所有数据
        agent_data = self._shapley_cache[agent_id]
        
        # 按时间排序
        sorted_data = sorted(agent_data, key=lambda x: x["file_datetime"], reverse=True)
        
        # 限制返回数量
        limited_data = sorted_data[:weeks]
        
        self.logger.debug(f"获取智能体 {agent_id} 最近 {len(limited_data)} 周的历史数据")
        
        return limited_data
    
    def get_agent_optimization_history(self, agent_id: str, weeks: int = 10) -> List[Dict[str, Any]]:
        """
        获取智能体的优化历史（包含提示词信息）
        
        参数:
            agent_id: 智能体ID
            weeks: 考虑的历史周数
            
        返回:
            优化历史列表
        """
        
        # 从数据库获取优化历史
        optimization_history = self._get_optimization_history_from_db(agent_id, weeks)
        
        # 如果数据库中没有足够的历史，使用默认提示词和历史得分
        if len(optimization_history) < 2:
            score_history = self.get_agent_score_history(agent_id, weeks)
            
            # 创建默认的优化历史
            default_history = []
            for i, score_data in enumerate(score_history):
                default_history.append({
                    "prompt": f"默认提示词 {i+1} - {agent_id}",
                    "score": score_data["score"],
                    "timestamp": score_data["timestamp"],
                    "week": score_data["week"],
                    "is_default": True,
                    "source": "historical_scores"
                })
            
            return default_history
        
        return optimization_history
    
    def _get_optimization_history_from_db(self, agent_id: str, weeks: int) -> List[Dict[str, Any]]:
        """从数据库获取优化历史"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 获取优化历史
                cursor.execute("""
                    SELECT prompt_text, shapley_score, optimization_date, 
                           evaluation_date, metadata
                    FROM optimization_history 
                    WHERE agent_id = ? AND shapley_score IS NOT NULL
                    ORDER BY optimization_date DESC
                    LIMIT ?
                """, (agent_id, weeks))
                
                rows = cursor.fetchall()
                
                optimization_history = []
                for row in rows:
                    prompt_text, score, opt_date, eval_date, metadata_str = row
                    
                    metadata = {}
                    if metadata_str:
                        try:
                            metadata = json.loads(metadata_str)
                        except json.JSONDecodeError:
                            pass
                    
                    optimization_history.append({
                        "prompt": prompt_text,
                        "score": score,
                        "optimization_date": opt_date,
                        "evaluation_date": eval_date,
                        "timestamp": eval_date or opt_date,
                        "metadata": metadata,
                        "is_default": False,
                        "source": "optimization_history"
                    })
                
                return optimization_history
                
        except Exception as e:
            self.logger.error(f"从数据库获取优化历史失败: {e}")
            return []
    
    def store_optimization_result(self, 
                                agent_id: str, 
                                prompt: str, 
                                estimated_score: Optional[float] = None,
                                metadata: Optional[Dict] = None) -> str:
        """
        存储优化结果
        
        参数:
            agent_id: 智能体ID
            prompt: 优化后的提示词
            estimated_score: 估算得分
            metadata: 额外的元数据
            
        返回:
            提示词哈希值
        """
        prompt_hash = self._hash_prompt(prompt)
        current_time = datetime.now().isoformat()
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 先设置其他提示词为非活跃状态
                cursor.execute("""
                    UPDATE optimization_history 
                    SET is_active = FALSE 
                    WHERE agent_id = ?
                """, (agent_id,))
                
                # 插入新的优化结果
                cursor.execute("""
                    INSERT INTO optimization_history 
                    (agent_id, prompt_hash, prompt_text, shapley_score, 
                     optimization_date, is_active, metadata)
                    VALUES (?, ?, ?, ?, ?, TRUE, ?)
                """, (agent_id, prompt_hash, prompt, estimated_score, 
                     current_time, json.dumps(metadata) if metadata else None))
                
                conn.commit()
                
            self.logger.info(f"优化结果已存储: {agent_id} -> {prompt_hash[:8]}...")
            
        except Exception as e:
            self.logger.error(f"存储优化结果失败: {e}")
        
        return prompt_hash
    
    def update_actual_score(self, 
                          agent_id: str, 
                          prompt_hash: str, 
                          actual_score: float,
                          evaluation_date: Optional[str] = None) -> bool:
        """
        更新实际得分
        
        参数:
            agent_id: 智能体ID
            prompt_hash: 提示词哈希值
            actual_score: 实际Shapley得分
            evaluation_date: 评估日期
            
        返回:
            是否更新成功
        """
        evaluation_date = evaluation_date or datetime.now().isoformat()
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    UPDATE optimization_history 
                    SET shapley_score = ?, evaluation_date = ?
                    WHERE agent_id = ? AND prompt_hash = ?
                """, (actual_score, evaluation_date, agent_id, prompt_hash))
                
                rows_affected = cursor.rowcount
                conn.commit()
                
                if rows_affected > 0:
                    self.logger.info(f"更新实际得分成功: {agent_id} {prompt_hash[:8]} -> {actual_score:.6f}")
                    return True
                else:
                    self.logger.warning(f"未找到匹配的优化记录: {agent_id} {prompt_hash[:8]}")
                    return False
                    
        except Exception as e:
            self.logger.error(f"更新实际得分失败: {e}")
            return False
    
    def get_active_prompts(self) -> Dict[str, str]:
        """
        获取当前活跃的提示词
        
        返回:
            {agent_id: prompt_text} 字典
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT agent_id, prompt_text 
                    FROM optimization_history 
                    WHERE is_active = TRUE
                """)
                
                rows = cursor.fetchall()
                return {agent_id: prompt_text for agent_id, prompt_text in rows}
                
        except Exception as e:
            self.logger.error(f"获取活跃提示词失败: {e}")
            return {}
    
    def get_score_trends(self, agent_id: str, weeks: int = 20) -> Dict[str, Any]:
        """
        分析智能体得分趋势
        
        参数:
            agent_id: 智能体ID
            weeks: 分析的历史周数
            
        返回:
            趋势分析结果
        """
        score_history = self.get_agent_score_history(agent_id, weeks)
        
        if len(score_history) < 2:
            return {
                "trend": "insufficient_data",
                "slope": 0.0,
                "r_squared": 0.0,
                "recent_average": 0.0,
                "historical_average": 0.0,
                "volatility": 0.0
            }
        
        # 提取得分和时间
        scores = [data["score"] for data in score_history]
        scores.reverse()  # 按时间正序排列
        
        # 计算趋势
        x = np.arange(len(scores))
        if len(scores) > 1:
            # 线性回归计算趋势
            slope, intercept = np.polyfit(x, scores, 1)
            r_squared = np.corrcoef(x, scores)[0, 1] ** 2 if len(scores) > 2 else 0.0
        else:
            slope = 0.0
            r_squared = 0.0
        
        # 计算其他统计指标
        recent_scores = scores[-min(5, len(scores)):]  # 最近5周
        historical_scores = scores[:-min(5, len(scores))] if len(scores) > 5 else scores
        
        recent_average = np.mean(recent_scores)
        historical_average = np.mean(historical_scores) if historical_scores else recent_average
        volatility = np.std(scores) if len(scores) > 1 else 0.0
        
        # 确定趋势方向
        if slope > 0.01:
            trend = "improving"
        elif slope < -0.01:
            trend = "declining"
        else:
            trend = "stable"
        
        return {
            "trend": trend,
            "slope": slope,
            "r_squared": r_squared,
            "recent_average": recent_average,
            "historical_average": historical_average,
            "volatility": volatility,
            "data_points": len(scores),
            "score_range": {
                "min": min(scores),
                "max": max(scores),
                "latest": scores[-1] if scores else 0.0
            }
        }
    
    def get_cross_agent_comparison(self, weeks: int = 10) -> Dict[str, Any]:
        """
        获取跨智能体的性能比较
        
        参数:
            weeks: 比较的历史周数
            
        返回:
            比较结果
        """
        self._refresh_shapley_cache()
        
        agent_stats = {}
        
        # 计算每个智能体的统计信息
        for agent_id in self._shapley_cache.keys():
            score_history = self.get_agent_score_history(agent_id, weeks)
            scores = [data["score"] for data in score_history]
            
            if scores:
                agent_stats[agent_id] = {
                    "average_score": np.mean(scores),
                    "max_score": max(scores),
                    "min_score": min(scores),
                    "volatility": np.std(scores),
                    "data_points": len(scores),
                    "latest_score": scores[0] if scores else 0.0,  # 最新的在前面
                    "trend": self.get_score_trends(agent_id, weeks)["trend"]
                }
        
        # 排序智能体
        if agent_stats:
            sorted_agents = sorted(
                agent_stats.items(),
                key=lambda x: x[1]["average_score"],
                reverse=True
            )
            
            best_agent = sorted_agents[0]
            worst_agent = sorted_agents[-1]
            
            return {
                "agent_stats": agent_stats,
                "ranking": [agent for agent, _ in sorted_agents],
                "best_performer": {
                    "agent_id": best_agent[0],
                    "average_score": best_agent[1]["average_score"]
                },
                "worst_performer": {
                    "agent_id": worst_agent[0],
                    "average_score": worst_agent[1]["average_score"]
                },
                "overall_stats": {
                    "total_agents": len(agent_stats),
                    "average_score_across_agents": np.mean([stats["average_score"] for stats in agent_stats.values()]),
                    "score_dispersion": np.std([stats["average_score"] for stats in agent_stats.values()])
                }
            }
        
        return {"agent_stats": {}, "ranking": [], "overall_stats": {}}
    
    def get_optimization_effectiveness(self) -> Dict[str, Any]:
        """
        分析优化效果
        
        返回:
            优化效果分析结果
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 获取所有有实际得分的优化记录
                cursor.execute("""
                    SELECT agent_id, shapley_score, optimization_date, evaluation_date
                    FROM optimization_history 
                    WHERE shapley_score IS NOT NULL
                    ORDER BY agent_id, optimization_date
                """)
                
                rows = cursor.fetchall()
                
                if not rows:
                    return {"effectiveness": "no_data", "improvements": {}}
                
                # 按智能体分组分析
                agent_improvements = defaultdict(list)
                for agent_id, score, opt_date, eval_date in rows:
                    agent_improvements[agent_id].append({
                        "score": score,
                        "optimization_date": opt_date,
                        "evaluation_date": eval_date
                    })
                
                # 分析每个智能体的改进情况
                effectiveness_stats = {}
                for agent_id, improvements in agent_improvements.items():
                    if len(improvements) < 2:
                        continue
                    
                    scores = [imp["score"] for imp in improvements]
                    
                    # 计算改进趋势
                    first_score = scores[0]
                    latest_score = scores[-1]
                    improvement = latest_score - first_score
                    improvement_pct = (improvement / abs(first_score)) * 100 if first_score != 0 else 0
                    
                    effectiveness_stats[agent_id] = {
                        "total_optimizations": len(improvements),
                        "first_score": first_score,
                        "latest_score": latest_score,
                        "absolute_improvement": improvement,
                        "percentage_improvement": improvement_pct,
                        "best_score": max(scores),
                        "worst_score": min(scores),
                        "average_score": np.mean(scores)
                    }
                
                # 计算总体效果
                if effectiveness_stats:
                    total_improvements = sum(stats["absolute_improvement"] for stats in effectiveness_stats.values())
                    positive_improvements = sum(1 for stats in effectiveness_stats.values() if stats["absolute_improvement"] > 0)
                    
                    overall_effectiveness = {
                        "total_agents_optimized": len(effectiveness_stats),
                        "agents_with_positive_improvement": positive_improvements,
                        "improvement_success_rate": positive_improvements / len(effectiveness_stats) * 100,
                        "total_absolute_improvement": total_improvements,
                        "average_improvement_per_agent": total_improvements / len(effectiveness_stats)
                    }
                else:
                    overall_effectiveness = {}
                
                return {
                    "effectiveness": "analyzed",
                    "agent_improvements": effectiveness_stats,
                    "overall_effectiveness": overall_effectiveness
                }
                
        except Exception as e:
            self.logger.error(f"分析优化效果失败: {e}")
            return {"effectiveness": "error", "error": str(e)}
    
    def _hash_prompt(self, prompt: str) -> str:
        """生成提示词哈希值"""
        return hashlib.md5(prompt.encode('utf-8')).hexdigest()
    
    def cleanup_old_data(self, days_to_keep: int = 90):
        """
        清理旧数据
        
        参数:
            days_to_keep: 保留的天数
        """
        cutoff_date = datetime.now() - timedelta(days=days_to_keep)
        cutoff_str = cutoff_date.isoformat()
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 清理旧的优化历史（保留活跃的）
                cursor.execute("""
                    DELETE FROM optimization_history 
                    WHERE optimization_date < ? AND is_active = FALSE
                """, (cutoff_str,))
                
                optimization_deleted = cursor.rowcount
                
                # 清理旧的周期性得分
                cursor.execute("""
                    DELETE FROM weekly_scores 
                    WHERE created_at < ?
                """, (cutoff_str,))
                
                weekly_deleted = cursor.rowcount
                conn.commit()
                
                self.logger.info(f"数据清理完成: 删除 {optimization_deleted} 条优化记录, "
                               f"{weekly_deleted} 条周期性得分记录")
                
        except Exception as e:
            self.logger.error(f"清理旧数据失败: {e}")
    
    def export_data(self, output_path: str) -> bool:
        """
        导出数据到JSON文件
        
        参数:
            output_path: 输出文件路径
            
        返回:
            是否导出成功
        """
        try:
            export_data = {
                "export_timestamp": datetime.now().isoformat(),
                "shapley_cache": dict(self._shapley_cache),
                "optimization_history": self._export_optimization_history(),
                "weekly_scores": self._export_weekly_scores()
            }
            
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"数据导出成功: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"数据导出失败: {e}")
            return False
    
    def _export_optimization_history(self) -> List[Dict]:
        """导出优化历史"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT agent_id, prompt_hash, prompt_text, shapley_score,
                           optimization_date, evaluation_date, is_active, metadata
                    FROM optimization_history
                    ORDER BY optimization_date DESC
                """)
                
                rows = cursor.fetchall()
                
                return [{
                    "agent_id": row[0],
                    "prompt_hash": row[1],
                    "prompt_text": row[2],
                    "shapley_score": row[3],
                    "optimization_date": row[4],
                    "evaluation_date": row[5],
                    "is_active": bool(row[6]),
                    "metadata": json.loads(row[7]) if row[7] else None
                } for row in rows]
                
        except Exception as e:
            self.logger.error(f"导出优化历史失败: {e}")
            return []
    
    def _export_weekly_scores(self) -> List[Dict]:
        """导出周期性得分"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT agent_id, week_number, shapley_score, trading_days, 
                           result_file, created_at
                    FROM weekly_scores
                    ORDER BY created_at DESC
                """)
                
                rows = cursor.fetchall()
                
                return [{
                    "agent_id": row[0],
                    "week_number": row[1],
                    "shapley_score": row[2],
                    "trading_days": row[3],
                    "result_file": row[4],
                    "created_at": row[5]
                } for row in rows]
                
        except Exception as e:
            self.logger.error(f"导出周期性得分失败: {e}")
            return []
    
    def get_summary_stats(self) -> Dict[str, Any]:
        """获取汇总统计信息"""
        self._refresh_shapley_cache()
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 统计优化历史
                cursor.execute("SELECT COUNT(*) FROM optimization_history")
                total_optimizations = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(DISTINCT agent_id) FROM optimization_history")
                agents_optimized = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM optimization_history WHERE is_active = TRUE")
                active_prompts = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM weekly_scores")
                total_weekly_scores = cursor.fetchone()[0]
                
                # 统计缓存数据
                cached_agents = len(self._shapley_cache)
                total_cached_records = sum(len(records) for records in self._shapley_cache.values())
                
                return {
                    "database_stats": {
                        "total_optimizations": total_optimizations,
                        "agents_optimized": agents_optimized,
                        "active_prompts": active_prompts,
                        "total_weekly_scores": total_weekly_scores
                    },
                    "cache_stats": {
                        "cached_agents": cached_agents,
                        "total_cached_records": total_cached_records,
                        "cache_timestamp": self._cache_timestamp.isoformat() if self._cache_timestamp else None
                    },
                    "system_info": {
                        "results_base_path": self.results_base_path,
                        "db_path": self.db_path,
                        "cache_ttl": self._cache_ttl
                    }
                }
                
        except Exception as e:
            self.logger.error(f"获取汇总统计失败: {e}")
            return {"error": str(e)}