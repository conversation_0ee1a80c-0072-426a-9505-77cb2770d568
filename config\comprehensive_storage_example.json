{"description": "综合数据存储系统示例配置文件", "version": "1.0", "last_updated": "2025-07-04", "storage": {"comprehensive_storage": {"enabled": true, "base_path": "data", "trading_data_path": "data/trading", "prompts_data_path": "data/prompts", "visualizations_path": "data/visualizations", "exports_path": "data/exports", "backups_path": "data/backups", "database_path": "data/comprehensive_storage.db", "auto_backup_interval_hours": 24, "data_validation_enabled": true, "compression_enabled": true, "max_file_size_mb": 100}}, "backup_config": {"enabled": true, "backup_interval_hours": 24, "max_backups_to_keep": 30, "compression_enabled": true, "incremental_backup": true, "backup_validation": true, "auto_cleanup": true, "backup_base_path": "data/backups"}, "visualization_config": {"chart_width": 1200, "chart_height": 800, "color_scheme": "default", "save_format": "png", "interactive_charts": true, "include_volume": true, "include_indicators": true}, "analysis_config": {"default_export_format": "excel", "include_charts": true, "max_data_points": 10000, "cache_analysis_results": true}, "ab_testing_config": {"default_test_duration_hours": 24, "min_sample_size": 10, "significance_level": 0.05, "auto_analysis": true}, "data_retention": {"trading_sessions_days": 365, "prompt_optimizations_days": 180, "visualizations_days": 90, "exports_days": 30, "logs_days": 7}, "performance_settings": {"batch_size": 1000, "connection_timeout": 30, "max_concurrent_operations": 5, "memory_limit_mb": 1024}, "notification_settings": {"backup_completion": true, "backup_failure": true, "storage_full_warning": true, "data_validation_errors": true}, "security_settings": {"encrypt_backups": false, "backup_password": null, "access_logging": true, "data_anonymization": false}, "integration_settings": {"opro_integration": true, "auto_data_collection": true, "real_time_analysis": false, "external_apis": {"stock_data": true, "news_sentiment": false}}, "logging": {"level": "INFO", "file_path": "logs/data_storage.log", "max_file_size_mb": 10, "backup_count": 5, "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"}, "monitoring": {"enabled": true, "metrics_collection": true, "performance_tracking": true, "error_tracking": true, "usage_statistics": true}}