# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Multi-Agent Trading System with Contribution Assessment that uses Large Language Models to analyze stock market data through 7 specialized agents and calculates each agent's contribution using Shapley values.

### Agent Architecture (7 Agents Total)
- **Analysis Layer**: NAA (News Analyst), TAA (Technical Analyst), FAA (Fundamental Analyst)  
- **Outlook Layer**: BOA (Bullish Outlook), BeOA (Bearish Outlook), NOA (Neutral Observer)
- **Decision Layer**: TRA (Trader Agent)

## Development Commands

### Environment Setup
```bash
# Check LLM provider setup
python check_llm_setup.py --provider zhipuai

# Environment variables needed
export ZHIPUAI_API_KEY=your_api_key
# OR create .env file with ZHIPUAI_API_KEY=your_key
```

### Running the System
```bash
# Quick test run (recommended for development)
python run_contribution_assessment.py --llm-provider zhipuai --quick-test --verbose

# Full assessment with date range
python run_contribution_assessment.py --llm-provider zhipuai --start-date 2025-01-01 --end-date 2025-01-03 --verbose

# Using dedicated real agents script
python run_with_real_agents.py --llm-provider zhipuai --verbose

# Shell script for batch runs
./run_assessment.sh
```

### Testing
```bash
# Quick validation test
python test_real_agents.py

# Daily LLM trading test
python daily_llm_trading.py
```

## Code Architecture

### Core Modules
- **`contribution_assessment/`** - Main coordination system
  - `assessor.py` - Main orchestrator (4-phase assessment process)
  - `shapley_calculator.py` - Shapley value computation engine
  - `trading_simulator.py` - Trading simulation and backtesting
  - `coalition_manager.py` - Agent coalition management
  - `llm_interface.py` - LLM provider abstraction layer
  - `analysis_cache.py` - Analysis result caching system

- **`agents/`** - Agent implementations
  - `base_agent.py` - Abstract base class for all agents
  - `analyst_agents.py` - Analysis layer (NAA, TAA, FAA)
  - `outlook_agents.py` - Outlook layer (BOA, BeOA, NOA)  
  - `trader_agent.py` - Trading execution agent (TRA)
  - `agent_factory.py` - Agent creation and configuration

- **`stock_trading_env.py`** - Trading environment simulator
- **`data/`** - Comprehensive data management (SQLite + JSON)

### Four-Phase Assessment Process
1. **Analysis Caching** - Run and cache agent analyses
2. **Coalition Generation** - Generate all possible agent coalitions
3. **Trading Simulation** - Simulate trading for each coalition
4. **Shapley Calculation** - Calculate contribution scores using game theory

### Data Structure
- **Stock Data**: SQLite databases in `data/tickers/` (AAPL, GOOGL, META, etc.)
- **OHLCV Data**: Daily price data in `data/ohlcv/`
- **News Data**: Daily news articles in `data/news/`
- **Results**: Assessment outputs in `results/`

### LLM Integration
- Primary: ZhipuAI (zhipuai SDK)
- Secondary: OpenAI (openai SDK)
- Provider abstraction through `llm_interface.py`
- Environment validation via `check_llm_setup.py`

## Key Implementation Notes

### Agent Development
- All agents inherit from `BaseAgent` in `agents/base_agent.py`
- Use `AgentFactory` for consistent agent creation
- Agents operate on real market data, not simulations
- Analysis results are cached to avoid redundant LLM calls

### Data Management
- Stock data spans 1999-2025 with 9,605+ news articles for AAPL
- Data validation and continuity checking built-in
- SQLite for structured data, JSON for daily snapshots
- Automatic data collection and validation scripts

### Contribution Assessment
- Uses game theory (Shapley values) to quantify agent contributions
- Coalition-based approach evaluates all agent combinations
- Supports both quick tests and full assessments
- Results include individual and coalition performance metrics

### Configuration
- Environment variables managed through `.env` file
- LLM provider switching via command-line arguments
- Comprehensive logging and diagnostic output
- Support for weekly/periodic evaluation cycles