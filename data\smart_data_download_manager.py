#!/usr/bin/env python3
"""
智能数据下载管理器

统一管理股票数据的下载、缓存和验证，支持：
1. 基本面数据：一次性下载全部历史数据
2. K线数据：一次性下载全部历史数据  
3. 新闻数据：按月下载，支持训练期间提前1个月

核心特性：
- 智能缓存检查，避免重复下载
- API调用次数优化
- 数据完整性验证
- 错误处理和重试机制
"""

import os
import sys
import json
import sqlite3
import subprocess
import time
from datetime import datetime, timedelta, date
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import logging

# 添加项目根目录到路径
current_script_path = os.path.abspath(__file__)
project_root = os.path.dirname(os.path.dirname(current_script_path))
sys.path.append(project_root)

try:
    from config import DATA_DIR, ALPHAVANTAGE_API_KEY
    from data.get_all_data import manage_stock_data, get_database_path
    from data.download_script_integrator import DownloadScriptIntegrator
    from data.data_requirement_analyzer import DataRequirementAnalyzer
    from data.cache_metadata_manager import CacheMetadataManager
except ImportError as e:
    print(f"导入配置失败: {e}", file=sys.stderr)
    sys.exit(1)

@dataclass
class DataRequirement:
    """数据需求定义"""
    stock: str
    training_start_date: str
    training_end_date: str
    news_start_date: str  # 新闻数据开始日期（提前1个月）
    news_end_date: str    # 新闻数据结束日期
    
@dataclass
class CacheStatus:
    """缓存状态信息"""
    has_ohlcv: bool
    has_news: bool
    has_fundamentals: bool
    ohlcv_range: Tuple[Optional[str], Optional[str]]
    news_range: Tuple[Optional[str], Optional[str]]
    last_update: Optional[str]
    data_quality_score: float

class SmartDataDownloadManager:
    """智能数据下载管理器"""
    
    def __init__(self, verbose: bool = True, max_api_calls_per_day: int = 25):
        """
        初始化智能数据下载管理器

        Args:
            verbose: 是否显示详细日志
            max_api_calls_per_day: 每日最大API调用次数
        """
        self.verbose = verbose
        self.max_api_calls_per_day = max_api_calls_per_day
        self.api_call_count = 0

        # 设置日志
        self.logger = self._setup_logger()

        # 缓存目录
        self.cache_dir = os.path.join(DATA_DIR, "cache")
        os.makedirs(self.cache_dir, exist_ok=True)

        # API调用统计文件
        self.api_stats_file = os.path.join(self.cache_dir, "api_call_stats.json")
        self._load_api_stats()

        # 初始化组件
        self.script_integrator = DownloadScriptIntegrator(verbose=verbose)
        self.requirement_analyzer = DataRequirementAnalyzer(verbose=verbose)
        self.cache_manager = CacheMetadataManager(verbose=verbose)

        self.logger.info("智能数据下载管理器初始化完成")
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger("SmartDataDownloadManager")
        logger.setLevel(logging.INFO if self.verbose else logging.WARNING)
        
        if not logger.handlers:
            handler = logging.StreamHandler(sys.stderr)
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _load_api_stats(self) -> None:
        """加载API调用统计"""
        try:
            if os.path.exists(self.api_stats_file):
                with open(self.api_stats_file, 'r') as f:
                    stats = json.load(f)
                    today = datetime.now().strftime('%Y-%m-%d')
                    self.api_call_count = stats.get(today, 0)
            else:
                self.api_call_count = 0
        except Exception as e:
            self.logger.warning(f"加载API统计失败: {e}")
            self.api_call_count = 0
    
    def _save_api_stats(self) -> None:
        """保存API调用统计"""
        try:
            stats = {}
            if os.path.exists(self.api_stats_file):
                with open(self.api_stats_file, 'r') as f:
                    stats = json.load(f)
            
            today = datetime.now().strftime('%Y-%m-%d')
            stats[today] = self.api_call_count
            
            with open(self.api_stats_file, 'w') as f:
                json.dump(stats, f, indent=2)
        except Exception as e:
            self.logger.warning(f"保存API统计失败: {e}")
    
    def ensure_data_availability(self, 
                                stocks: List[str], 
                                training_start_date: str, 
                                training_end_date: str,
                                force_refresh: bool = False) -> Dict[str, bool]:
        """
        确保指定股票在训练期间的数据可用性
        
        Args:
            stocks: 股票代码列表
            training_start_date: 训练开始日期 (YYYY-MM-DD)
            training_end_date: 训练结束日期 (YYYY-MM-DD)
            force_refresh: 是否强制刷新所有数据
            
        Returns:
            每个股票的数据准备状态 {stock: success}
        """
        self.logger.info(f"开始确保数据可用性: {len(stocks)}只股票")
        self.logger.info(f"训练期间: {training_start_date} 到 {training_end_date}")
        
        # 分析数据需求
        requirements = self._analyze_data_requirements(stocks, training_start_date, training_end_date)
        
        # 检查缓存状态
        cache_statuses = self._check_cache_statuses(requirements, force_refresh)
        
        # 生成下载计划
        download_plan = self._generate_download_plan(requirements, cache_statuses)
        
        # 执行下载
        results = self._execute_download_plan(download_plan)
        
        # 验证数据完整性
        final_results = self._validate_final_data(requirements, results)
        
        # 保存API统计
        self._save_api_stats()
        
        success_count = sum(1 for success in final_results.values() if success)
        self.logger.info(f"数据准备完成: {success_count}/{len(stocks)} 成功")
        
        return final_results
    
    def _analyze_data_requirements(self, 
                                  stocks: List[str], 
                                  training_start_date: str, 
                                  training_end_date: str) -> List[DataRequirement]:
        """分析数据需求"""
        requirements = []
        
        # 计算新闻数据的开始日期（提前1个月）
        training_start = datetime.strptime(training_start_date, '%Y-%m-%d')
        news_start = training_start - timedelta(days=30)  # 简化为30天
        news_start_date = news_start.strftime('%Y-%m-%d')
        
        for stock in stocks:
            requirement = DataRequirement(
                stock=stock,
                training_start_date=training_start_date,
                training_end_date=training_end_date,
                news_start_date=news_start_date,
                news_end_date=training_end_date
            )
            requirements.append(requirement)
            
            self.logger.info(f"{stock} 数据需求:")
            self.logger.info(f"  训练期间: {training_start_date} 到 {training_end_date}")
            self.logger.info(f"  新闻期间: {news_start_date} 到 {training_end_date}")
        
        return requirements
    
    def _check_cache_statuses(self, 
                             requirements: List[DataRequirement], 
                             force_refresh: bool) -> Dict[str, CacheStatus]:
        """检查缓存状态"""
        cache_statuses = {}
        
        for req in requirements:
            if force_refresh:
                # 强制刷新时，假设所有数据都需要重新下载
                cache_status = CacheStatus(
                    has_ohlcv=False,
                    has_news=False,
                    has_fundamentals=False,
                    ohlcv_range=(None, None),
                    news_range=(None, None),
                    last_update=None,
                    data_quality_score=0.0
                )
            else:
                cache_status = self._check_single_stock_cache(req)
            
            cache_statuses[req.stock] = cache_status
            
            self.logger.info(f"{req.stock} 缓存状态:")
            self.logger.info(f"  OHLCV: {'✓' if cache_status.has_ohlcv else '✗'}")
            self.logger.info(f"  新闻: {'✓' if cache_status.has_news else '✗'}")
            self.logger.info(f"  基本面: {'✓' if cache_status.has_fundamentals else '✗'}")
        
        return cache_statuses
    
    def _check_single_stock_cache(self, requirement: DataRequirement) -> CacheStatus:
        """检查单个股票的缓存状态"""
        stock = requirement.stock
        db_path = get_database_path(stock)
        
        if not os.path.exists(db_path):
            return CacheStatus(
                has_ohlcv=False,
                has_news=False,
                has_fundamentals=False,
                ohlcv_range=(None, None),
                news_range=(None, None),
                last_update=None,
                data_quality_score=0.0
            )
        
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 检查OHLCV数据
            has_ohlcv, ohlcv_range = self._check_ohlcv_data(cursor, stock, requirement)
            
            # 检查新闻数据
            has_news, news_range = self._check_news_data(cursor, stock, requirement)
            
            # 检查基本面数据
            has_fundamentals = self._check_fundamental_data(cursor, stock)
            
            conn.close()
            
            # 计算数据质量分数
            quality_score = (
                (1.0 if has_ohlcv else 0.0) * 0.5 +
                (1.0 if has_news else 0.0) * 0.3 +
                (1.0 if has_fundamentals else 0.0) * 0.2
            )
            
            return CacheStatus(
                has_ohlcv=has_ohlcv,
                has_news=has_news,
                has_fundamentals=has_fundamentals,
                ohlcv_range=ohlcv_range,
                news_range=news_range,
                last_update=datetime.now().isoformat(),
                data_quality_score=quality_score
            )
            
        except Exception as e:
            self.logger.error(f"检查 {stock} 缓存状态失败: {e}")
            return CacheStatus(
                has_ohlcv=False,
                has_news=False,
                has_fundamentals=False,
                ohlcv_range=(None, None),
                news_range=(None, None),
                last_update=None,
                data_quality_score=0.0
            )

    def _check_ohlcv_data(self, cursor: sqlite3.Cursor, stock: str, requirement: DataRequirement) -> Tuple[bool, Tuple[Optional[str], Optional[str]]]:
        """检查OHLCV数据是否满足需求"""
        try:
            # 查询数据库中的OHLCV数据范围
            cursor.execute("""
                SELECT MIN(trade_date), MAX(trade_date), COUNT(*)
                FROM ohlcv
                WHERE ticker = ?
            """, (stock,))

            result = cursor.fetchone()
            if not result or not result[0]:
                return False, (None, None)

            db_start, db_end, count = result

            # 检查是否覆盖训练期间
            training_start = datetime.strptime(requirement.training_start_date, '%Y-%m-%d').date()
            training_end = datetime.strptime(requirement.training_end_date, '%Y-%m-%d').date()
            db_start_date = datetime.strptime(db_start, '%Y-%m-%d').date()
            db_end_date = datetime.strptime(db_end, '%Y-%m-%d').date()

            # 数据库范围应该覆盖训练期间
            has_sufficient_data = (db_start_date <= training_start and db_end_date >= training_end and count > 0)

            return has_sufficient_data, (db_start, db_end)

        except Exception as e:
            self.logger.error(f"检查 {stock} OHLCV数据失败: {e}")
            return False, (None, None)

    def _check_news_data(self, cursor: sqlite3.Cursor, stock: str, requirement: DataRequirement) -> Tuple[bool, Tuple[Optional[str], Optional[str]]]:
        """检查新闻数据是否满足需求"""
        try:
            # 查询数据库中的新闻数据范围
            cursor.execute("""
                SELECT MIN(DATE(time_published)), MAX(DATE(time_published)), COUNT(*)
                FROM news
                WHERE ticker = ?
            """, (stock,))

            result = cursor.fetchone()
            if not result or not result[0]:
                return False, (None, None)

            db_start, db_end, count = result

            # 检查是否覆盖新闻期间（训练期间+提前1个月）
            news_start = datetime.strptime(requirement.news_start_date, '%Y-%m-%d').date()
            news_end = datetime.strptime(requirement.news_end_date, '%Y-%m-%d').date()
            db_start_date = datetime.strptime(db_start, '%Y-%m-%d').date()
            db_end_date = datetime.strptime(db_end, '%Y-%m-%d').date()

            # 数据库范围应该覆盖新闻期间
            has_sufficient_data = (db_start_date <= news_start and db_end_date >= news_end and count > 0)

            return has_sufficient_data, (db_start, db_end)

        except Exception as e:
            self.logger.error(f"检查 {stock} 新闻数据失败: {e}")
            return False, (None, None)

    def _check_fundamental_data(self, cursor: sqlite3.Cursor, stock: str) -> bool:
        """检查基本面数据是否存在"""
        try:
            # 检查年度财务数据
            cursor.execute("""
                SELECT COUNT(*) FROM annual_financials WHERE ticker = ?
            """, (stock,))
            annual_count = cursor.fetchone()[0]

            # 检查季度财务数据
            cursor.execute("""
                SELECT COUNT(*) FROM quarterly_financials WHERE ticker = ?
            """, (stock,))
            quarterly_count = cursor.fetchone()[0]

            # 至少要有一些基本面数据
            return annual_count > 0 or quarterly_count > 0

        except Exception as e:
            self.logger.error(f"检查 {stock} 基本面数据失败: {e}")
            return False

    def _generate_download_plan(self,
                               requirements: List[DataRequirement],
                               cache_statuses: Dict[str, CacheStatus]) -> Dict[str, Dict[str, bool]]:
        """生成下载计划"""
        download_plan = {}

        for req in requirements:
            stock = req.stock
            cache_status = cache_statuses[stock]

            plan = {
                'need_ohlcv': not cache_status.has_ohlcv,
                'need_news': not cache_status.has_news,
                'need_fundamentals': not cache_status.has_fundamentals
            }

            download_plan[stock] = plan

            needed_items = [k.replace('need_', '') for k, v in plan.items() if v]
            if needed_items:
                self.logger.info(f"{stock} 需要下载: {', '.join(needed_items)}")
            else:
                self.logger.info(f"{stock} 数据完整，无需下载")

        return download_plan

    def _execute_download_plan(self, download_plan: Dict[str, Dict[str, bool]]) -> Dict[str, bool]:
        """执行下载计划"""
        results = {}

        for stock, plan in download_plan.items():
            if not any(plan.values()):
                # 无需下载任何数据
                results[stock] = True
                continue

            self.logger.info(f"开始为 {stock} 下载数据...")

            # 检查API调用限制
            if self.api_call_count >= self.max_api_calls_per_day:
                self.logger.warning(f"已达到每日API调用限制 ({self.max_api_calls_per_day})，跳过 {stock}")
                results[stock] = False
                continue

            try:
                # 使用新的脚本集成器进行下载
                download_results = self.script_integrator.download_comprehensive_data(
                    stock,
                    "2020-01-01",  # 使用较早的开始日期确保获取完整历史数据
                    datetime.now().strftime('%Y-%m-%d'),  # 到当前日期
                    include_ohlcv=plan.get('need_ohlcv', True),
                    include_news=plan.get('need_news', True),
                    include_fundamental=plan.get('need_fundamentals', True)
                )

                # 评估下载成功率
                data_types = ['ohlcv', 'news', 'fundamental']
                successful_downloads = sum(1 for dt in data_types if download_results.get(dt, False))
                total_needed = sum(1 for dt in data_types if plan.get(f'need_{dt}', True))

                # 如果至少有一半的数据下载成功，认为整体成功
                success = successful_downloads >= (total_needed * 0.5) if total_needed > 0 else True

                if success:
                    self.logger.info(f"✅ {stock} 数据下载成功 ({successful_downloads}/{total_needed})")
                    # 估算API调用次数
                    estimated_calls = successful_downloads * 2  # 每种数据类型估算2次调用
                    self.api_call_count += estimated_calls

                    # 更新缓存元数据
                    self.cache_manager.update_stock_metadata(stock)
                else:
                    self.logger.error(f"❌ {stock} 数据下载失败 ({successful_downloads}/{total_needed})")

                results[stock] = success

            except Exception as e:
                self.logger.error(f"下载 {stock} 数据时发生异常: {e}")
                results[stock] = False

            # 在下载之间添加延迟，避免API限制
            if stock != list(download_plan.keys())[-1]:  # 不是最后一个股票
                time.sleep(12)  # 12秒延迟

        return results

    def _validate_final_data(self,
                            requirements: List[DataRequirement],
                            download_results: Dict[str, bool]) -> Dict[str, bool]:
        """验证最终数据完整性"""
        final_results = {}

        for req in requirements:
            stock = req.stock

            if not download_results.get(stock, False):
                final_results[stock] = False
                continue

            # 重新检查数据完整性
            cache_status = self._check_single_stock_cache(req)

            # 数据质量分数阈值
            quality_threshold = 0.7
            is_valid = cache_status.data_quality_score >= quality_threshold

            if is_valid:
                self.logger.info(f"✅ {stock} 数据验证通过 (质量分数: {cache_status.data_quality_score:.2f})")
            else:
                self.logger.warning(f"⚠️ {stock} 数据质量不足 (质量分数: {cache_status.data_quality_score:.2f})")

            final_results[stock] = is_valid

        return final_results

    def get_data_summary(self, stocks: List[str]) -> Dict[str, Any]:
        """获取数据摘要信息"""
        summary = {
            "total_stocks": len(stocks),
            "stocks_with_data": 0,
            "api_calls_used": self.api_call_count,
            "api_calls_remaining": max(0, self.max_api_calls_per_day - self.api_call_count),
            "stock_details": {}
        }

        for stock in stocks:
            db_path = get_database_path(stock)
            if os.path.exists(db_path):
                try:
                    conn = sqlite3.connect(db_path)
                    cursor = conn.cursor()

                    # 获取各类数据的统计
                    cursor.execute("SELECT COUNT(*) FROM ohlcv WHERE ticker = ?", (stock,))
                    ohlcv_count = cursor.fetchone()[0]

                    cursor.execute("SELECT COUNT(*) FROM news WHERE ticker = ?", (stock,))
                    news_count = cursor.fetchone()[0]

                    cursor.execute("SELECT COUNT(*) FROM annual_financials WHERE ticker = ?", (stock,))
                    annual_count = cursor.fetchone()[0]

                    cursor.execute("SELECT COUNT(*) FROM quarterly_financials WHERE ticker = ?", (stock,))
                    quarterly_count = cursor.fetchone()[0]

                    conn.close()

                    if ohlcv_count > 0:
                        summary["stocks_with_data"] += 1

                    summary["stock_details"][stock] = {
                        "ohlcv_records": ohlcv_count,
                        "news_records": news_count,
                        "annual_financials": annual_count,
                        "quarterly_financials": quarterly_count,
                        "has_basic_data": ohlcv_count > 0
                    }

                except Exception as e:
                    summary["stock_details"][stock] = {
                        "error": str(e),
                        "has_basic_data": False
                    }
            else:
                summary["stock_details"][stock] = {
                    "database_exists": False,
                    "has_basic_data": False
                }

        return summary


# 便捷函数
def ensure_trading_data(stocks: List[str],
                       start_date: str,
                       end_date: str,
                       force_refresh: bool = False,
                       verbose: bool = True) -> Dict[str, bool]:
    """
    便捷函数：确保交易数据可用性

    Args:
        stocks: 股票代码列表
        start_date: 开始日期 (YYYY-MM-DD)
        end_date: 结束日期 (YYYY-MM-DD)
        force_refresh: 是否强制刷新
        verbose: 是否显示详细日志

    Returns:
        每个股票的数据准备状态
    """
    manager = SmartDataDownloadManager(verbose=verbose)
    return manager.ensure_data_availability(stocks, start_date, end_date, force_refresh)


if __name__ == "__main__":
    # 测试代码
    import argparse

    parser = argparse.ArgumentParser(description="智能数据下载管理器")
    parser.add_argument("--stocks", nargs="+", default=["AAPL"], help="股票代码列表")
    parser.add_argument("--start-date", default="2025-01-01", help="开始日期")
    parser.add_argument("--end-date", default="2025-03-31", help="结束日期")
    parser.add_argument("--force-refresh", action="store_true", help="强制刷新所有数据")
    parser.add_argument("--summary", action="store_true", help="显示数据摘要")

    args = parser.parse_args()

    manager = SmartDataDownloadManager(verbose=True)

    if args.summary:
        summary = manager.get_data_summary(args.stocks)
        print("\n📊 数据摘要:")
        print(json.dumps(summary, indent=2, ensure_ascii=False))
    else:
        results = manager.ensure_data_availability(
            args.stocks,
            args.start_date,
            args.end_date,
            args.force_refresh
        )

        print("\n📋 最终结果:")
        for stock, success in results.items():
            status = "✅ 成功" if success else "❌ 失败"
            print(f"  {stock}: {status}")

        # 显示摘要
        summary = manager.get_data_summary(args.stocks)
        print(f"\n📈 API使用情况: {summary['api_calls_used']}/{manager.max_api_calls_per_day}")
        print(f"📊 有数据的股票: {summary['stocks_with_data']}/{summary['total_stocks']}")
