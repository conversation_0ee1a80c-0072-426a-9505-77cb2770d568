"""
LLM 接口模块 (LLM Interface Module)

本模块提供一个通用的LLM接口，用于与不同的大语言模型提供商进行交互。
它抽象了不同SDK的细节，提供一个统一的 `analyze` 方法。

主要功能：
1. 支持多种LLM提供商（当前实现zhipuai，可扩展至OpenAI等）
2. 通过环境变量安全地管理API密钥
3. 提供统一的分析接口
"""

import os
import json
import logging
from typing import Optional, Dict, Any

# 动态导入，避免在未使用时强制要求安装
try:
    import zhipuai
except ImportError:
    zhipuai = None

try:
    import openai
except ImportError:
    openai = None


class LLMInterface:
    """
    通用LLM接口类
    
    根据指定的提供商，初始化并封装LLM客户端。
    """
    
    def __init__(self, provider: Optional[str] = None, logger: Optional[logging.Logger] = None):
        """
        初始化LLM接口
        
        参数:
            provider: LLM提供商名称 (例如 "zhipuai", "openai")
            logger: 日志记录器
        """
        self.logger = logger or self._create_default_logger()
        self.provider = provider
        self.client = None
        
        if self.provider:
            self.provider = self.provider.lower()
            self._initialize_client()

    def _create_default_logger(self) -> logging.Logger:
        """创建默认日志记录器"""
        logger = logging.getLogger(f"{__name__}.LLMInterface")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger

    def _initialize_client(self) -> None:
        """根据提供商初始化LLM客户端"""
        self.logger.info(f"尝试初始化LLM提供商: {self.provider}")
        
        if self.provider == "zhipuai":
            if zhipuai is None:
                self.logger.warning("ZhipuAI SDK (zhipuai) 未安装。请运行 'pip install zhipuai'")
                return
            
            api_key = os.environ.get("ZHIPUAI_API_KEY")
            if not api_key:
                self.logger.warning("未找到 ZHIPUAI_API_KEY 环境变量。")
                return
            
            try:
                self.client = zhipuai.ZhipuAI(api_key=api_key)
                self.logger.info("ZhipuAI 客户端初始化成功")
            except Exception as e:
                self.logger.error(f"初始化 ZhipuAI 客户端失败: {e}")

        elif self.provider == "openai":
            if openai is None:
                self.logger.warning("OpenAI SDK (openai) 未安装。请运行 'pip install openai'")
                return
                
            api_key = os.environ.get("OPENAI_API_KEY")
            if not api_key:
                self.logger.warning("未找到 OPENAI_API_KEY 环境变量。")
                return
            
            try:
                self.client = openai.OpenAI(api_key=api_key)
                self.logger.info("OpenAI 客户端初始化成功")
            except Exception as e:
                self.logger.error(f"初始化 OpenAI 客户端失败: {e}")

        else:
            self.logger.error(f"不支持的LLM提供商: {self.provider}")

    def analyze(self, prompt: str, model: str, **kwargs) -> Optional[Dict[str, Any]]:
        """
        使用指定的模型进行分析
        
        参数:
            prompt: 发送给模型的提示
            model: 要使用的模型名称 (例如 "glm-4-flash")
            **kwargs: 传递给LLM API的其他参数
            
        返回:
            LLM的分析结果字典，如果失败则返回None
        """
        if not self.client:
            self.logger.error("LLM客户端未初始化，无法执行分析")
            return None
            
        try:
            self.logger.debug(f"向模型 {model} 发送请求...")
            
            if self.provider == "zhipuai":
                response = self.client.chat.completions.create(
                    model=model,
                    messages=[{"role": "user", "content": prompt}],
                    **kwargs
                )
                # 假设返回的是JSON字符串
                content = response.choices[0].message.content
                return self._parse_json_response(content)

            elif self.provider == "openai":
                response = self.client.chat.completions.create(
                    model=model,
                    messages=[{"role": "user", "content": prompt}],
                    **kwargs
                )
                content = response.choices[0].message.content
                return self._parse_json_response(content)

            else:
                self.logger.error(f"提供商 {self.provider} 的分析逻辑未实现")
                return None

        except Exception as e:
            self.logger.error(f"LLM分析请求失败: {e}")
            return None

    def _parse_json_response(self, content: str) -> Optional[Dict[str, Any]]:
        """
        解析LLM返回的JSON字符串

        参数:
            content: LLM返回的内容字符串

        返回:
            解析后的字典，如果失败则返回包含原始内容的字典
        """
        try:
            # 移除代码块标记
            cleaned_content = content.strip().replace("```json", "").replace("```", "").strip()
            return json.loads(cleaned_content)
        except json.JSONDecodeError as e:
            self.logger.debug(f"LLM响应不是JSON格式，返回文本内容: {e}")
            # 如果不是JSON格式，返回包含原始文本的字典
            return {"content": content, "type": "text_response"}
