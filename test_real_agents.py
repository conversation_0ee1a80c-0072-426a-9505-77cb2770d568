#!/usr/bin/env python3
"""
测试真实智能体的简单脚本

用于快速验证系统是否正确使用真实的LLM智能体
"""

import os
import sys
from dotenv import load_dotenv

def test_agent_creation():
    """测试智能体创建"""
    print("🧪 测试智能体创建...")
    
    try:
        from contribution_assessment.llm_interface import LLMInterface
        from agents.agent_factory import AgentFactory
        
        # 检查环境变量
        api_key = os.environ.get("ZHIPUAI_API_KEY")
        if not api_key:
            print("❌ 未找到 ZHIPUAI_API_KEY，请设置环境变量")
            return False
        
        # 创建LLM接口
        llm_interface = LLMInterface(provider="zhipuai")
        if not llm_interface.client:
            print("❌ LLM接口初始化失败")
            return False
        
        print("✅ LLM接口初始化成功")
        
        # 创建智能体
        agent_factory = AgentFactory(llm_interface=llm_interface)
        naa_agent = agent_factory.create_agent("NAA")
        
        if naa_agent and hasattr(naa_agent, 'llm_interface') and naa_agent.llm_interface:
            print("✅ 智能体创建成功并配置了LLM接口")
            return True
        else:
            print("❌ 智能体创建失败或未配置LLM接口")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def test_contribution_assessor():
    """测试贡献度评估器"""
    print("\n📊 测试贡献度评估器...")
    
    try:
        from contribution_assessment import ContributionAssessor
        from contribution_assessment.llm_interface import LLMInterface
        from agents.agent_factory import AgentFactory
        
        # 创建智能体实例
        llm_interface = LLMInterface(provider="zhipuai")
        if not llm_interface.client:
            print("❌ LLM接口不可用")
            return False
        
        agent_factory = AgentFactory(llm_interface=llm_interface)
        agent_instances = {
            "NAA": agent_factory.create_agent("NAA"),
            "TAA": agent_factory.create_agent("TAA"),
            "FAA": agent_factory.create_agent("FAA")
        }
        
        print(f"✅ 创建了 {len(agent_instances)} 个智能体实例")
        
        # 创建简单配置
        config = {
            "start_date": "2023-01-01",
            "end_date": "2023-01-02",
            "stocks": ["AAPL"],
            "starting_cash": 10000,
            "simulation_days": 1
        }
        
        # 创建评估器
        assessor = ContributionAssessor(
            config=config,
            agents=agent_instances,
            llm_provider="zhipuai"
        )
        
        # 检查评估器状态
        print(f"评估器LLM接口: {'✅ 可用' if assessor.llm_interface and assessor.llm_interface.client else '❌ 不可用'}")
        print(f"评估器智能体数量: {len(assessor.agents) if assessor.agents else 0}")
        
        # 模拟运行（不实际执行完整评估）
        print("✅ 贡献度评估器配置正确")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def main():
    """主函数"""
    load_dotenv()
    
    print("🔧 真实智能体测试")
    print("=" * 40)
    
    # 检查基本环境
    if not os.environ.get("ZHIPUAI_API_KEY"):
        print("❌ 请先设置 ZHIPUAI_API_KEY 环境变量")
        print("export ZHIPUAI_API_KEY=your_api_key")
        sys.exit(1)
    
    try:
        import zhipuai
        print("✅ zhipuai SDK 已安装")
    except ImportError:
        print("❌ zhipuai SDK 未安装，请运行: pip install zhipuai")
        sys.exit(1)
    
    # 运行测试
    tests = [
        ("智能体创建", test_agent_creation),
        ("贡献度评估器", test_contribution_assessor)
    ]
    
    passed = 0
    for test_name, test_func in tests:
        print(f"\n{'-' * 30}")
        if test_func():
            passed += 1
    
    print(f"\n{'=' * 40}")
    print(f"测试结果: {passed}/{len(tests)} 通过")
    
    if passed == len(tests):
        print("🎉 所有测试通过！系统已正确配置。")
        print("\n现在可以运行:")
        print("python run_with_real_agents.py --llm-provider zhipuai")
    else:
        print("⚠️  部分测试失败，请检查配置。")
        sys.exit(1)


if __name__ == "__main__":
    main()
