#!/usr/bin/env python3
"""
备份管理器 (Backup Manager)

提供自动化数据备份和恢复功能：
1. 定期自动备份
2. 增量备份支持
3. 数据验证和完整性检查
4. 备份恢复功能
5. 存储策略管理

作者: AI Assistant
创建时间: 2025-07-04
"""

import os
import json
import shutil
import sqlite3
import hashlib
import logging
import threading
import time
import gzip
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path
import schedule

@dataclass
class BackupConfig:
    """备份配置"""
    enabled: bool = True
    backup_interval_hours: int = 24
    max_backups_to_keep: int = 30
    compression_enabled: bool = True
    incremental_backup: bool = True
    backup_validation: bool = True
    auto_cleanup: bool = True
    backup_base_path: str = "data/backups"

@dataclass
class BackupRecord:
    """备份记录"""
    backup_id: str
    backup_type: str  # "full", "incremental"
    timestamp: str
    file_path: str
    file_size_mb: float
    checksum: str
    validation_status: str
    metadata: Dict[str, Any]

class BackupManager:
    """
    备份管理器
    
    提供全面的数据备份和恢复功能
    """
    
    def __init__(self, 
                 storage_manager,
                 config: Optional[BackupConfig] = None,
                 logger: Optional[logging.Logger] = None):
        """
        初始化备份管理器
        
        参数:
            storage_manager: 存储管理器实例
            config: 备份配置
            logger: 日志记录器
        """
        self.storage_manager = storage_manager
        self.config = config or BackupConfig()
        self.logger = logger or self._create_default_logger()
        
        # 备份状态
        self.backup_thread = None
        self.backup_running = False
        self.last_backup_time = None
        self.backup_records = []
        
        # 初始化备份目录
        self._initialize_backup_directory()
        
        # 加载备份记录
        self._load_backup_records()
        
        # 启动自动备份（如果启用）
        if self.config.enabled:
            self._start_auto_backup()
        
        self.logger.info("备份管理器初始化完成")
    
    def _create_default_logger(self) -> logging.Logger:
        """创建默认日志记录器"""
        logger = logging.getLogger(f"{__name__}.BackupManager")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def _initialize_backup_directory(self):
        """初始化备份目录"""
        try:
            os.makedirs(self.config.backup_base_path, exist_ok=True)
            
            # 创建子目录
            subdirs = ["full", "incremental", "metadata"]
            for subdir in subdirs:
                os.makedirs(os.path.join(self.config.backup_base_path, subdir), exist_ok=True)
            
            self.logger.debug(f"备份目录初始化完成: {self.config.backup_base_path}")
            
        except Exception as e:
            self.logger.error(f"初始化备份目录失败: {e}")
            raise
    
    def _load_backup_records(self):
        """加载备份记录"""
        try:
            records_file = os.path.join(self.config.backup_base_path, "metadata", "backup_records.json")
            
            if os.path.exists(records_file):
                with open(records_file, 'r', encoding='utf-8') as f:
                    records_data = json.load(f)
                
                self.backup_records = [
                    BackupRecord(**record) for record in records_data
                ]
                
                # 更新最后备份时间
                if self.backup_records:
                    latest_record = max(self.backup_records, key=lambda x: x.timestamp)
                    self.last_backup_time = datetime.fromisoformat(latest_record.timestamp)
                
                self.logger.info(f"加载了 {len(self.backup_records)} 条备份记录")
            
        except Exception as e:
            self.logger.error(f"加载备份记录失败: {e}")
            self.backup_records = []
    
    def _save_backup_records(self):
        """保存备份记录"""
        try:
            records_file = os.path.join(self.config.backup_base_path, "metadata", "backup_records.json")
            
            records_data = [asdict(record) for record in self.backup_records]
            
            with open(records_file, 'w', encoding='utf-8') as f:
                json.dump(records_data, f, indent=2, ensure_ascii=False)
            
            self.logger.debug("备份记录已保存")
            
        except Exception as e:
            self.logger.error(f"保存备份记录失败: {e}")
    
    def create_full_backup(self, backup_name: Optional[str] = None) -> Dict[str, Any]:
        """
        创建完整备份
        
        参数:
            backup_name: 备份名称
            
        返回:
            备份结果
        """
        try:
            timestamp = datetime.now()
            backup_id = backup_name or f"full_backup_{timestamp.strftime('%Y%m%d_%H%M%S')}"
            
            self.logger.info(f"开始创建完整备份: {backup_id}")
            
            # 创建备份目录
            backup_dir = os.path.join(self.config.backup_base_path, "full", backup_id)
            os.makedirs(backup_dir, exist_ok=True)
            
            # 备份数据库
            db_backup_path = self._backup_database(backup_dir)
            
            # 备份文件数据
            files_backup_paths = self._backup_files(backup_dir)
            
            # 创建备份元数据
            metadata = {
                "backup_id": backup_id,
                "backup_type": "full",
                "timestamp": timestamp.isoformat(),
                "database_backup": db_backup_path,
                "files_backup": files_backup_paths,
                "storage_config": asdict(self.storage_manager.config)
            }
            
            # 保存元数据
            metadata_file = os.path.join(backup_dir, "backup_metadata.json")
            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=2, ensure_ascii=False)
            
            # 压缩备份（如果启用）
            final_backup_path = backup_dir
            if self.config.compression_enabled:
                final_backup_path = self._compress_backup(backup_dir)
                shutil.rmtree(backup_dir)  # 删除未压缩版本
            
            # 计算校验和
            checksum = self._calculate_checksum(final_backup_path)
            
            # 计算大小
            backup_size = self._calculate_backup_size(final_backup_path)
            
            # 验证备份（如果启用）
            validation_status = "success"
            if self.config.backup_validation:
                validation_status = self._validate_backup(final_backup_path, metadata)
            
            # 创建备份记录
            backup_record = BackupRecord(
                backup_id=backup_id,
                backup_type="full",
                timestamp=timestamp.isoformat(),
                file_path=final_backup_path,
                file_size_mb=backup_size,
                checksum=checksum,
                validation_status=validation_status,
                metadata=metadata
            )
            
            # 添加到记录列表
            self.backup_records.append(backup_record)
            self._save_backup_records()
            
            # 更新最后备份时间
            self.last_backup_time = timestamp
            
            # 清理旧备份（如果启用）
            if self.config.auto_cleanup:
                self._cleanup_old_backups()
            
            result = {
                "success": True,
                "backup_id": backup_id,
                "backup_path": final_backup_path,
                "backup_size_mb": backup_size,
                "checksum": checksum,
                "validation_status": validation_status,
                "timestamp": timestamp.isoformat()
            }
            
            self.logger.info(f"完整备份创建成功: {backup_id} ({backup_size:.2f} MB)")
            return result
            
        except Exception as e:
            self.logger.error(f"创建完整备份失败: {e}")
            return {"success": False, "error": str(e)}
    
    def create_incremental_backup(self) -> Dict[str, Any]:
        """
        创建增量备份
        
        返回:
            备份结果
        """
        try:
            if not self.config.incremental_backup:
                return {"success": False, "error": "增量备份未启用"}
            
            # 查找最后一次完整备份
            full_backups = [r for r in self.backup_records if r.backup_type == "full"]
            if not full_backups:
                self.logger.info("未找到完整备份，创建完整备份")
                return self.create_full_backup()
            
            last_full_backup = max(full_backups, key=lambda x: x.timestamp)
            last_backup_time = datetime.fromisoformat(last_full_backup.timestamp)
            
            timestamp = datetime.now()
            backup_id = f"incremental_backup_{timestamp.strftime('%Y%m%d_%H%M%S')}"
            
            self.logger.info(f"开始创建增量备份: {backup_id}")
            
            # 创建备份目录
            backup_dir = os.path.join(self.config.backup_base_path, "incremental", backup_id)
            os.makedirs(backup_dir, exist_ok=True)
            
            # 获取变更的数据
            changed_data = self._get_changed_data_since(last_backup_time)
            
            if not changed_data["has_changes"]:
                self.logger.info("自上次备份以来无数据变更")
                shutil.rmtree(backup_dir)
                return {"success": True, "message": "无需增量备份", "changes": False}
            
            # 备份变更的数据
            changes_file = os.path.join(backup_dir, "incremental_changes.json")
            with open(changes_file, 'w', encoding='utf-8') as f:
                json.dump(changed_data, f, indent=2, ensure_ascii=False)
            
            # 创建备份元数据
            metadata = {
                "backup_id": backup_id,
                "backup_type": "incremental",
                "timestamp": timestamp.isoformat(),
                "base_backup_id": last_full_backup.backup_id,
                "changes_since": last_backup_time.isoformat(),
                "changes_summary": changed_data["summary"]
            }
            
            # 保存元数据
            metadata_file = os.path.join(backup_dir, "backup_metadata.json")
            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=2, ensure_ascii=False)
            
            # 压缩备份（如果启用）
            final_backup_path = backup_dir
            if self.config.compression_enabled:
                final_backup_path = self._compress_backup(backup_dir)
                shutil.rmtree(backup_dir)
            
            # 计算校验和和大小
            checksum = self._calculate_checksum(final_backup_path)
            backup_size = self._calculate_backup_size(final_backup_path)
            
            # 创建备份记录
            backup_record = BackupRecord(
                backup_id=backup_id,
                backup_type="incremental",
                timestamp=timestamp.isoformat(),
                file_path=final_backup_path,
                file_size_mb=backup_size,
                checksum=checksum,
                validation_status="success",
                metadata=metadata
            )
            
            self.backup_records.append(backup_record)
            self._save_backup_records()
            self.last_backup_time = timestamp
            
            result = {
                "success": True,
                "backup_id": backup_id,
                "backup_path": final_backup_path,
                "backup_size_mb": backup_size,
                "changes": True,
                "changes_summary": changed_data["summary"],
                "timestamp": timestamp.isoformat()
            }
            
            self.logger.info(f"增量备份创建成功: {backup_id} ({backup_size:.2f} MB)")
            return result
            
        except Exception as e:
            self.logger.error(f"创建增量备份失败: {e}")
            return {"success": False, "error": str(e)}
    
    def _backup_database(self, backup_dir: str) -> str:
        """备份数据库"""
        try:
            db_source = self.storage_manager.config.database_path
            db_backup_path = os.path.join(backup_dir, "comprehensive_storage.db")
            
            if os.path.exists(db_source):
                shutil.copy2(db_source, db_backup_path)
                self.logger.debug(f"数据库备份完成: {db_backup_path}")
                return db_backup_path
            else:
                self.logger.warning(f"数据库文件不存在: {db_source}")
                return ""
                
        except Exception as e:
            self.logger.error(f"备份数据库失败: {e}")
            return ""
    
    def _backup_files(self, backup_dir: str) -> List[str]:
        """备份文件数据"""
        backup_paths = []
        
        try:
            # 备份各类数据文件
            data_dirs = [
                (self.storage_manager.config.trading_data_path, "trading"),
                (self.storage_manager.config.prompts_data_path, "prompts"),
                (self.storage_manager.config.visualizations_path, "visualizations")
            ]
            
            for source_dir, backup_name in data_dirs:
                if os.path.exists(source_dir):
                    dest_dir = os.path.join(backup_dir, backup_name)
                    shutil.copytree(source_dir, dest_dir, dirs_exist_ok=True)
                    backup_paths.append(dest_dir)
                    self.logger.debug(f"文件备份完成: {source_dir} -> {dest_dir}")
            
            return backup_paths
            
        except Exception as e:
            self.logger.error(f"备份文件失败: {e}")
            return []

    def _compress_backup(self, backup_dir: str) -> str:
        """压缩备份目录"""
        try:
            compressed_path = f"{backup_dir}.tar.gz"
            shutil.make_archive(backup_dir, 'gztar', backup_dir)
            self.logger.debug(f"备份压缩完成: {compressed_path}")
            return compressed_path

        except Exception as e:
            self.logger.error(f"压缩备份失败: {e}")
            return backup_dir

    def _calculate_checksum(self, file_path: str) -> str:
        """计算文件校验和"""
        try:
            hash_md5 = hashlib.md5()

            if os.path.isfile(file_path):
                with open(file_path, "rb") as f:
                    for chunk in iter(lambda: f.read(4096), b""):
                        hash_md5.update(chunk)
            else:
                # 目录的话，计算所有文件的校验和
                for root, _, files in os.walk(file_path):
                    for file in sorted(files):
                        file_path_full = os.path.join(root, file)
                        with open(file_path_full, "rb") as f:
                            for chunk in iter(lambda: f.read(4096), b""):
                                hash_md5.update(chunk)

            return hash_md5.hexdigest()

        except Exception as e:
            self.logger.error(f"计算校验和失败: {e}")
            return ""

    def _calculate_backup_size(self, backup_path: str) -> float:
        """计算备份大小（MB）"""
        try:
            if os.path.isfile(backup_path):
                size_bytes = os.path.getsize(backup_path)
            else:
                size_bytes = sum(
                    os.path.getsize(os.path.join(dirpath, filename))
                    for dirpath, _, filenames in os.walk(backup_path)
                    for filename in filenames
                )

            return size_bytes / (1024 * 1024)  # 转换为MB

        except Exception as e:
            self.logger.error(f"计算备份大小失败: {e}")
            return 0.0

    def _validate_backup(self, backup_path: str, metadata: Dict[str, Any]) -> str:
        """验证备份完整性"""
        try:
            # 检查文件是否存在
            if not os.path.exists(backup_path):
                return "failed_missing_file"

            # 检查是否可以读取
            if os.path.isfile(backup_path):
                try:
                    with open(backup_path, 'rb') as f:
                        f.read(1024)  # 尝试读取前1KB
                except:
                    return "failed_read_error"

            # 如果是压缩文件，尝试解压缩测试
            if backup_path.endswith('.tar.gz'):
                import tarfile
                try:
                    with tarfile.open(backup_path, 'r:gz') as tar:
                        tar.getnames()  # 获取文件列表
                except:
                    return "failed_compression_error"

            return "success"

        except Exception as e:
            self.logger.error(f"验证备份失败: {e}")
            return "failed_validation_error"

    def _get_changed_data_since(self, since_time: datetime) -> Dict[str, Any]:
        """获取自指定时间以来的数据变更"""
        try:
            changes = {
                "has_changes": False,
                "summary": {
                    "new_trading_sessions": 0,
                    "new_prompt_optimizations": 0,
                    "new_visualizations": 0
                },
                "details": {}
            }

            # 检查交易会话变更
            trading_sessions = self.storage_manager.get_trading_sessions(
                start_date=since_time.strftime('%Y-%m-%d')
            )
            new_sessions = [s for s in trading_sessions
                          if datetime.fromisoformat(s.get('timestamp', '')) > since_time]

            if new_sessions:
                changes["has_changes"] = True
                changes["summary"]["new_trading_sessions"] = len(new_sessions)
                changes["details"]["trading_sessions"] = [s["session_id"] for s in new_sessions]

            # 检查提示词优化变更
            optimizations = self.storage_manager.get_prompt_optimizations(
                start_date=since_time.strftime('%Y-%m-%d')
            )
            new_optimizations = [o for o in optimizations
                               if datetime.fromisoformat(o.get('timestamp', '')) > since_time]

            if new_optimizations:
                changes["has_changes"] = True
                changes["summary"]["new_prompt_optimizations"] = len(new_optimizations)
                changes["details"]["prompt_optimizations"] = [o["optimization_id"] for o in new_optimizations]

            return changes

        except Exception as e:
            self.logger.error(f"获取数据变更失败: {e}")
            return {"has_changes": False, "error": str(e)}

    def _cleanup_old_backups(self):
        """清理旧备份"""
        try:
            if len(self.backup_records) <= self.config.max_backups_to_keep:
                return

            # 按时间排序，保留最新的备份
            sorted_records = sorted(self.backup_records, key=lambda x: x.timestamp, reverse=True)
            records_to_keep = sorted_records[:self.config.max_backups_to_keep]
            records_to_delete = sorted_records[self.config.max_backups_to_keep:]

            deleted_count = 0
            for record in records_to_delete:
                try:
                    if os.path.exists(record.file_path):
                        if os.path.isfile(record.file_path):
                            os.remove(record.file_path)
                        else:
                            shutil.rmtree(record.file_path)
                        deleted_count += 1
                        self.logger.debug(f"删除旧备份: {record.backup_id}")
                except Exception as e:
                    self.logger.error(f"删除备份失败 {record.backup_id}: {e}")

            # 更新备份记录
            self.backup_records = records_to_keep
            self._save_backup_records()

            if deleted_count > 0:
                self.logger.info(f"清理了 {deleted_count} 个旧备份")

        except Exception as e:
            self.logger.error(f"清理旧备份失败: {e}")

    def _start_auto_backup(self):
        """启动自动备份"""
        try:
            def backup_job():
                try:
                    if self.config.incremental_backup:
                        result = self.create_incremental_backup()
                    else:
                        result = self.create_full_backup()

                    if result.get("success"):
                        self.logger.info("自动备份完成")
                    else:
                        self.logger.error(f"自动备份失败: {result.get('error', '未知错误')}")

                except Exception as e:
                    self.logger.error(f"自动备份异常: {e}")

            # 使用schedule库安排定期备份
            schedule.every(self.config.backup_interval_hours).hours.do(backup_job)

            def run_scheduler():
                while self.backup_running:
                    schedule.run_pending()
                    time.sleep(60)  # 每分钟检查一次

            self.backup_running = True
            self.backup_thread = threading.Thread(target=run_scheduler, daemon=True)
            self.backup_thread.start()

            self.logger.info(f"自动备份已启动，间隔: {self.config.backup_interval_hours} 小时")

        except Exception as e:
            self.logger.error(f"启动自动备份失败: {e}")

    def stop_auto_backup(self):
        """停止自动备份"""
        try:
            self.backup_running = False
            if self.backup_thread:
                self.backup_thread.join(timeout=5)

            self.logger.info("自动备份已停止")

        except Exception as e:
            self.logger.error(f"停止自动备份失败: {e}")

    def get_backup_status(self) -> Dict[str, Any]:
        """获取备份状态"""
        try:
            total_backups = len(self.backup_records)
            full_backups = len([r for r in self.backup_records if r.backup_type == "full"])
            incremental_backups = len([r for r in self.backup_records if r.backup_type == "incremental"])

            total_size = sum(r.file_size_mb for r in self.backup_records)

            latest_backup = max(self.backup_records, key=lambda x: x.timestamp) if self.backup_records else None

            return {
                "enabled": self.config.enabled,
                "auto_backup_running": self.backup_running,
                "total_backups": total_backups,
                "full_backups": full_backups,
                "incremental_backups": incremental_backups,
                "total_size_mb": total_size,
                "last_backup_time": self.last_backup_time.isoformat() if self.last_backup_time else None,
                "latest_backup": {
                    "backup_id": latest_backup.backup_id,
                    "backup_type": latest_backup.backup_type,
                    "timestamp": latest_backup.timestamp,
                    "size_mb": latest_backup.file_size_mb
                } if latest_backup else None,
                "backup_interval_hours": self.config.backup_interval_hours,
                "max_backups_to_keep": self.config.max_backups_to_keep
            }

        except Exception as e:
            self.logger.error(f"获取备份状态失败: {e}")
            return {"enabled": False, "error": str(e)}

    def list_backups(self) -> List[Dict[str, Any]]:
        """列出所有备份"""
        try:
            backups = []
            for record in sorted(self.backup_records, key=lambda x: x.timestamp, reverse=True):
                backups.append({
                    "backup_id": record.backup_id,
                    "backup_type": record.backup_type,
                    "timestamp": record.timestamp,
                    "file_path": record.file_path,
                    "file_size_mb": record.file_size_mb,
                    "checksum": record.checksum,
                    "validation_status": record.validation_status,
                    "exists": os.path.exists(record.file_path)
                })

            return backups

        except Exception as e:
            self.logger.error(f"列出备份失败: {e}")
            return []
