#!/usr/bin/env python3
"""
下载脚本集成器

统一管理和调用现有的数据下载脚本，包括：
1. get_OHLCV_data.py - K线数据下载
2. get_news_data.py - 新闻数据下载
3. get_fundamental_data.py - 基本面数据下载

提供统一的接口和错误处理机制
"""

import os
import sys
import subprocess
import time
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
import logging

# 添加项目根目录到路径
current_script_path = os.path.abspath(__file__)
project_root = os.path.dirname(os.path.dirname(current_script_path))
sys.path.append(project_root)

try:
    from config import ALPHAVANTAGE_API_KEY
except ImportError as e:
    print(f"导入配置失败: {e}", file=sys.stderr)
    sys.exit(1)

class DownloadScriptIntegrator:
    """下载脚本集成器"""
    
    def __init__(self, verbose: bool = True, api_delay: int = 12):
        """
        初始化下载脚本集成器
        
        Args:
            verbose: 是否显示详细日志
            api_delay: API调用间隔（秒）
        """
        self.verbose = verbose
        self.api_delay = api_delay
        self.logger = self._setup_logger()
        
        # 脚本路径
        self.data_dir = os.path.join(project_root, "data")
        self.scripts = {
            'ohlcv': os.path.join(self.data_dir, "get_OHLCV_data.py"),
            'news': os.path.join(self.data_dir, "get_news_data.py"),
            'fundamental': os.path.join(self.data_dir, "get_fundamental_data.py"),
            'prepare': os.path.join(self.data_dir, "prepare_data.py")
        }
        
        # 验证脚本存在
        self._validate_scripts()
        
        self.logger.info("下载脚本集成器初始化完成")
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger("DownloadScriptIntegrator")
        logger.setLevel(logging.INFO if self.verbose else logging.WARNING)
        
        if not logger.handlers:
            handler = logging.StreamHandler(sys.stderr)
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _validate_scripts(self) -> None:
        """验证所需脚本是否存在"""
        missing_scripts = []
        for name, path in self.scripts.items():
            if not os.path.exists(path):
                missing_scripts.append(f"{name}: {path}")
        
        if missing_scripts:
            error_msg = "缺少必要的下载脚本:\n" + "\n".join(missing_scripts)
            self.logger.error(error_msg)
            raise FileNotFoundError(error_msg)
    
    def _run_script(self, script_name: str, args: List[str], timeout: int = 600) -> Tuple[bool, str, str]:
        """
        运行指定脚本
        
        Args:
            script_name: 脚本名称 ('ohlcv', 'news', 'fundamental', 'prepare')
            args: 脚本参数列表
            timeout: 超时时间（秒）
            
        Returns:
            (是否成功, stdout, stderr)
        """
        if script_name not in self.scripts:
            return False, "", f"未知脚本: {script_name}"
        
        script_path = self.scripts[script_name]
        cmd = [sys.executable, script_path] + args
        
        self.logger.info(f"执行脚本: {script_name} {' '.join(args)}")
        
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=timeout,
                cwd=project_root
            )
            
            success = result.returncode == 0
            
            if success:
                self.logger.info(f"✅ {script_name} 执行成功")
            else:
                self.logger.error(f"❌ {script_name} 执行失败 (返回码: {result.returncode})")
                if result.stderr:
                    self.logger.error(f"错误信息: {result.stderr}")
            
            return success, result.stdout, result.stderr
            
        except subprocess.TimeoutExpired:
            self.logger.error(f"❌ {script_name} 执行超时 ({timeout}秒)")
            return False, "", f"脚本执行超时: {timeout}秒"
        except Exception as e:
            self.logger.error(f"❌ {script_name} 执行异常: {e}")
            return False, "", str(e)
    
    def prepare_database(self, ticker: str) -> bool:
        """
        准备数据库结构
        
        Args:
            ticker: 股票代码
            
        Returns:
            是否成功
        """
        self.logger.info(f"为 {ticker} 准备数据库结构...")
        success, stdout, stderr = self._run_script('prepare', [ticker])
        
        if not success:
            self.logger.error(f"数据库准备失败: {stderr}")
        
        return success
    
    def download_ohlcv_data(self, ticker: str, start_date: str, end_date: str) -> bool:
        """
        下载OHLCV数据
        
        Args:
            ticker: 股票代码
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            
        Returns:
            是否成功
        """
        if not ALPHAVANTAGE_API_KEY or ALPHAVANTAGE_API_KEY == "YOUR_ALPHA_VANTAGE_API_KEY":
            self.logger.error("Alpha Vantage API密钥未配置")
            return False
        
        self.logger.info(f"下载 {ticker} OHLCV数据: {start_date} 到 {end_date}")
        success, stdout, stderr = self._run_script('ohlcv', [ticker, start_date, end_date])
        
        if success:
            self.logger.info(f"✅ {ticker} OHLCV数据下载成功")
        else:
            self.logger.error(f"❌ {ticker} OHLCV数据下载失败")
        
        return success
    
    def download_news_data(self, ticker: str, start_date: str, end_date: str) -> bool:
        """
        下载新闻数据
        
        Args:
            ticker: 股票代码
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            
        Returns:
            是否成功
        """
        if not ALPHAVANTAGE_API_KEY or ALPHAVANTAGE_API_KEY == "YOUR_ALPHA_VANTAGE_API_KEY":
            self.logger.error("Alpha Vantage API密钥未配置")
            return False
        
        self.logger.info(f"下载 {ticker} 新闻数据: {start_date} 到 {end_date}")
        success, stdout, stderr = self._run_script('news', [ticker, start_date, end_date], timeout=1200)  # 20分钟超时
        
        if success:
            self.logger.info(f"✅ {ticker} 新闻数据下载成功")
        else:
            self.logger.error(f"❌ {ticker} 新闻数据下载失败")
            # 新闻数据下载失败通常是由于API限制，不应该阻止整个流程
            if "API_LIMIT_REACHED" in stderr or "rate limit" in stderr.lower():
                self.logger.warning(f"⚠️ {ticker} 新闻数据下载受API限制影响，但不影响其他数据")
        
        return success
    
    def download_fundamental_data(self, ticker: str) -> bool:
        """
        下载基本面数据
        
        Args:
            ticker: 股票代码
            
        Returns:
            是否成功
        """
        if not ALPHAVANTAGE_API_KEY or ALPHAVANTAGE_API_KEY == "YOUR_ALPHA_VANTAGE_API_KEY":
            self.logger.error("Alpha Vantage API密钥未配置")
            return False
        
        self.logger.info(f"下载 {ticker} 基本面数据")
        success, stdout, stderr = self._run_script('fundamental', [ticker], timeout=900)  # 15分钟超时
        
        if success:
            self.logger.info(f"✅ {ticker} 基本面数据下载成功")
        else:
            self.logger.error(f"❌ {ticker} 基本面数据下载失败")
        
        return success
    
    def download_comprehensive_data(self, 
                                  ticker: str, 
                                  start_date: str, 
                                  end_date: str,
                                  include_ohlcv: bool = True,
                                  include_news: bool = True,
                                  include_fundamental: bool = True) -> Dict[str, bool]:
        """
        下载综合数据
        
        Args:
            ticker: 股票代码
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            include_ohlcv: 是否包含OHLCV数据
            include_news: 是否包含新闻数据
            include_fundamental: 是否包含基本面数据
            
        Returns:
            各类数据的下载结果
        """
        self.logger.info(f"开始为 {ticker} 下载综合数据")
        
        results = {
            'prepare': False,
            'ohlcv': False,
            'news': False,
            'fundamental': False
        }
        
        # 1. 准备数据库
        results['prepare'] = self.prepare_database(ticker)
        if not results['prepare']:
            self.logger.error(f"数据库准备失败，跳过 {ticker}")
            return results
        
        # 2. 下载OHLCV数据
        if include_ohlcv:
            results['ohlcv'] = self.download_ohlcv_data(ticker, start_date, end_date)
            if results['ohlcv']:
                time.sleep(self.api_delay)  # API调用间隔
        
        # 3. 下载基本面数据
        if include_fundamental:
            results['fundamental'] = self.download_fundamental_data(ticker)
            if results['fundamental']:
                time.sleep(self.api_delay)  # API调用间隔
        
        # 4. 下载新闻数据（最后下载，因为最容易受限制）
        if include_news:
            results['news'] = self.download_news_data(ticker, start_date, end_date)
        
        # 统计结果
        success_count = sum(1 for k, v in results.items() if k != 'prepare' and v)
        total_count = sum(1 for k in results.keys() if k != 'prepare')
        
        self.logger.info(f"{ticker} 数据下载完成: {success_count}/{total_count} 成功")
        
        return results
    
    def batch_download(self, 
                      tickers: List[str], 
                      start_date: str, 
                      end_date: str,
                      **kwargs) -> Dict[str, Dict[str, bool]]:
        """
        批量下载数据
        
        Args:
            tickers: 股票代码列表
            start_date: 开始日期
            end_date: 结束日期
            **kwargs: 传递给download_comprehensive_data的其他参数
            
        Returns:
            所有股票的下载结果
        """
        self.logger.info(f"开始批量下载 {len(tickers)} 只股票的数据")
        
        all_results = {}
        
        for i, ticker in enumerate(tickers, 1):
            self.logger.info(f"处理第 {i}/{len(tickers)} 只股票: {ticker}")
            
            results = self.download_comprehensive_data(ticker, start_date, end_date, **kwargs)
            all_results[ticker] = results
            
            # 在股票之间添加更长的延迟
            if i < len(tickers):
                self.logger.info(f"等待 {self.api_delay * 2} 秒后处理下一只股票...")
                time.sleep(self.api_delay * 2)
        
        # 统计总体结果
        total_stocks = len(tickers)
        successful_stocks = sum(1 for results in all_results.values() 
                               if any(v for k, v in results.items() if k != 'prepare'))
        
        self.logger.info(f"批量下载完成: {successful_stocks}/{total_stocks} 只股票成功")
        
        return all_results


if __name__ == "__main__":
    # 测试代码
    import argparse
    import json
    
    parser = argparse.ArgumentParser(description="下载脚本集成器")
    parser.add_argument("--ticker", default="AAPL", help="股票代码")
    parser.add_argument("--tickers", nargs="+", help="股票代码列表")
    parser.add_argument("--start-date", default="2025-01-01", help="开始日期")
    parser.add_argument("--end-date", default="2025-03-31", help="结束日期")
    parser.add_argument("--type", choices=['ohlcv', 'news', 'fundamental', 'all'], 
                       default='all', help="下载数据类型")
    parser.add_argument("--batch", action="store_true", help="批量下载模式")
    
    args = parser.parse_args()
    
    integrator = DownloadScriptIntegrator(verbose=True)
    
    if args.batch and args.tickers:
        # 批量下载
        results = integrator.batch_download(args.tickers, args.start_date, args.end_date)
        print("\n📊 批量下载结果:")
        print(json.dumps(results, indent=2, ensure_ascii=False))
    else:
        # 单个股票下载
        ticker = args.ticker
        
        if args.type == 'all':
            results = integrator.download_comprehensive_data(ticker, args.start_date, args.end_date)
            print(f"\n📊 {ticker} 综合数据下载结果:")
            for data_type, success in results.items():
                if data_type != 'prepare':
                    status = "✅ 成功" if success else "❌ 失败"
                    print(f"  {data_type}: {status}")
        elif args.type == 'ohlcv':
            success = integrator.download_ohlcv_data(ticker, args.start_date, args.end_date)
            print(f"OHLCV数据下载: {'✅ 成功' if success else '❌ 失败'}")
        elif args.type == 'news':
            success = integrator.download_news_data(ticker, args.start_date, args.end_date)
            print(f"新闻数据下载: {'✅ 成功' if success else '❌ 失败'}")
        elif args.type == 'fundamental':
            success = integrator.download_fundamental_data(ticker)
            print(f"基本面数据下载: {'✅ 成功' if success else '❌ 失败'}")
