2025-07-04 19:41:05,561 - __main__ - INFO - ================================================================================
2025-07-04 19:41:05,561 - __main__ - INFO - 增强提示词优化系统测试
2025-07-04 19:41:05,562 - __main__ - INFO - ================================================================================
2025-07-04 19:41:05,562 - __main__ - INFO - 测试配置: {'data_dir': 'data/test_prompt_optimization', 'optimization_threshold': 0.8, 'ab_test_duration_days': 7, 'min_improvement_ratio': 0.05}
2025-07-04 19:41:05,573 - __main__ - INFO - 数据存储设置完成: data/test_prompt_optimization\prompt_optimization.db
2025-07-04 19:41:05,573 - __main__ - INFO - 增强的提示词优化系统初始化完成
2025-07-04 19:41:05,573 - __main__ - INFO - ============================================================
2025-07-04 19:41:05,573 - __main__ - INFO - 测试周期性优化工作流
2025-07-04 19:41:05,573 - __main__ - INFO - ============================================================
2025-07-04 19:41:05,574 - __main__ - INFO - 
--- 第 1 周 ---
2025-07-04 19:41:05,574 - __main__ - INFO - 智能体贡献度: {'NAA': 0.9744586360488553, 'TAA': 0.9528928703124153, 'FAA': 0.9358905062331141, 'TRA': 0.5690947949791627, 'BOA': 0.5321598024594882}
2025-07-04 19:41:05,574 - __main__ - INFO - 智能体 TRA 贡献度 0.5691 低于阈值 0.6343
2025-07-04 19:41:05,574 - __main__ - INFO - 智能体 BOA 贡献度 0.5322 低于阈值 0.6343
2025-07-04 19:41:05,576 - __main__ - INFO - 第 1 周检测到 2 个智能体需要优化
2025-07-04 19:41:05,582 - __main__ - INFO - A/B测试已启动: ab_test_TRA_1_20250704_194105 (智能体: TRA)
2025-07-04 19:41:05,587 - __main__ - INFO - A/B测试已启动: ab_test_BOA_1_20250704_194105 (智能体: BOA)
2025-07-04 19:41:05,587 - __main__ - INFO - 第 1 周处理成功
2025-07-04 19:41:05,587 - __main__ - INFO - 需要优化的智能体: ['TRA', 'BOA']
2025-07-04 19:41:05,588 - __main__ - INFO - 
--- 第 2 周 ---
2025-07-04 19:41:05,588 - __main__ - INFO - 智能体贡献度: {'NAA': 0.8312299333678216, 'TAA': 0.7412022520386814, 'FAA': 0.8489529712504698, 'TRA': 0.5285187429615242, 'BOA': 0.38568669879324274}
2025-07-04 19:41:05,588 - __main__ - INFO - 智能体 TRA 贡献度 0.5285 低于阈值 0.5337
2025-07-04 19:41:05,588 - __main__ - INFO - 智能体 BOA 贡献度 0.3857 低于阈值 0.5337
2025-07-04 19:41:05,591 - __main__ - INFO - 第 2 周检测到 2 个智能体需要优化
2025-07-04 19:41:05,596 - __main__ - INFO - A/B测试已启动: ab_test_TRA_2_20250704_194105 (智能体: TRA)
2025-07-04 19:41:05,600 - __main__ - INFO - A/B测试已启动: ab_test_BOA_2_20250704_194105 (智能体: BOA)
2025-07-04 19:41:05,601 - __main__ - INFO - 第 2 周处理成功
2025-07-04 19:41:05,601 - __main__ - INFO - 需要优化的智能体: ['TRA', 'BOA']
2025-07-04 19:41:05,601 - __main__ - INFO - 
--- 第 3 周 ---
2025-07-04 19:41:05,601 - __main__ - INFO - 智能体贡献度: {'NAA': 0.7983097944007658, 'TAA': 0.6572290430946623, 'FAA': 0.8494501378364123, 'TRA': 0.6811063869688526, 'BOA': 0.45384099106921894}
2025-07-04 19:41:05,602 - __main__ - INFO - 智能体 BOA 贡献度 0.4538 低于阈值 0.5504
2025-07-04 19:41:05,605 - __main__ - INFO - 第 3 周检测到 1 个智能体需要优化
2025-07-04 19:41:05,610 - __main__ - INFO - A/B测试已启动: ab_test_BOA_3_20250704_194105 (智能体: BOA)
2025-07-04 19:41:05,610 - __main__ - INFO - 第 3 周处理成功
2025-07-04 19:41:05,611 - __main__ - INFO - 需要优化的智能体: ['BOA']
2025-07-04 19:41:05,611 - __main__ - INFO - 
--- 第 4 周 ---
2025-07-04 19:41:05,611 - __main__ - INFO - 智能体贡献度: {'NAA': 0.906372691545025, 'TAA': 0.7530429380196556, 'FAA': 0.8043769929936072, 'TRA': 0.8205661407615971, 'BOA': 1.0}
2025-07-04 19:41:05,614 - __main__ - INFO - 第 4 周处理成功
2025-07-04 19:41:05,614 - __main__ - INFO - 需要优化的智能体: []
2025-07-04 19:41:05,614 - __main__ - INFO - 
--- 完成A/B测试 ---
2025-07-04 19:41:05,614 - __main__ - INFO - 需要完成 5 个A/B测试
2025-07-04 19:41:05,616 - __main__ - INFO - A/B测试完成: test_TRA_20250704_194105, 获胜变体: control
2025-07-04 19:41:05,616 - __main__ - INFO - A/B测试完成: TRA, 获胜变体: control, 改进率: -0.71%
2025-07-04 19:41:05,618 - __main__ - INFO - A/B测试完成: test_BOA_20250704_194105, 获胜变体: variant_1
2025-07-04 19:41:05,618 - __main__ - INFO - A/B测试完成: BOA, 获胜变体: variant_1, 改进率: 15.29%
2025-07-04 19:41:05,620 - __main__ - INFO - A/B测试完成: test_TRA_20250704_194105, 获胜变体: variant_1
2025-07-04 19:41:05,620 - __main__ - INFO - A/B测试完成: TRA, 获胜变体: variant_1, 改进率: 9.11%
2025-07-04 19:41:05,621 - __main__ - INFO - A/B测试完成: test_BOA_20250704_194105, 获胜变体: variant_1
2025-07-04 19:41:05,621 - __main__ - INFO - A/B测试完成: BOA, 获胜变体: variant_1, 改进率: 0.79%
2025-07-04 19:41:05,622 - __main__ - INFO - A/B测试完成: test_BOA_20250704_194105, 获胜变体: control
2025-07-04 19:41:05,622 - __main__ - INFO - A/B测试完成: BOA, 获胜变体: control, 改进率: -3.80%
2025-07-04 19:41:05,623 - __main__ - INFO - ============================================================
2025-07-04 19:41:05,623 - __main__ - INFO - 测试报告和分析功能
2025-07-04 19:41:05,623 - __main__ - INFO - ============================================================
2025-07-04 19:41:05,642 - __main__ - INFO - 优化报告已生成: data/test_prompt_optimization\optimization_report_20250704_194105.json
2025-07-04 19:41:05,642 - __main__ - INFO - 优化报告生成成功
2025-07-04 19:41:05,642 - __main__ - INFO - 报告摘要:
2025-07-04 19:41:05,642 - __main__ - INFO -   总优化次数: 5
2025-07-04 19:41:05,643 - __main__ - INFO -   成功优化次数: 0
2025-07-04 19:41:05,643 - __main__ - INFO -   平均改进率: 0.00%
2025-07-04 19:41:05,643 - __main__ - INFO -   成功率: 0.0%
2025-07-04 19:41:05,643 - __main__ - INFO - 智能体性能分析:
2025-07-04 19:41:05,643 - __main__ - INFO -   TRA: 优化次数 2, 成功率 0.0%
2025-07-04 19:41:05,643 - __main__ - INFO -   BOA: 优化次数 3, 成功率 0.0%
2025-07-04 19:41:05,643 - __main__ - INFO - 优化建议:
2025-07-04 19:41:05,643 - __main__ - INFO -   1. 优化成功率较低，建议调整优化策略或阈值设置
2025-07-04 19:41:05,643 - __main__ - INFO - 详细报告已保存至: data/test_prompt_optimization\optimization_report_20250704_194105.json
2025-07-04 19:41:05,644 - __main__ - INFO - 
系统统计:
2025-07-04 19:41:05,644 - __main__ - INFO -   total_optimizations: 5
2025-07-04 19:41:05,644 - __main__ - INFO -   successful_optimizations: 3
2025-07-04 19:41:05,644 - __main__ - INFO -   ab_tests_conducted: 5
2025-07-04 19:41:05,644 - __main__ - INFO -   average_improvement: 0.0
2025-07-04 19:41:05,645 - __main__ - INFO -   last_optimization: 2025-07-04T19:41:05.610919
2025-07-04 19:41:05,645 - __main__ - INFO - ============================================================
2025-07-04 19:41:05,645 - __main__ - INFO - 测试数据导出功能
2025-07-04 19:41:05,645 - __main__ - INFO - ============================================================
2025-07-04 19:41:05,645 - __main__ - INFO - 测试 JSON 格式导出...
2025-07-04 19:41:05,700 - __main__ - INFO - 数据导出完成: 3 个文件
2025-07-04 19:41:05,700 - __main__ - INFO - JSON 导出成功:
2025-07-04 19:41:05,700 - __main__ - INFO -   - data/test_prompt_optimization\exports\prompt_optimizations_20250704_194105.json
2025-07-04 19:41:05,701 - __main__ - INFO -   - data/test_prompt_optimization\exports\weekly_performance_20250704_194105.json
2025-07-04 19:41:05,701 - __main__ - INFO -   - data/test_prompt_optimization\exports\ab_test_results_20250704_194105.json
2025-07-04 19:41:05,701 - __main__ - INFO - 测试 CSV 格式导出...
2025-07-04 19:41:05,724 - __main__ - INFO - 数据导出完成: 3 个文件
2025-07-04 19:41:05,724 - __main__ - INFO - CSV 导出成功:
2025-07-04 19:41:05,724 - __main__ - INFO -   - data/test_prompt_optimization\exports\prompt_optimizations_20250704_194105.csv
2025-07-04 19:41:05,725 - __main__ - INFO -   - data/test_prompt_optimization\exports\weekly_performance_20250704_194105.csv
2025-07-04 19:41:05,725 - __main__ - INFO -   - data/test_prompt_optimization\exports\ab_test_results_20250704_194105.csv
2025-07-04 19:41:05,725 - __main__ - INFO - ================================================================================
2025-07-04 19:41:05,725 - __main__ - INFO - 所有测试完成!
2025-07-04 19:41:05,725 - __main__ - INFO - ================================================================================
2025-07-04 19:41:05,725 - __main__ - INFO - 最终系统统计:
2025-07-04 19:41:05,726 - __main__ - INFO -   total_optimizations: 5
2025-07-04 19:41:05,726 - __main__ - INFO -   successful_optimizations: 3
2025-07-04 19:41:05,726 - __main__ - INFO -   ab_tests_conducted: 5
2025-07-04 19:41:05,726 - __main__ - INFO -   average_improvement: 0.0
2025-07-04 19:41:05,726 - __main__ - INFO -   last_optimization: 2025-07-04T19:41:05.610919
