# /Users/<USER>/Code/multi_agent_optimize/data/get_OHLCV_data.py
import requests # Keep requests for general use if needed, but use curl_cffi for yfinance session
import sys
import json
import os
import subprocess
from datetime import datetime
import sqlite3
import yfinance as yf # Import yfinance
import pandas as pd # Import pandas

# Add project root to sys.path
current_script_path = os.path.abspath(__file__)
project_root = os.path.dirname(os.path.dirname(current_script_path))
sys.path.append(project_root)

from config import ALPHAVANTAGE_API_KEY, DATA_DIR

def get_database_path(ticker):
    """
    根据ticker获取对应的数据库路径
    """
    ticker_dir = os.path.join(DATA_DIR, "tickers", ticker.upper())
    os.makedirs(ticker_dir, exist_ok=True)
    return os.path.join(ticker_dir, f"{ticker.upper()}_data.db")

# Alpha Vantage API Base URL (Still needed if you plan to use Alpha Vantage as fallback or for other data)
BASE_URL = "https://www.alphavantage.co/query"

def create_connection(ticker):
    """Creates a SQLite database connection for specific ticker"""
    conn = None
    try:
        database_path = get_database_path(ticker)
        conn = sqlite3.connect(database_path)
        # print(f"Successfully connected to SQLite database: {database_path}", file=sys.stderr) # Suppress frequent connection messages
        return conn
    except sqlite3.Error as e:
        print(f"Database connection error: {e}", file=sys.stderr)
        return None

def insert_ohlcv_data(conn, ticker, data):
    """
    Inserts OHLCV data into the database, skipping duplicates.
    Assumes the ohlcv table already exists.
    Data is expected to be a list of dictionaries from Alpha Vantage parsing.
    """
    insert_sql = """
    INSERT OR IGNORE INTO ohlcv (ticker, trade_date, Open, High, Low, Close, Adj_Close, Volume)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?);
    """

    cursor = conn.cursor()
    values_to_insert = []
    for item in data:
        values_to_insert.append((
            ticker,
            item.get('date'),
            item.get('open'),
            item.get('high'),
            item.get('low'),
            item.get('close'),
            item.get('close'), # Alpha Vantage TIME_SERIES_DAILY does not have Adj Close, use Close
            item.get('volume')
        ))

    if values_to_insert:
        try:
            cursor.executemany(insert_sql, values_to_insert)
            conn.commit()
            print(f"Attempted to insert {len(values_to_insert)} OHLCV records for {ticker}.", file=sys.stderr)
        except sqlite3.Error as e:
            print(f"Error inserting {ticker} OHLCV data into database: {e}", file=sys.stderr)
            raise # Re-raise the exception

def download_ohlcv_data_alpha_vantage(ticker, start_date_str, end_date_str):
    """
    Downloads OHLCV data using Alpha Vantage TIME_SERIES_DAILY API and inserts into DB.
    Uses outputsize=full to get all available history, then filters by date range.
    """
    conn = create_connection(ticker)
    if conn is None:
        print("Failed to connect to database, cannot download OHLCV data.", file=sys.stderr)
        return

    print(f"正在使用 Alpha Vantage 获取 {ticker} 的完整历史每日数据并插入数据库...")

    # API Parameters
    params = {
        "function": "TIME_SERIES_DAILY",
        "symbol": ticker,
        "outputsize": "full",  # Get full history (up to 20+ years)
        "apikey": ALPHAVANTAGE_API_KEY,
        "datatype": "json"
    }

    try:
        response = requests.get(BASE_URL, params=params)
        response.raise_for_status() # Raise HTTPError for bad responses (4xx or 5xx)

        data = response.json()

        # Check API response
        if "Error Message" in data:
            print(f"Alpha Vantage API Error: {data['Error Message']}", file=sys.stderr)
            conn.close()
            return
        if "Note" in data:
             print(f"Alpha Vantage API Note: {data['Note']}", file=sys.stderr)
             # This often indicates rate limits
             conn.close()
             return

        time_series_key = "Time Series (Daily)"
        if time_series_key not in data:
            print(f"Alpha Vantage response did not contain '{time_series_key}' data.", file=sys.stderr)
            conn.close()
            return

        raw_data = data[time_series_key]

        print(f"Fetched {len(raw_data)} days of raw historical data. Filtering by date range and preparing for insertion...", file=sys.stderr)

        # Filter by date range and format data for insertion
        # start_date_dt = datetime.strptime(start_date_str, '%Y-%m-%d') # No longer used for filtering
        # end_date_dt = datetime.strptime(end_date_str, '%Y-%m-%d') # No longer used for filtering

        data_for_insertion = []
        for date_str, ohlcv_data in raw_data.items():
            try:
                # current_date_dt = datetime.strptime(date_str, '%Y-%m-%d') # No longer used for filtering
                # if start_date_dt <= current_date_dt <= end_date_dt: # Condition removed to process all data
                # Format data, remove prefixes, convert types
                formatted_item = {
                    "date": date_str,
                    "open": float(ohlcv_data.get("1. open")),
                    "high": float(ohlcv_data.get("2. high")),
                    "low": float(ohlcv_data.get("3. low")),
                    "close": float(ohlcv_data.get("4. close")),
                    "volume": int(ohlcv_data.get("5. volume"))
                }
                data_for_insertion.append(formatted_item)
            except Exception as e:
                 print(f"Error processing date {date_str} from API response: {e}", file=sys.stderr)
                 continue # Skip this data point

        # Data is typically returned newest first, sort by date ascending before inserting
        data_for_insertion.sort(key=lambda x: x['date'])

        if data_for_insertion:
            print(f"Inserting {len(data_for_insertion)} records for {ticker} into database for date range {start_date_str} to {end_date_str}...", file=sys.stderr)
            insert_ohlcv_data(conn, ticker, data_for_insertion)
            print("Insertion complete.", file=sys.stderr)
        else:
            print(f"No data found within the specified date range ({start_date_str} to {end_date_str}) for {ticker} after fetching full history.", file=sys.stderr)

    except requests.exceptions.RequestException as e:
        print(f"API request failed: {e}", file=sys.stderr)
    except json.JSONDecodeError:
        print("Could not parse API response as JSON.", file=sys.stderr)
    except Exception as e:
        print(f"An unexpected error occurred during Alpha Vantage download: {e}", file=sys.stderr)
    finally:
        if conn:
            conn.close()

def main():
    """Main function"""
    # Check command line arguments
    # Requires 4 arguments: script name, ticker, start_date, end_date
    if len(sys.argv) < 4:
        print("Usage: python data/get_OHLCV_data.py <ticker> <start_date> <end_date>", file=sys.stderr)
        print("Example: python data/get_OHLCV_data.py AAPL 2023-01-01 2023-12-31", file=sys.stderr)
        sys.exit(1)

    ticker = sys.argv[1].upper()
    start_date = sys.argv[2]
    end_date = sys.argv[3]

    # 确保数据目录存在
    os.makedirs(DATA_DIR, exist_ok=True)
    os.makedirs(os.path.join(DATA_DIR, "tickers"), exist_ok=True)

    # 确保数据库结构存在
    print("Ensuring database structure exists by running data/prepare_data.py...", file=sys.stderr)
    try:
        # Assuming prepare_data.py is executable and initializes DB
        import subprocess
        result = subprocess.run([sys.executable, os.path.join(os.path.dirname(__file__), 'prepare_data.py'), ticker], check=True, capture_output=True, text=True)
        # print("prepare_data.py output:", result.stdout, file=sys.stderr)
        if result.stderr:
             print("prepare_data.py stderr:", result.stderr, file=sys.stderr)

    except FileNotFoundError:
        print("Error: prepare_data.py not found. Make sure it exists in the data/ directory.", file=sys.stderr)
        sys.exit(1)
    except subprocess.CalledProcessError as e:
        print(f"Error running prepare_data.py: {e}", file=sys.stderr)
        print("prepare_data.py stderr:", e.stderr, file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        print(f"An unexpected error occurred while running prepare_data.py: {e}", file=sys.stderr)
        sys.exit(1)

    # Now proceed with data download and insertion
    if not ALPHAVANTAGE_API_KEY or ALPHAVANTAGE_API_KEY == "YOUR_ALPHA_VANTAGE_API_KEY":
        print("\nError: Please configure your Alpha Vantage API key in config.py, skipping OHLCV download.", file=sys.stderr)
    else:
        download_ohlcv_data_alpha_vantage(ticker, start_date, end_date)

    print(f"\nOHLCV data download and insertion process finished for {ticker} from {start_date} to {end_date}.")
    db_path = get_database_path(ticker)
    print(f"数据存储在: {db_path}")

if __name__ == "__main__":
    main() 