# OPRO系统修复后运行指南

## 快速开始

### 1. 验证修复效果

#### 测试联盟形成时机修复
```bash
# 运行原始系统查看日志
python run_opro_system.py

# 检查日志中联盟生成的时机是否为每周执行
# 应该看到类似 "第 X 周交易" 的日志信息
```

#### 测试图表可视化修复
```bash
# 运行可视化测试脚本
python test_visualization_fixes.py

# 检查生成的图表文件
# results/test_improved_chart_*.png - 改进的股价图表
# results/test_chinese_chars_*.png - 中文字符测试图表
```

#### 测试增强提示词优化系统
```bash
# 运行提示词优化测试脚本
python test_enhanced_prompt_optimization.py

# 检查生成的数据和报告
# data/test_prompt_optimization/ - 优化数据存储
# *.log - 详细的测试日志
```

### 2. 系统配置

#### 启用新功能
在你的配置文件中添加以下设置：

```python
# config.py 或相应的配置文件
config = {
    # 启用周期性联盟生成
    "enable_weekly_coalition": True,
    
    # 启用增强的提示词优化
    "enable_enhanced_optimization": True,
    
    # 优化阈值设置
    "optimization_threshold": 0.8,  # 低于平均值80%触发优化
    
    # A/B测试配置
    "ab_test_duration_days": 7,
    "min_improvement_ratio": 0.05,  # 最小改进率5%
    
    # 可视化改进
    "visualization_improvements": True,
    "chart_width": 1400,
    "chart_height": 900,
    
    # 数据存储
    "data_dir": "data/prompt_optimization",
}
```

### 3. 运行完整系统

#### 标准运行模式
```bash
# 运行完整的OPRO系统
python run_opro_system.py

# 系统现在将：
# 1. 按周进行联盟生成和评估
# 2. 自动识别需要优化的智能体
# 3. 触发提示词优化和A/B测试
# 4. 生成高质量的中文图表
# 5. 保存详细的优化记录
```

#### 监控系统运行
```bash
# 查看实时日志
tail -f opro_system_*.log

# 检查关键信息：
# - "第 X 周交易" - 确认周期性执行
# - "检测到 X 个智能体需要优化" - 优化触发
# - "A/B测试已启动" - A/B测试状态
# - "图表已保存至" - 可视化输出
```

## 功能详解

### 1. 周期性联盟形成

**修复前**: 联盟在第一天就生成，后续只是使用预生成的联盟
**修复后**: 每周重新生成联盟，基于最新的智能体表现

```python
# 新的执行流程
for week in range(total_weeks):
    # 1. 生成本周的联盟
    coalition_result = self._run_coalition_generation_phase(target_agents)
    
    # 2. 运行本周的交易模拟
    simulation_result = self._run_trading_simulation_phase(...)
    
    # 3. 计算本周的Shapley值
    shapley_result = self._run_shapley_calculation_phase(...)
    
    # 4. 分析智能体贡献度
    contribution_analysis = self._analyze_weekly_contributions(...)
    
    # 5. 触发优化（如需要）
    if contribution_analysis.get("needs_optimization"):
        optimization_result = self._trigger_weekly_optimization(...)
```

### 2. 改进的图表可视化

**修复前**: 中文字符显示为方块，文本标签重叠
**修复后**: 智能字体检测，优化布局参数

```python
# 智能字体检测
def setup_chinese_fonts():
    system = platform.system()
    if system == "Windows":
        chinese_fonts = ['SimHei', 'Microsoft YaHei', 'SimSun', 'KaiTi']
    elif system == "Darwin":  # macOS
        chinese_fonts = ['Arial Unicode MS', 'PingFang SC', 'Heiti SC']
    else:  # Linux
        chinese_fonts = ['WenQuanYi Micro Hei', 'DejaVu Sans']
    
    plt.rcParams['font.sans-serif'] = chinese_fonts
    # ... 其他配置
```

### 3. 增强的提示词优化系统

**新功能**: 完整的A/B测试和优化跟踪系统

```python
# 使用示例
optimizer = EnhancedPromptOptimizer(config=config)

# 处理周级性能数据
result = optimizer.process_weekly_performance(
    week_number=1,
    start_date="2025-01-01",
    end_date="2025-01-07",
    agent_contributions={"NAA": 0.85, "TAA": 0.70, ...},
    coalition_performances={...},
    shapley_values={...}
)

# 生成优化报告
report = optimizer.generate_optimization_report(start_week=1, end_week=4)

# 导出数据
export_result = optimizer.export_data(export_format="json")
```

## 数据文件说明

### 生成的文件结构
```
Multi_Agent_Optimization/
├── results/                          # 图表输出
│   ├── test_improved_chart_*.png     # 改进的股价图表
│   └── test_chinese_chars_*.png      # 中文字符测试图表
├── data/
│   ├── prompt_optimization/          # 提示词优化数据
│   │   ├── prompt_optimization.db    # SQLite数据库
│   │   ├── optimization_report_*.json # 优化报告
│   │   └── exports/                  # 导出数据
│   └── visualizations/               # 系统生成的图表
├── logs/
│   ├── opro_system_*.log            # 系统运行日志
│   └── test_*.log                   # 测试日志
└── *.py                             # 修复后的源代码
```

### 重要数据文件

1. **prompt_optimization.db**: SQLite数据库，存储所有优化记录
2. **optimization_report_*.json**: 详细的优化效果报告
3. **opro_system_*.log**: 系统运行日志，包含周期性执行信息
4. **results/*.png**: 生成的图表文件，支持中文显示

## 故障排除

### 常见问题

#### 1. 中文字符仍显示为方块
```bash
# 检查系统字体
python -c "import matplotlib.font_manager as fm; print([f.name for f in fm.fontManager.ttflist if 'SimHei' in f.name or 'Microsoft' in f.name])"

# 如果没有中文字体，安装字体包
# Windows: 系统自带
# macOS: 系统自带
# Linux: sudo apt-get install fonts-wqy-microhei
```

#### 2. 联盟仍然每日生成
```bash
# 检查配置
grep -n "enable_weekly_coalition" config.py

# 检查日志中的执行模式
grep "周期性联盟生成" opro_system_*.log
```

#### 3. A/B测试未启动
```bash
# 检查优化阈值设置
# 如果所有智能体性能都很好，不会触发优化
# 可以临时降低阈值进行测试
```

### 性能优化

#### 1. 大数据集处理
```python
# 如果数据集很大，可以调整批处理大小
config["batch_size"] = 1000
config["max_coalitions_per_week"] = 50
```

#### 2. 内存优化
```python
# 启用内存优化模式
config["memory_optimization"] = True
config["cache_size_limit"] = "1GB"
```

## 联系支持

如果遇到问题，请：

1. **检查日志文件**: 查看详细的错误信息
2. **运行测试脚本**: 验证各个组件是否正常工作
3. **查看配置**: 确认所有配置参数正确设置
4. **提供信息**: 包含错误日志、配置文件和系统环境信息

---

**修复版本**: v2.0  
**最后更新**: 2025-07-04  
**兼容性**: Python 3.7+, Windows/macOS/Linux
