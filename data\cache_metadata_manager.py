#!/usr/bin/env python3
"""
缓存元数据管理器

管理股票数据的缓存元数据，包括：
1. 数据更新时间跟踪
2. 数据完整性评估
3. 缓存有效性检查
4. 数据质量评分
"""

import os
import sys
import json
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
import logging

# 添加项目根目录到路径
current_script_path = os.path.abspath(__file__)
project_root = os.path.dirname(os.path.dirname(current_script_path))
sys.path.append(project_root)

try:
    from config import DATA_DIR
    from data.get_all_data import get_database_path
except ImportError as e:
    print(f"导入配置失败: {e}", file=sys.stderr)
    sys.exit(1)

@dataclass
class DataRange:
    """数据范围信息"""
    start_date: Optional[str]
    end_date: Optional[str]
    record_count: int
    last_updated: Optional[str]

@dataclass
class StockCacheMetadata:
    """股票缓存元数据"""
    ticker: str
    last_cache_update: str
    ohlcv_data: DataRange
    news_data: DataRange
    annual_financials: DataRange
    quarterly_financials: DataRange
    data_quality_score: float
    cache_version: str = "1.0"

class CacheMetadataManager:
    """缓存元数据管理器"""
    
    def __init__(self, verbose: bool = True):
        """
        初始化缓存元数据管理器
        
        Args:
            verbose: 是否显示详细日志
        """
        self.verbose = verbose
        self.logger = self._setup_logger()
        
        # 缓存元数据目录
        self.metadata_dir = os.path.join(DATA_DIR, "cache", "metadata")
        os.makedirs(self.metadata_dir, exist_ok=True)
        
        self.logger.info("缓存元数据管理器初始化完成")
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger("CacheMetadataManager")
        logger.setLevel(logging.INFO if self.verbose else logging.WARNING)
        
        if not logger.handlers:
            handler = logging.StreamHandler(sys.stderr)
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def get_metadata_file_path(self, ticker: str) -> str:
        """获取股票元数据文件路径"""
        return os.path.join(self.metadata_dir, f"{ticker.upper()}_metadata.json")
    
    def load_stock_metadata(self, ticker: str) -> Optional[StockCacheMetadata]:
        """加载股票缓存元数据"""
        metadata_file = self.get_metadata_file_path(ticker)
        
        if not os.path.exists(metadata_file):
            return None
        
        try:
            with open(metadata_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 转换为StockCacheMetadata对象
            metadata = StockCacheMetadata(
                ticker=data['ticker'],
                last_cache_update=data['last_cache_update'],
                ohlcv_data=DataRange(**data['ohlcv_data']),
                news_data=DataRange(**data['news_data']),
                annual_financials=DataRange(**data['annual_financials']),
                quarterly_financials=DataRange(**data['quarterly_financials']),
                data_quality_score=data['data_quality_score'],
                cache_version=data.get('cache_version', '1.0')
            )
            
            return metadata
            
        except Exception as e:
            self.logger.error(f"加载 {ticker} 元数据失败: {e}")
            return None
    
    def save_stock_metadata(self, metadata: StockCacheMetadata) -> bool:
        """保存股票缓存元数据"""
        metadata_file = self.get_metadata_file_path(metadata.ticker)
        
        try:
            # 更新最后更新时间
            metadata.last_cache_update = datetime.now().isoformat()
            
            # 转换为字典并保存
            data = asdict(metadata)
            
            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"保存 {metadata.ticker} 元数据成功")
            return True
            
        except Exception as e:
            self.logger.error(f"保存 {metadata.ticker} 元数据失败: {e}")
            return False
    
    def generate_stock_metadata(self, ticker: str) -> Optional[StockCacheMetadata]:
        """从数据库生成股票缓存元数据"""
        db_path = get_database_path(ticker)
        
        if not os.path.exists(db_path):
            self.logger.warning(f"{ticker} 数据库不存在: {db_path}")
            return None
        
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 获取OHLCV数据信息
            ohlcv_data = self._get_table_data_range(cursor, 'ohlcv', 'trade_date', ticker)
            
            # 获取新闻数据信息
            news_data = self._get_table_data_range(cursor, 'news', 'time_published', ticker, date_format='datetime')
            
            # 获取年度财务数据信息
            annual_data = self._get_table_data_range(cursor, 'annual_financials', 'fiscal_date', ticker)
            
            # 获取季度财务数据信息
            quarterly_data = self._get_table_data_range(cursor, 'quarterly_financials', 'fiscal_date', ticker)
            
            conn.close()
            
            # 计算数据质量分数
            quality_score = self._calculate_quality_score(ohlcv_data, news_data, annual_data, quarterly_data)
            
            # 创建元数据对象
            metadata = StockCacheMetadata(
                ticker=ticker.upper(),
                last_cache_update=datetime.now().isoformat(),
                ohlcv_data=ohlcv_data,
                news_data=news_data,
                annual_financials=annual_data,
                quarterly_financials=quarterly_data,
                data_quality_score=quality_score
            )
            
            return metadata
            
        except Exception as e:
            self.logger.error(f"生成 {ticker} 元数据失败: {e}")
            return None
    
    def _get_table_data_range(self, 
                             cursor: sqlite3.Cursor, 
                             table_name: str, 
                             date_column: str, 
                             ticker: str,
                             date_format: str = 'date') -> DataRange:
        """获取表的数据范围信息"""
        try:
            if date_format == 'datetime':
                # 对于datetime类型，提取日期部分
                query = f"""
                    SELECT 
                        MIN(DATE({date_column})) as min_date,
                        MAX(DATE({date_column})) as max_date,
                        COUNT(*) as count
                    FROM {table_name} 
                    WHERE ticker = ?
                """
            else:
                # 对于date类型
                query = f"""
                    SELECT 
                        MIN({date_column}) as min_date,
                        MAX({date_column}) as max_date,
                        COUNT(*) as count
                    FROM {table_name} 
                    WHERE ticker = ?
                """
            
            cursor.execute(query, (ticker,))
            result = cursor.fetchone()
            
            if result and result[2] > 0:  # 有数据
                return DataRange(
                    start_date=result[0],
                    end_date=result[1],
                    record_count=result[2],
                    last_updated=datetime.now().isoformat()
                )
            else:
                return DataRange(
                    start_date=None,
                    end_date=None,
                    record_count=0,
                    last_updated=None
                )
                
        except Exception as e:
            self.logger.error(f"获取 {table_name} 数据范围失败: {e}")
            return DataRange(
                start_date=None,
                end_date=None,
                record_count=0,
                last_updated=None
            )
    
    def _calculate_quality_score(self, 
                                ohlcv: DataRange, 
                                news: DataRange, 
                                annual: DataRange, 
                                quarterly: DataRange) -> float:
        """计算数据质量分数"""
        score = 0.0
        
        # OHLCV数据权重 50%
        if ohlcv.record_count > 0:
            score += 0.5
        
        # 新闻数据权重 30%
        if news.record_count > 0:
            score += 0.3
        
        # 基本面数据权重 20%
        if annual.record_count > 0 or quarterly.record_count > 0:
            score += 0.2
        
        return score
    
    def is_cache_valid(self, ticker: str, max_age_days: int = 7) -> bool:
        """检查缓存是否有效"""
        metadata = self.load_stock_metadata(ticker)
        
        if not metadata:
            return False
        
        try:
            last_update = datetime.fromisoformat(metadata.last_cache_update)
            age = datetime.now() - last_update
            
            return age.days <= max_age_days
            
        except Exception as e:
            self.logger.error(f"检查 {ticker} 缓存有效性失败: {e}")
            return False
    
    def update_stock_metadata(self, ticker: str) -> bool:
        """更新股票元数据"""
        metadata = self.generate_stock_metadata(ticker)
        
        if metadata:
            return self.save_stock_metadata(metadata)
        else:
            return False
    
    def get_cache_summary(self, tickers: List[str]) -> Dict[str, Any]:
        """获取缓存摘要信息"""
        summary = {
            "total_stocks": len(tickers),
            "cached_stocks": 0,
            "valid_cache_stocks": 0,
            "stocks_with_data": 0,
            "average_quality_score": 0.0,
            "stock_details": {}
        }
        
        total_quality = 0.0
        
        for ticker in tickers:
            metadata = self.load_stock_metadata(ticker)
            
            if metadata:
                summary["cached_stocks"] += 1
                total_quality += metadata.data_quality_score
                
                if metadata.data_quality_score > 0:
                    summary["stocks_with_data"] += 1
                
                if self.is_cache_valid(ticker):
                    summary["valid_cache_stocks"] += 1
                
                summary["stock_details"][ticker] = {
                    "has_cache": True,
                    "is_valid": self.is_cache_valid(ticker),
                    "quality_score": metadata.data_quality_score,
                    "last_update": metadata.last_cache_update,
                    "ohlcv_records": metadata.ohlcv_data.record_count,
                    "news_records": metadata.news_data.record_count,
                    "annual_records": metadata.annual_financials.record_count,
                    "quarterly_records": metadata.quarterly_financials.record_count
                }
            else:
                summary["stock_details"][ticker] = {
                    "has_cache": False,
                    "is_valid": False,
                    "quality_score": 0.0,
                    "last_update": None
                }
        
        if summary["cached_stocks"] > 0:
            summary["average_quality_score"] = total_quality / summary["cached_stocks"]
        
        return summary


if __name__ == "__main__":
    # 测试代码
    import argparse
    
    parser = argparse.ArgumentParser(description="缓存元数据管理器")
    parser.add_argument("--stocks", nargs="+", default=["AAPL"], help="股票代码列表")
    parser.add_argument("--update", action="store_true", help="更新元数据")
    parser.add_argument("--summary", action="store_true", help="显示缓存摘要")
    
    args = parser.parse_args()
    
    manager = CacheMetadataManager(verbose=True)
    
    if args.update:
        for ticker in args.stocks:
            print(f"更新 {ticker} 元数据...")
            success = manager.update_stock_metadata(ticker)
            print(f"{ticker}: {'✅ 成功' if success else '❌ 失败'}")
    
    if args.summary:
        summary = manager.get_cache_summary(args.stocks)
        print("\n📊 缓存摘要:")
        print(json.dumps(summary, indent=2, ensure_ascii=False))
