#!/usr/bin/env python3
"""
数据分析工具 (Data Analysis Tools)

提供数据导出和分析功能：
1. 多格式数据导出（CSV、Excel、JSON）
2. 数据分析和报告生成
3. 性能趋势分析
4. 交互式数据探索
5. 自动化报告生成

作者: AI Assistant
创建时间: 2025-07-04
"""

import os
import json
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import logging
from pathlib import Path

from .comprehensive_storage_manager import ComprehensiveStorageManager

# 设置中文字体和改进的可视化配置
def setup_analysis_fonts():
    """设置分析工具的中文字体支持"""
    import platform

    # 根据操作系统选择合适的中文字体
    system = platform.system()

    if system == "Windows":
        chinese_fonts = ['SimHei', 'Microsoft YaHei', 'SimSun', 'KaiTi']
    elif system == "Darwin":  # macOS
        chinese_fonts = ['Arial Unicode MS', 'PingFang SC', 'Heiti SC', 'STHeiti']
    else:  # Linux
        chinese_fonts = ['WenQuanYi Micro Hei', 'DejaVu Sans', 'Liberation Sans']

    chinese_fonts.extend(['DejaVu Sans', 'Arial', 'sans-serif'])

    # 设置matplotlib参数
    plt.rcParams['font.sans-serif'] = chinese_fonts
    plt.rcParams['axes.unicode_minus'] = False

    # 改进的图表样式设置
    plt.rcParams['figure.figsize'] = (12, 8)
    plt.rcParams['figure.dpi'] = 100
    plt.rcParams['savefig.dpi'] = 300
    plt.rcParams['savefig.bbox'] = 'tight'
    plt.rcParams['savefig.pad_inches'] = 0.2

    # 字体大小设置
    plt.rcParams['font.size'] = 10
    plt.rcParams['axes.titlesize'] = 14
    plt.rcParams['axes.labelsize'] = 12
    plt.rcParams['xtick.labelsize'] = 10
    plt.rcParams['ytick.labelsize'] = 10
    plt.rcParams['legend.fontsize'] = 10

    # 布局设置
    plt.rcParams['figure.autolayout'] = True
    plt.rcParams['axes.grid'] = True
    plt.rcParams['axes.grid.alpha'] = 0.3

# 初始化字体设置
setup_analysis_fonts()
sns.set_style("whitegrid")
sns.set_palette("husl")

class DataAnalysisTools:
    """
    数据分析工具
    
    提供全面的数据分析和导出功能
    """
    
    def __init__(self, 
                 storage_manager: ComprehensiveStorageManager,
                 logger: Optional[logging.Logger] = None):
        """
        初始化数据分析工具
        
        参数:
            storage_manager: 存储管理器实例
            logger: 日志记录器
        """
        self.storage_manager = storage_manager
        self.logger = logger or self._create_default_logger()
        
        self.logger.info("数据分析工具初始化完成")
    
    def _create_default_logger(self) -> logging.Logger:
        """创建默认日志记录器"""
        logger = logging.getLogger(f"{__name__}.DataAnalysisTools")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def export_trading_performance_analysis(self, 
                                          start_date: Optional[str] = None,
                                          end_date: Optional[str] = None,
                                          output_format: str = "excel") -> Dict[str, Any]:
        """
        导出交易性能分析报告
        
        参数:
            start_date: 开始日期
            end_date: 结束日期
            output_format: 输出格式 ("excel", "csv", "json")
            
        返回:
            导出结果
        """
        try:
            # 获取交易数据
            trading_sessions = self.storage_manager.get_trading_sessions(start_date, end_date)
            
            if not trading_sessions:
                return {"success": False, "error": "无交易数据"}
            
            # 分析数据
            analysis_results = self._analyze_trading_performance(trading_sessions)
            
            # 导出数据
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            export_dir = os.path.join(self.storage_manager.config.exports_path, f"trading_analysis_{timestamp}")
            os.makedirs(export_dir, exist_ok=True)
            
            exported_files = []
            
            if output_format == "excel":
                file_path = self._export_to_excel(analysis_results, export_dir, "trading_performance")
                if file_path:
                    exported_files.append(file_path)
            
            elif output_format == "csv":
                file_paths = self._export_to_csv(analysis_results, export_dir)
                exported_files.extend(file_paths)
            
            elif output_format == "json":
                file_path = self._export_to_json(analysis_results, export_dir, "trading_performance")
                if file_path:
                    exported_files.append(file_path)
            
            # 生成图表
            chart_paths = self._generate_performance_charts(analysis_results, export_dir)
            exported_files.extend(chart_paths)
            
            return {
                "success": True,
                "export_directory": export_dir,
                "exported_files": exported_files,
                "analysis_summary": analysis_results.get("summary", {}),
                "timestamp": timestamp
            }
            
        except Exception as e:
            self.logger.error(f"导出交易性能分析失败: {e}")
            return {"success": False, "error": str(e)}
    
    def export_prompt_optimization_analysis(self, 
                                          agent_id: Optional[str] = None,
                                          weeks: int = 12,
                                          output_format: str = "excel") -> Dict[str, Any]:
        """
        导出提示词优化分析报告
        
        参数:
            agent_id: 智能体ID（None表示所有智能体）
            weeks: 分析周数
            output_format: 输出格式
            
        返回:
            导出结果
        """
        try:
            # 获取提示词优化数据
            end_date = datetime.now()
            start_date = end_date - timedelta(weeks=weeks)
            
            optimizations = self.storage_manager.get_prompt_optimizations(
                agent_id=agent_id,
                start_date=start_date.strftime('%Y-%m-%d'),
                end_date=end_date.strftime('%Y-%m-%d')
            )
            
            if not optimizations:
                return {"success": False, "error": "无提示词优化数据"}
            
            # 分析数据
            analysis_results = self._analyze_prompt_optimizations(optimizations)
            
            # 导出数据
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            export_dir = os.path.join(self.storage_manager.config.exports_path, f"prompt_analysis_{timestamp}")
            os.makedirs(export_dir, exist_ok=True)
            
            exported_files = []
            
            if output_format == "excel":
                file_path = self._export_to_excel(analysis_results, export_dir, "prompt_optimization")
                if file_path:
                    exported_files.append(file_path)
            
            elif output_format == "csv":
                file_paths = self._export_to_csv(analysis_results, export_dir)
                exported_files.extend(file_paths)
            
            elif output_format == "json":
                file_path = self._export_to_json(analysis_results, export_dir, "prompt_optimization")
                if file_path:
                    exported_files.append(file_path)
            
            # 生成图表
            chart_paths = self._generate_optimization_charts(analysis_results, export_dir)
            exported_files.extend(chart_paths)
            
            return {
                "success": True,
                "export_directory": export_dir,
                "exported_files": exported_files,
                "analysis_summary": analysis_results.get("summary", {}),
                "timestamp": timestamp
            }
            
        except Exception as e:
            self.logger.error(f"导出提示词优化分析失败: {e}")
            return {"success": False, "error": str(e)}
    
    def _analyze_trading_performance(self, trading_sessions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析交易性能"""
        try:
            # 转换为DataFrame
            sessions_data = []
            for session in trading_sessions:
                session_info = {
                    "session_id": session.get("session_id"),
                    "timestamp": session.get("timestamp"),
                    "created_at": session.get("created_at")
                }
                
                # 添加盈亏数据
                profit_loss = session.get("profit_loss", {})
                for agent_id, pnl in profit_loss.items():
                    session_info[f"pnl_{agent_id}"] = pnl
                
                # 添加智能体数量
                agent_outputs = session.get("agent_outputs", {})
                session_info["agent_count"] = len(agent_outputs)
                
                sessions_data.append(session_info)
            
            df = pd.DataFrame(sessions_data)
            
            # 基本统计
            analysis = {
                "basic_stats": {
                    "total_sessions": len(df),
                    "date_range": {
                        "start": df["timestamp"].min() if not df.empty else None,
                        "end": df["timestamp"].max() if not df.empty else None
                    },
                    "avg_agents_per_session": df["agent_count"].mean() if not df.empty else 0
                },
                "performance_by_agent": {},
                "time_series_data": [],
                "summary": {}
            }
            
            # 按智能体分析性能
            pnl_columns = [col for col in df.columns if col.startswith("pnl_")]
            for col in pnl_columns:
                agent_id = col.replace("pnl_", "")
                if agent_id != "system_total":
                    agent_pnl = df[col].dropna()
                    if not agent_pnl.empty:
                        analysis["performance_by_agent"][agent_id] = {
                            "total_pnl": float(agent_pnl.sum()),
                            "avg_pnl": float(agent_pnl.mean()),
                            "std_pnl": float(agent_pnl.std()),
                            "win_rate": float((agent_pnl > 0).mean()),
                            "sessions_count": len(agent_pnl)
                        }
            
            # 时间序列数据
            if not df.empty:
                df["date"] = pd.to_datetime(df["timestamp"]).dt.date
                daily_stats = df.groupby("date").agg({
                    "session_id": "count",
                    "agent_count": "mean"
                }).reset_index()
                
                analysis["time_series_data"] = daily_stats.to_dict("records")
            
            # 生成摘要
            if analysis["performance_by_agent"]:
                best_agent = max(analysis["performance_by_agent"].items(), 
                               key=lambda x: x[1]["total_pnl"])
                analysis["summary"] = {
                    "best_performing_agent": best_agent[0],
                    "best_agent_total_pnl": best_agent[1]["total_pnl"],
                    "total_system_sessions": analysis["basic_stats"]["total_sessions"]
                }
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"分析交易性能失败: {e}")
            return {"error": str(e)}
    
    def _analyze_prompt_optimizations(self, optimizations: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析提示词优化"""
        try:
            # 转换为DataFrame
            opt_data = []
            for opt in optimizations:
                opt_info = {
                    "optimization_id": opt.get("optimization_id"),
                    "agent_id": opt.get("agent_id"),
                    "timestamp": opt.get("timestamp"),
                    "original_length": len(opt.get("original_prompt", "")),
                    "optimized_length": len(opt.get("optimized_prompt", "")),
                    "optimization_reason": opt.get("optimization_reason", "")
                }
                
                # 添加性能指标
                metrics = opt.get("performance_metrics", {})
                for metric_name, value in metrics.items():
                    opt_info[f"metric_{metric_name}"] = value
                
                opt_data.append(opt_info)
            
            df = pd.DataFrame(opt_data)
            
            # 基本统计
            analysis = {
                "basic_stats": {
                    "total_optimizations": len(df),
                    "unique_agents": df["agent_id"].nunique() if not df.empty else 0,
                    "date_range": {
                        "start": df["timestamp"].min() if not df.empty else None,
                        "end": df["timestamp"].max() if not df.empty else None
                    }
                },
                "optimization_by_agent": {},
                "prompt_length_analysis": {},
                "time_series_data": [],
                "summary": {}
            }
            
            # 按智能体分析
            if not df.empty:
                for agent_id in df["agent_id"].unique():
                    agent_data = df[df["agent_id"] == agent_id]
                    
                    analysis["optimization_by_agent"][agent_id] = {
                        "optimization_count": len(agent_data),
                        "avg_original_length": float(agent_data["original_length"].mean()),
                        "avg_optimized_length": float(agent_data["optimized_length"].mean()),
                        "length_change_ratio": float(
                            (agent_data["optimized_length"] - agent_data["original_length"]).mean() /
                            agent_data["original_length"].mean()
                        ) if agent_data["original_length"].mean() > 0 else 0
                    }
                
                # 提示词长度分析
                analysis["prompt_length_analysis"] = {
                    "original_length_stats": {
                        "mean": float(df["original_length"].mean()),
                        "std": float(df["original_length"].std()),
                        "min": int(df["original_length"].min()),
                        "max": int(df["original_length"].max())
                    },
                    "optimized_length_stats": {
                        "mean": float(df["optimized_length"].mean()),
                        "std": float(df["optimized_length"].std()),
                        "min": int(df["optimized_length"].min()),
                        "max": int(df["optimized_length"].max())
                    }
                }
                
                # 时间序列数据
                df["date"] = pd.to_datetime(df["timestamp"]).dt.date
                daily_opts = df.groupby("date").size().reset_index(name="optimization_count")
                analysis["time_series_data"] = daily_opts.to_dict("records")
                
                # 生成摘要
                most_optimized_agent = max(analysis["optimization_by_agent"].items(),
                                         key=lambda x: x[1]["optimization_count"])
                analysis["summary"] = {
                    "most_optimized_agent": most_optimized_agent[0],
                    "most_optimized_count": most_optimized_agent[1]["optimization_count"],
                    "avg_optimizations_per_agent": len(df) / df["agent_id"].nunique()
                }
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"分析提示词优化失败: {e}")
            return {"error": str(e)}
    
    def _export_to_excel(self, analysis_data: Dict[str, Any], export_dir: str, filename: str) -> Optional[str]:
        """导出到Excel文件"""
        try:
            file_path = os.path.join(export_dir, f"{filename}.xlsx")
            
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                # 基本统计
                basic_stats = analysis_data.get("basic_stats", {})
                if basic_stats:
                    stats_df = pd.DataFrame([basic_stats])
                    stats_df.to_excel(writer, sheet_name="基本统计", index=False)
                
                # 按智能体的性能/优化数据
                agent_data = analysis_data.get("performance_by_agent") or analysis_data.get("optimization_by_agent", {})
                if agent_data:
                    agent_df = pd.DataFrame.from_dict(agent_data, orient='index')
                    agent_df.to_excel(writer, sheet_name="智能体分析")
                
                # 时间序列数据
                time_series = analysis_data.get("time_series_data", [])
                if time_series:
                    ts_df = pd.DataFrame(time_series)
                    ts_df.to_excel(writer, sheet_name="时间序列", index=False)
                
                # 摘要
                summary = analysis_data.get("summary", {})
                if summary:
                    summary_df = pd.DataFrame([summary])
                    summary_df.to_excel(writer, sheet_name="摘要", index=False)
            
            self.logger.info(f"Excel文件已导出: {file_path}")
            return file_path
            
        except Exception as e:
            self.logger.error(f"导出Excel文件失败: {e}")
            return None
    
    def _export_to_csv(self, analysis_data: Dict[str, Any], export_dir: str) -> List[str]:
        """导出到CSV文件"""
        exported_files = []
        
        try:
            # 导出智能体数据
            agent_data = analysis_data.get("performance_by_agent") or analysis_data.get("optimization_by_agent", {})
            if agent_data:
                agent_df = pd.DataFrame.from_dict(agent_data, orient='index')
                file_path = os.path.join(export_dir, "agent_analysis.csv")
                agent_df.to_csv(file_path, encoding='utf-8-sig')
                exported_files.append(file_path)
            
            # 导出时间序列数据
            time_series = analysis_data.get("time_series_data", [])
            if time_series:
                ts_df = pd.DataFrame(time_series)
                file_path = os.path.join(export_dir, "time_series.csv")
                ts_df.to_csv(file_path, index=False, encoding='utf-8-sig')
                exported_files.append(file_path)
            
            self.logger.info(f"CSV文件已导出: {len(exported_files)} 个文件")
            return exported_files
            
        except Exception as e:
            self.logger.error(f"导出CSV文件失败: {e}")
            return []
    
    def _export_to_json(self, analysis_data: Dict[str, Any], export_dir: str, filename: str) -> Optional[str]:
        """导出到JSON文件"""
        try:
            file_path = os.path.join(export_dir, f"{filename}.json")
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(analysis_data, f, indent=2, ensure_ascii=False, default=str)
            
            self.logger.info(f"JSON文件已导出: {file_path}")
            return file_path
            
        except Exception as e:
            self.logger.error(f"导出JSON文件失败: {e}")
            return None

    def _generate_performance_charts(self, analysis_data: Dict[str, Any], export_dir: str) -> List[str]:
        """生成性能分析图表"""
        chart_paths = []

        try:
            # 1. 智能体性能对比图
            agent_data = analysis_data.get("performance_by_agent", {})
            if agent_data:
                fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

                agents = list(agent_data.keys())
                total_pnls = [agent_data[agent]["total_pnl"] for agent in agents]
                win_rates = [agent_data[agent]["win_rate"] for agent in agents]

                # 总盈亏图
                bars1 = ax1.bar(agents, total_pnls, color=['green' if pnl > 0 else 'red' for pnl in total_pnls])
                ax1.set_title('智能体总盈亏对比', fontsize=14, fontweight='bold')
                ax1.set_ylabel('总盈亏')
                ax1.tick_params(axis='x', rotation=45)

                # 添加数值标签
                for bar, pnl in zip(bars1, total_pnls):
                    height = bar.get_height()
                    ax1.text(bar.get_x() + bar.get_width()/2., height,
                            f'{pnl:.2f}', ha='center', va='bottom' if height > 0 else 'top')

                # 胜率图
                bars2 = ax2.bar(agents, win_rates, color='skyblue', alpha=0.7)
                ax2.set_title('智能体胜率对比', fontsize=14, fontweight='bold')
                ax2.set_ylabel('胜率')
                ax2.set_ylim(0, 1)
                ax2.tick_params(axis='x', rotation=45)

                # 添加数值标签
                for bar, rate in zip(bars2, win_rates):
                    height = bar.get_height()
                    ax2.text(bar.get_x() + bar.get_width()/2., height,
                            f'{rate:.2%}', ha='center', va='bottom')

                plt.tight_layout()
                chart_path = os.path.join(export_dir, "agent_performance_comparison.png")
                plt.savefig(chart_path, dpi=300, bbox_inches='tight')
                plt.close()
                chart_paths.append(chart_path)

            # 2. 时间序列图
            time_series = analysis_data.get("time_series_data", [])
            if time_series:
                ts_df = pd.DataFrame(time_series)
                if "date" in ts_df.columns:
                    ts_df["date"] = pd.to_datetime(ts_df["date"])

                    fig, ax = plt.subplots(figsize=(12, 6))

                    if "session_id" in ts_df.columns:
                        ax.plot(ts_df["date"], ts_df["session_id"], marker='o', linewidth=2)
                        ax.set_ylabel('每日会话数')
                        ax.set_title('每日交易会话趋势', fontsize=14, fontweight='bold')
                    elif "optimization_count" in ts_df.columns:
                        ax.plot(ts_df["date"], ts_df["optimization_count"], marker='o', linewidth=2, color='orange')
                        ax.set_ylabel('每日优化次数')
                        ax.set_title('每日提示词优化趋势', fontsize=14, fontweight='bold')

                    ax.set_xlabel('日期')
                    ax.grid(True, alpha=0.3)
                    plt.xticks(rotation=45)
                    plt.tight_layout()

                    chart_path = os.path.join(export_dir, "time_series_trend.png")
                    plt.savefig(chart_path, dpi=300, bbox_inches='tight')
                    plt.close()
                    chart_paths.append(chart_path)

            self.logger.info(f"生成了 {len(chart_paths)} 个性能图表")
            return chart_paths

        except Exception as e:
            self.logger.error(f"生成性能图表失败: {e}")
            return []

    def _generate_optimization_charts(self, analysis_data: Dict[str, Any], export_dir: str) -> List[str]:
        """生成优化分析图表"""
        chart_paths = []

        try:
            # 1. 智能体优化次数对比
            agent_data = analysis_data.get("optimization_by_agent", {})
            if agent_data:
                fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

                agents = list(agent_data.keys())
                opt_counts = [agent_data[agent]["optimization_count"] for agent in agents]
                length_changes = [agent_data[agent]["length_change_ratio"] for agent in agents]

                # 优化次数图
                bars1 = ax1.bar(agents, opt_counts, color='lightblue', alpha=0.7)
                ax1.set_title('智能体优化次数对比', fontsize=14, fontweight='bold')
                ax1.set_ylabel('优化次数')
                ax1.tick_params(axis='x', rotation=45)

                # 添加数值标签
                for bar, count in zip(bars1, opt_counts):
                    height = bar.get_height()
                    ax1.text(bar.get_x() + bar.get_width()/2., height,
                            f'{count}', ha='center', va='bottom')

                # 长度变化比率图
                colors = ['green' if ratio > 0 else 'red' for ratio in length_changes]
                bars2 = ax2.bar(agents, length_changes, color=colors, alpha=0.7)
                ax2.set_title('提示词长度变化比率', fontsize=14, fontweight='bold')
                ax2.set_ylabel('长度变化比率')
                ax2.tick_params(axis='x', rotation=45)
                ax2.axhline(y=0, color='black', linestyle='-', alpha=0.3)

                # 添加数值标签
                for bar, ratio in zip(bars2, length_changes):
                    height = bar.get_height()
                    ax2.text(bar.get_x() + bar.get_width()/2., height,
                            f'{ratio:.2%}', ha='center', va='bottom' if height > 0 else 'top')

                plt.tight_layout()
                chart_path = os.path.join(export_dir, "optimization_comparison.png")
                plt.savefig(chart_path, dpi=300, bbox_inches='tight')
                plt.close()
                chart_paths.append(chart_path)

            # 2. 提示词长度分布图
            length_analysis = analysis_data.get("prompt_length_analysis", {})
            if length_analysis:
                fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

                original_stats = length_analysis.get("original_length_stats", {})
                optimized_stats = length_analysis.get("optimized_length_stats", {})

                if original_stats and optimized_stats:
                    # 长度对比
                    categories = ['平均长度', '最小长度', '最大长度']
                    original_values = [original_stats.get("mean", 0), original_stats.get("min", 0), original_stats.get("max", 0)]
                    optimized_values = [optimized_stats.get("mean", 0), optimized_stats.get("min", 0), optimized_stats.get("max", 0)]

                    x = np.arange(len(categories))
                    width = 0.35

                    ax1.bar(x - width/2, original_values, width, label='原始提示词', alpha=0.7)
                    ax1.bar(x + width/2, optimized_values, width, label='优化提示词', alpha=0.7)

                    ax1.set_title('提示词长度统计对比', fontsize=14, fontweight='bold')
                    ax1.set_ylabel('字符数')
                    ax1.set_xticks(x)
                    ax1.set_xticklabels(categories)
                    ax1.legend()

                    # 标准差对比
                    std_categories = ['原始提示词', '优化提示词']
                    std_values = [original_stats.get("std", 0), optimized_stats.get("std", 0)]

                    ax2.bar(std_categories, std_values, color=['orange', 'purple'], alpha=0.7)
                    ax2.set_title('提示词长度标准差对比', fontsize=14, fontweight='bold')
                    ax2.set_ylabel('标准差')

                    # 添加数值标签
                    for i, v in enumerate(std_values):
                        ax2.text(i, v, f'{v:.1f}', ha='center', va='bottom')

                plt.tight_layout()
                chart_path = os.path.join(export_dir, "prompt_length_analysis.png")
                plt.savefig(chart_path, dpi=300, bbox_inches='tight')
                plt.close()
                chart_paths.append(chart_path)

            self.logger.info(f"生成了 {len(chart_paths)} 个优化图表")
            return chart_paths

        except Exception as e:
            self.logger.error(f"生成优化图表失败: {e}")
            return []

    def generate_comprehensive_dashboard(self, output_dir: Optional[str] = None) -> Dict[str, Any]:
        """
        生成综合数据仪表板

        参数:
            output_dir: 输出目录

        返回:
            仪表板生成结果
        """
        try:
            if not output_dir:
                output_dir = os.path.join(self.storage_manager.config.exports_path,
                                        f"dashboard_{datetime.now().strftime('%Y%m%d_%H%M%S')}")

            os.makedirs(output_dir, exist_ok=True)

            # 获取所有数据
            trading_sessions = self.storage_manager.get_trading_sessions()
            prompt_optimizations = self.storage_manager.get_prompt_optimizations()

            dashboard_data = {
                "generation_time": datetime.now().isoformat(),
                "data_summary": {
                    "total_trading_sessions": len(trading_sessions),
                    "total_prompt_optimizations": len(prompt_optimizations),
                    "data_date_range": {
                        "earliest_trading": min([s.get("timestamp", "") for s in trading_sessions]) if trading_sessions else None,
                        "latest_trading": max([s.get("timestamp", "") for s in trading_sessions]) if trading_sessions else None,
                        "earliest_optimization": min([o.get("timestamp", "") for o in prompt_optimizations]) if prompt_optimizations else None,
                        "latest_optimization": max([o.get("timestamp", "") for o in prompt_optimizations]) if prompt_optimizations else None
                    }
                },
                "generated_files": []
            }

            # 生成交易分析
            if trading_sessions:
                trading_analysis = self.export_trading_performance_analysis(output_format="json")
                if trading_analysis.get("success"):
                    dashboard_data["trading_analysis"] = trading_analysis
                    dashboard_data["generated_files"].extend(trading_analysis.get("exported_files", []))

            # 生成优化分析
            if prompt_optimizations:
                optimization_analysis = self.export_prompt_optimization_analysis(output_format="json")
                if optimization_analysis.get("success"):
                    dashboard_data["optimization_analysis"] = optimization_analysis
                    dashboard_data["generated_files"].extend(optimization_analysis.get("exported_files", []))

            # 保存仪表板数据
            dashboard_file = os.path.join(output_dir, "dashboard_summary.json")
            with open(dashboard_file, 'w', encoding='utf-8') as f:
                json.dump(dashboard_data, f, indent=2, ensure_ascii=False, default=str)

            dashboard_data["generated_files"].append(dashboard_file)

            return {
                "success": True,
                "dashboard_directory": output_dir,
                "dashboard_file": dashboard_file,
                "total_files": len(dashboard_data["generated_files"]),
                "data_summary": dashboard_data["data_summary"]
            }

        except Exception as e:
            self.logger.error(f"生成综合仪表板失败: {e}")
            return {"success": False, "error": str(e)}
