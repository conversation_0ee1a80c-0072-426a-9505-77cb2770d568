#!/usr/bin/env python3
"""
并发评估演示脚本

本脚本演示如何使用优化后的ContributionAssessor进行并发评估，
充分利用LLM API的30个并发限制，显著提升测试运行速度。

使用示例:
python concurrent_assessment_demo.py --llm-provider zhipuai --concurrent-workers 30
"""

import sys
import argparse
import logging
from datetime import datetime, timedelta
from typing import List

# 添加项目路径
sys.path.append('.')

from contribution_assessment.assessor import ContributionAssessor


def setup_logging(verbose: bool = False) -> logging.Logger:
    """设置日志记录"""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)


def generate_date_range(start_date: str, days: int) -> List[str]:
    """生成连续的日期范围"""
    start = datetime.strptime(start_date, "%Y-%m-%d")
    return [(start + timedelta(days=i)).strftime("%Y-%m-%d") for i in range(days)]


def demo_concurrent_assessment(llm_provider: str, 
                             concurrent_workers: int = 30,
                             test_days: int = 5,
                             verbose: bool = False):
    """
    演示并发评估功能
    
    参数:
        llm_provider: LLM提供商
        concurrent_workers: 并发工作数
        test_days: 测试天数
        verbose: 是否详细输出
    """
    logger = setup_logging(verbose)
    
    logger.info("="*70)
    logger.info("🚀 多智能体并发评估演示")
    logger.info("="*70)
    logger.info(f"LLM提供商: {llm_provider}")
    logger.info(f"并发工作数: {concurrent_workers}")
    logger.info(f"测试天数: {test_days}")
    
    # 生成测试日期范围
    test_dates = generate_date_range("2025-01-01", test_days)
    logger.info(f"测试日期: {test_dates}")
    
    # 创建评估器配置
    config = {
        "start_date": "2025-01-01",
        "end_date": test_dates[-1],
        "stocks": ["AAPL"],
        "starting_cash": 1000000,
        "risk_free_rate": 0.02,
        "simulation_days": 1,  # 每天模拟1天
        "enable_concurrent_execution": True,
        "verbose": verbose
    }
    
    # 初始化评估器
    logger.info("🔧 初始化评估器...")
    assessor = ContributionAssessor(
        config=config,
        logger=logger,
        llm_provider=llm_provider
    )
    
    # 设置并发参数
    assessor.set_concurrent_execution(True, concurrent_workers)
    
    # 执行并发评估
    logger.info("⚡ 开始并发评估...")
    start_time = datetime.now()
    
    try:
        # 方式1: 使用标准并发评估
        logger.info("📊 方式1: 标准并发评估")
        result = assessor.run(
            agents=None,  # 使用模拟智能体
            target_agents=["NAA", "TAA", "FAA", "TRA"],
            max_coalitions=20  # 限制联盟数量以加快演示
        )
        
        # 方式2: 使用日期范围并发评估
        logger.info("📅 方式2: 日期范围并发评估")
        daily_result = assessor.run_concurrent_daily_assessment(
            date_range=test_dates[:3],  # 只测试前3天
            agents=None,
            target_agents=["NAA", "TAA", "FAA", "TRA"]
        )
        
        end_time = datetime.now()
        total_time = (end_time - start_time).total_seconds()
        
        # 显示结果
        logger.info("="*70)
        logger.info("📈 评估结果汇总")
        logger.info("="*70)
        
        if result.get("success", False):
            logger.info("✅ 标准并发评估成功完成")
            shapley_values = result.get("shapley_values", {})
            for agent, value in shapley_values.items():
                logger.info(f"  {agent}: {value:.6f}")
            
            # 显示并发统计
            simulation_stats = result.get("phase_results", {}).get("trading_simulation", {}).get("simulation_stats", {})
            if simulation_stats.get("concurrent_execution", False):
                logger.info(f"📊 并发模拟统计:")
                logger.info(f"  总联盟数: {simulation_stats.get('total_coalitions', 0)}")
                logger.info(f"  成功数: {simulation_stats.get('successful_simulations', 0)}")
                logger.info(f"  失败数: {simulation_stats.get('failed_simulations', 0)}")
                logger.info(f"  模拟时间: {simulation_stats.get('total_simulation_time', 0):.2f}s")
        
        if daily_result.get("success", False):
            logger.info("✅ 日期范围并发评估成功完成")
            daily_results = daily_result.get("daily_results", {})
            weekly_results = daily_result.get("weekly_aggregated_results", {})
            
            logger.info(f"📅 处理日期数: {len(daily_results)}")
            logger.info(f"🗓️  完成周数: {len(weekly_results)}")
            
            for week_id, week_data in weekly_results.items():
                if week_data.get("success", False):
                    logger.info(f"  {week_id} Shapley值:")
                    for agent, value in week_data.get("shapley_values", {}).items():
                        logger.info(f"    {agent}: {value:.6f}")
        
        # 显示性能统计
        logger.info("="*70)
        logger.info("⚡ 性能统计")
        logger.info("="*70)
        logger.info(f"总执行时间: {total_time:.2f}s")
        
        concurrent_stats = assessor.get_concurrent_stats()
        logger.info(f"并发任务总数: {concurrent_stats.get('total_concurrent_tasks', 0)}")
        logger.info(f"并发成功率: {concurrent_stats.get('concurrent_success_rate', 0):.1f}%")
        logger.info(f"平均任务时间: {concurrent_stats.get('average_concurrent_task_time', 0):.2f}s")
        
        # 性能对比估算
        estimated_serial_time = concurrent_stats.get('total_concurrent_tasks', 1) * concurrent_stats.get('average_concurrent_task_time', 1)
        speedup = estimated_serial_time / concurrent_stats.get('concurrent_execution_time', 1)
        logger.info(f"估算串行时间: {estimated_serial_time:.2f}s")
        logger.info(f"并发加速比: {speedup:.1f}x")
        
    except Exception as e:
        logger.error(f"❌ 评估过程中发生错误: {e}")
        return False
    
    logger.info("="*70)
    logger.info("🎉 并发评估演示完成！")
    logger.info("="*70)
    
    return True


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="多智能体并发评估演示")
    parser.add_argument("--llm-provider", default="zhipuai", 
                       help="LLM提供商 (默认: zhipuai)")
    parser.add_argument("--concurrent-workers", type=int, default=30,
                       help="并发工作数 (默认: 30)")
    parser.add_argument("--test-days", type=int, default=5,
                       help="测试天数 (默认: 5)")
    parser.add_argument("--verbose", action="store_true",
                       help="详细输出")
    
    args = parser.parse_args()
    
    # 运行演示
    success = demo_concurrent_assessment(
        llm_provider=args.llm_provider,
        concurrent_workers=args.concurrent_workers,
        test_days=args.test_days,
        verbose=args.verbose
    )
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()