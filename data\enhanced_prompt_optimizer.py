#!/usr/bin/env python3
"""
增强的提示词优化系统 (Enhanced Prompt Optimization System)

实现完整的周期性提示词优化工作流：
1. 周级回测和贡献分析
2. 提示词优化和A/B测试
3. 综合数据存储
4. 性能跟踪和报告生成

作者: AI Assistant
创建时间: 2025-07-04
"""

import os
import json
import logging
import sqlite3
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
import numpy as np
from pathlib import Path

@dataclass
class PromptOptimizationRecord:
    """提示词优化记录"""
    optimization_id: str
    agent_id: str
    week_number: int
    original_prompt: str
    optimized_prompt: str
    optimization_reason: str
    performance_before: float
    performance_after: float
    improvement_ratio: float
    optimization_timestamp: str
    ab_test_results: Dict[str, Any]
    metadata: Dict[str, Any]

@dataclass
class WeeklyPerformanceData:
    """周级性能数据"""
    week_id: str
    week_number: int
    start_date: str
    end_date: str
    agent_contributions: Dict[str, float]
    coalition_performances: Dict[str, float]
    shapley_values: Dict[str, float]
    optimization_triggers: List[str]
    performance_summary: Dict[str, Any]

class EnhancedPromptOptimizer:
    """增强的提示词优化系统"""
    
    def __init__(self, 
                 config: Dict[str, Any],
                 logger: Optional[logging.Logger] = None):
        """
        初始化增强的提示词优化系统
        
        参数:
            config: 配置字典
            logger: 日志记录器
        """
        self.config = config
        self.logger = logger or self._create_default_logger()
        
        # 数据存储配置
        self.data_dir = config.get("data_dir", "data/prompt_optimization")
        self.db_path = os.path.join(self.data_dir, "prompt_optimization.db")
        
        # 优化配置
        self.optimization_threshold = config.get("optimization_threshold", 0.8)
        self.ab_test_duration_days = config.get("ab_test_duration_days", 7)
        self.min_improvement_ratio = config.get("min_improvement_ratio", 0.05)
        
        # 创建数据目录和数据库
        self._setup_data_storage()
        
        # 统计信息
        self._stats = {
            "total_optimizations": 0,
            "successful_optimizations": 0,
            "ab_tests_conducted": 0,
            "average_improvement": 0.0,
            "last_optimization": None
        }
        
        self.logger.info("增强的提示词优化系统初始化完成")
    
    def _create_default_logger(self) -> logging.Logger:
        """创建默认日志记录器"""
        logger = logging.getLogger(f"{__name__}.EnhancedPromptOptimizer")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def _setup_data_storage(self):
        """设置数据存储"""
        os.makedirs(self.data_dir, exist_ok=True)
        
        # 创建数据库表
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 提示词优化记录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS prompt_optimizations (
                    optimization_id TEXT PRIMARY KEY,
                    agent_id TEXT NOT NULL,
                    week_number INTEGER NOT NULL,
                    original_prompt TEXT NOT NULL,
                    optimized_prompt TEXT NOT NULL,
                    optimization_reason TEXT,
                    performance_before REAL,
                    performance_after REAL,
                    improvement_ratio REAL,
                    optimization_timestamp TEXT,
                    ab_test_results TEXT,
                    metadata TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 周级性能数据表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS weekly_performance (
                    week_id TEXT PRIMARY KEY,
                    week_number INTEGER NOT NULL,
                    start_date TEXT NOT NULL,
                    end_date TEXT NOT NULL,
                    agent_contributions TEXT,
                    coalition_performances TEXT,
                    shapley_values TEXT,
                    optimization_triggers TEXT,
                    performance_summary TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # A/B测试结果表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS ab_test_results (
                    test_id TEXT PRIMARY KEY,
                    agent_id TEXT NOT NULL,
                    optimization_id TEXT,
                    test_start_date TEXT,
                    test_end_date TEXT,
                    control_prompt TEXT,
                    variant_prompts TEXT,
                    test_results TEXT,
                    winning_variant TEXT,
                    confidence_level REAL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (optimization_id) REFERENCES prompt_optimizations (optimization_id)
                )
            ''')
            
            conn.commit()
        
        self.logger.info(f"数据存储设置完成: {self.db_path}")
    
    def process_weekly_performance(self, 
                                 week_number: int,
                                 start_date: str,
                                 end_date: str,
                                 agent_contributions: Dict[str, float],
                                 coalition_performances: Dict[str, float],
                                 shapley_values: Dict[str, float]) -> Dict[str, Any]:
        """
        处理周级性能数据
        
        参数:
            week_number: 周数
            start_date: 开始日期
            end_date: 结束日期
            agent_contributions: 智能体贡献度
            coalition_performances: 联盟性能
            shapley_values: Shapley值
            
        返回:
            处理结果
        """
        try:
            week_id = f"week_{week_number}_{start_date.replace('-', '')}"
            
            # 分析性能并识别需要优化的智能体
            optimization_triggers = self._identify_optimization_triggers(
                agent_contributions, shapley_values
            )
            
            # 生成性能摘要
            performance_summary = self._generate_performance_summary(
                agent_contributions, coalition_performances, shapley_values
            )
            
            # 创建周级性能数据对象
            weekly_data = WeeklyPerformanceData(
                week_id=week_id,
                week_number=week_number,
                start_date=start_date,
                end_date=end_date,
                agent_contributions=agent_contributions,
                coalition_performances=coalition_performances,
                shapley_values=shapley_values,
                optimization_triggers=optimization_triggers,
                performance_summary=performance_summary
            )
            
            # 存储到数据库
            self._store_weekly_performance(weekly_data)
            
            # 如果有需要优化的智能体，触发优化流程
            optimization_results = []
            if optimization_triggers:
                self.logger.info(f"第 {week_number} 周检测到 {len(optimization_triggers)} 个智能体需要优化")
                
                for agent_id in optimization_triggers:
                    optimization_result = self._trigger_agent_optimization(
                        agent_id, week_number, agent_contributions.get(agent_id, 0.0)
                    )
                    optimization_results.append(optimization_result)
            
            return {
                "success": True,
                "week_id": week_id,
                "optimization_triggers": optimization_triggers,
                "optimization_results": optimization_results,
                "performance_summary": performance_summary
            }
            
        except Exception as e:
            self.logger.error(f"处理第 {week_number} 周性能数据失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _identify_optimization_triggers(self, 
                                      agent_contributions: Dict[str, float],
                                      shapley_values: Dict[str, float]) -> List[str]:
        """识别需要优化的智能体"""
        optimization_triggers = []
        
        # 计算平均贡献度
        if agent_contributions:
            mean_contribution = sum(agent_contributions.values()) / len(agent_contributions)
            threshold = mean_contribution * self.optimization_threshold
            
            for agent_id, contribution in agent_contributions.items():
                if contribution < threshold:
                    optimization_triggers.append(agent_id)
                    self.logger.info(f"智能体 {agent_id} 贡献度 {contribution:.4f} 低于阈值 {threshold:.4f}")
        
        return optimization_triggers
    
    def _generate_performance_summary(self,
                                    agent_contributions: Dict[str, float],
                                    coalition_performances: Dict[str, float],
                                    shapley_values: Dict[str, float]) -> Dict[str, Any]:
        """生成性能摘要"""
        summary = {
            "total_agents": len(agent_contributions),
            "total_coalitions": len(coalition_performances),
            "mean_agent_contribution": np.mean(list(agent_contributions.values())) if agent_contributions else 0.0,
            "std_agent_contribution": np.std(list(agent_contributions.values())) if agent_contributions else 0.0,
            "best_performing_agent": max(agent_contributions.items(), key=lambda x: x[1]) if agent_contributions else None,
            "worst_performing_agent": min(agent_contributions.items(), key=lambda x: x[1]) if agent_contributions else None,
            "best_coalition_performance": max(coalition_performances.values()) if coalition_performances else 0.0,
            "mean_coalition_performance": np.mean(list(coalition_performances.values())) if coalition_performances else 0.0
        }
        
        return summary
    
    def _store_weekly_performance(self, weekly_data: WeeklyPerformanceData):
        """存储周级性能数据"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO weekly_performance 
                (week_id, week_number, start_date, end_date, agent_contributions, 
                 coalition_performances, shapley_values, optimization_triggers, performance_summary)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                weekly_data.week_id,
                weekly_data.week_number,
                weekly_data.start_date,
                weekly_data.end_date,
                json.dumps(weekly_data.agent_contributions),
                json.dumps({str(k): v for k, v in weekly_data.coalition_performances.items()}),
                json.dumps(weekly_data.shapley_values),
                json.dumps(weekly_data.optimization_triggers),
                json.dumps(weekly_data.performance_summary)
            ))
            
            conn.commit()
    
    def _trigger_agent_optimization(self, 
                                  agent_id: str, 
                                  week_number: int, 
                                  current_performance: float) -> Dict[str, Any]:
        """触发智能体优化"""
        try:
            optimization_id = f"opt_{agent_id}_{week_number}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # 获取当前提示词
            current_prompt = self._get_current_prompt(agent_id)
            
            # 生成优化后的提示词
            optimized_prompt = self._generate_optimized_prompt(agent_id, current_prompt, current_performance)
            
            # 创建优化记录
            optimization_record = PromptOptimizationRecord(
                optimization_id=optimization_id,
                agent_id=agent_id,
                week_number=week_number,
                original_prompt=current_prompt,
                optimized_prompt=optimized_prompt,
                optimization_reason=f"性能低于阈值 (当前: {current_performance:.4f})",
                performance_before=current_performance,
                performance_after=0.0,  # 将在A/B测试后更新
                improvement_ratio=0.0,  # 将在A/B测试后更新
                optimization_timestamp=datetime.now().isoformat(),
                ab_test_results={},
                metadata={"week_number": week_number, "trigger_reason": "low_performance"}
            )
            
            # 存储优化记录
            self._store_optimization_record(optimization_record)
            
            # 启动A/B测试
            ab_test_result = self._start_ab_test(optimization_record)
            
            self._stats["total_optimizations"] += 1
            self._stats["last_optimization"] = datetime.now().isoformat()
            
            return {
                "success": True,
                "optimization_id": optimization_id,
                "agent_id": agent_id,
                "ab_test_started": ab_test_result.get("success", False)
            }
            
        except Exception as e:
            self.logger.error(f"触发智能体 {agent_id} 优化失败: {e}")
            return {
                "success": False,
                "agent_id": agent_id,
                "error": str(e)
            }

    def _get_current_prompt(self, agent_id: str) -> str:
        """获取智能体当前提示词"""
        # 这里应该从智能体系统获取当前提示词
        # 暂时返回模拟提示词
        default_prompts = {
            "NAA": "你是一个新闻分析智能体，负责分析市场新闻和事件对股价的影响。",
            "TAA": "你是一个技术分析智能体，负责分析股价图表和技术指标。",
            "FAA": "你是一个基本面分析智能体，负责分析公司财务数据和基本面指标。",
            "TRA": "你是一个交易智能体，负责根据分析结果做出交易决策。"
        }

        return default_prompts.get(agent_id, f"你是智能体 {agent_id}，负责协助交易决策。")

    def _generate_optimized_prompt(self,
                                 agent_id: str,
                                 current_prompt: str,
                                 current_performance: float) -> str:
        """生成优化后的提示词"""
        # 这里应该使用OPRO算法生成优化提示词
        # 暂时返回改进的提示词

        optimization_strategies = {
            "NAA": "请更加关注新闻的市场情绪和投资者反应，提供更具体的影响评估。",
            "TAA": "请结合多个时间框架的技术指标，提供更准确的趋势判断。",
            "FAA": "请深入分析财务比率的变化趋势，关注行业对比和竞争优势。",
            "TRA": "请更加谨慎地评估风险收益比，优化仓位管理策略。"
        }

        strategy = optimization_strategies.get(agent_id, "请提高分析的准确性和实用性。")

        optimized_prompt = f"{current_prompt}\n\n优化指导：{strategy}\n\n请特别注意提高分析质量和决策准确性。"

        return optimized_prompt

    def _store_optimization_record(self, record: PromptOptimizationRecord):
        """存储优化记录"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            cursor.execute('''
                INSERT OR REPLACE INTO prompt_optimizations
                (optimization_id, agent_id, week_number, original_prompt, optimized_prompt,
                 optimization_reason, performance_before, performance_after, improvement_ratio,
                 optimization_timestamp, ab_test_results, metadata)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                record.optimization_id,
                record.agent_id,
                record.week_number,
                record.original_prompt,
                record.optimized_prompt,
                record.optimization_reason,
                record.performance_before,
                record.performance_after,
                record.improvement_ratio,
                record.optimization_timestamp,
                json.dumps(record.ab_test_results),
                json.dumps(record.metadata)
            ))

            conn.commit()

    def _start_ab_test(self, optimization_record: PromptOptimizationRecord) -> Dict[str, Any]:
        """启动A/B测试"""
        try:
            test_id = f"ab_test_{optimization_record.agent_id}_{optimization_record.week_number}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            # 设置A/B测试参数
            test_start_date = datetime.now().strftime('%Y-%m-%d')
            test_end_date = (datetime.now() + timedelta(days=self.ab_test_duration_days)).strftime('%Y-%m-%d')

            # 准备测试变体
            variant_prompts = {
                "control": optimization_record.original_prompt,
                "variant_1": optimization_record.optimized_prompt
            }

            # 存储A/B测试记录
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    INSERT INTO ab_test_results
                    (test_id, agent_id, optimization_id, test_start_date, test_end_date,
                     control_prompt, variant_prompts, test_results, winning_variant, confidence_level)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    test_id,
                    optimization_record.agent_id,
                    optimization_record.optimization_id,
                    test_start_date,
                    test_end_date,
                    optimization_record.original_prompt,
                    json.dumps(variant_prompts),
                    json.dumps({}),  # 测试结果将在测试完成后更新
                    "",  # 获胜变体将在测试完成后确定
                    0.0  # 置信度将在测试完成后计算
                ))

                conn.commit()

            self._stats["ab_tests_conducted"] += 1

            self.logger.info(f"A/B测试已启动: {test_id} (智能体: {optimization_record.agent_id})")

            return {
                "success": True,
                "test_id": test_id,
                "test_start_date": test_start_date,
                "test_end_date": test_end_date,
                "variant_count": len(variant_prompts)
            }

        except Exception as e:
            self.logger.error(f"启动A/B测试失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def complete_ab_test(self,
                        test_id: str,
                        test_results: Dict[str, Any]) -> Dict[str, Any]:
        """完成A/B测试并分析结果"""
        try:
            # 分析测试结果
            analysis_result = self._analyze_ab_test_results(test_results)

            # 更新数据库
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    UPDATE ab_test_results
                    SET test_results = ?, winning_variant = ?, confidence_level = ?
                    WHERE test_id = ?
                ''', (
                    json.dumps(test_results),
                    analysis_result["winning_variant"],
                    analysis_result["confidence_level"],
                    test_id
                ))

                conn.commit()

            # 如果优化版本获胜，更新优化记录
            if analysis_result["winning_variant"] == "variant_1":
                self._apply_optimization(test_id, analysis_result)
                self._stats["successful_optimizations"] += 1

            self.logger.info(f"A/B测试完成: {test_id}, 获胜变体: {analysis_result['winning_variant']}")

            return {
                "success": True,
                "test_id": test_id,
                "winning_variant": analysis_result["winning_variant"],
                "confidence_level": analysis_result["confidence_level"],
                "improvement_ratio": analysis_result.get("improvement_ratio", 0.0)
            }

        except Exception as e:
            self.logger.error(f"完成A/B测试失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def _analyze_ab_test_results(self, test_results: Dict[str, Any]) -> Dict[str, Any]:
        """分析A/B测试结果"""
        # 简化的A/B测试分析
        control_performance = test_results.get("control", {}).get("performance", 0.0)
        variant_performance = test_results.get("variant_1", {}).get("performance", 0.0)

        improvement_ratio = (variant_performance - control_performance) / control_performance if control_performance > 0 else 0.0

        # 简单的统计显著性检验（实际应用中应使用更严格的方法）
        confidence_level = min(abs(improvement_ratio) * 100, 95.0)

        winning_variant = "variant_1" if variant_performance > control_performance else "control"

        return {
            "winning_variant": winning_variant,
            "confidence_level": confidence_level,
            "improvement_ratio": improvement_ratio,
            "control_performance": control_performance,
            "variant_performance": variant_performance
        }

    def _apply_optimization(self, test_id: str, analysis_result: Dict[str, Any]):
        """应用优化结果"""
        # 获取相关的优化记录
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            cursor.execute('''
                SELECT optimization_id FROM ab_test_results WHERE test_id = ?
            ''', (test_id,))

            result = cursor.fetchone()
            if result:
                optimization_id = result[0]

                # 更新优化记录
                cursor.execute('''
                    UPDATE prompt_optimizations
                    SET performance_after = ?, improvement_ratio = ?
                    WHERE optimization_id = ?
                ''', (
                    analysis_result["variant_performance"],
                    analysis_result["improvement_ratio"],
                    optimization_id
                ))

                conn.commit()

    def generate_optimization_report(self,
                                   start_week: Optional[int] = None,
                                   end_week: Optional[int] = None) -> Dict[str, Any]:
        """生成优化报告"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # 查询优化记录
                query = "SELECT * FROM prompt_optimizations"
                params = []

                if start_week is not None and end_week is not None:
                    query += " WHERE week_number BETWEEN ? AND ?"
                    params = [start_week, end_week]

                optimizations_df = pd.read_sql_query(query, conn, params=params)

                # 查询A/B测试结果
                ab_tests_df = pd.read_sql_query("SELECT * FROM ab_test_results", conn)

                # 查询周级性能数据
                weekly_performance_df = pd.read_sql_query("SELECT * FROM weekly_performance", conn)

            # 生成报告
            report = {
                "report_generated_at": datetime.now().isoformat(),
                "period": {
                    "start_week": start_week,
                    "end_week": end_week
                },
                "summary": {
                    "total_optimizations": len(optimizations_df),
                    "successful_optimizations": len(optimizations_df[optimizations_df['improvement_ratio'] > self.min_improvement_ratio]),
                    "total_ab_tests": len(ab_tests_df),
                    "average_improvement": optimizations_df['improvement_ratio'].mean() if not optimizations_df.empty else 0.0,
                    "success_rate": (len(optimizations_df[optimizations_df['improvement_ratio'] > self.min_improvement_ratio]) /
                                   max(len(optimizations_df), 1)) * 100
                },
                "agent_performance": self._analyze_agent_performance(optimizations_df),
                "weekly_trends": self._analyze_weekly_trends(weekly_performance_df),
                "recommendations": self._generate_recommendations(optimizations_df, ab_tests_df)
            }

            # 保存报告
            report_path = os.path.join(self.data_dir, f"optimization_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)

            self.logger.info(f"优化报告已生成: {report_path}")

            return {
                "success": True,
                "report": report,
                "report_path": report_path
            }

        except Exception as e:
            self.logger.error(f"生成优化报告失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def _analyze_agent_performance(self, optimizations_df: pd.DataFrame) -> Dict[str, Any]:
        """分析智能体性能"""
        if optimizations_df.empty:
            return {"message": "无优化数据"}

        agent_stats = {}
        for agent_id in optimizations_df['agent_id'].unique():
            agent_data = optimizations_df[optimizations_df['agent_id'] == agent_id]

            agent_stats[agent_id] = {
                "total_optimizations": len(agent_data),
                "successful_optimizations": len(agent_data[agent_data['improvement_ratio'] > self.min_improvement_ratio]),
                "average_improvement": agent_data['improvement_ratio'].mean(),
                "best_improvement": agent_data['improvement_ratio'].max(),
                "success_rate": (len(agent_data[agent_data['improvement_ratio'] > self.min_improvement_ratio]) /
                               max(len(agent_data), 1)) * 100
            }

        return agent_stats

    def _analyze_weekly_trends(self, weekly_performance_df: pd.DataFrame) -> Dict[str, Any]:
        """分析周级趋势"""
        if weekly_performance_df.empty:
            return {"message": "无周级数据"}

        trends = {
            "total_weeks": len(weekly_performance_df),
            "weeks_with_optimizations": 0,
            "average_agents_per_week": 0,
            "performance_trend": "stable"
        }

        # 分析优化触发趋势
        optimization_counts = []
        for _, row in weekly_performance_df.iterrows():
            triggers = json.loads(row['optimization_triggers']) if row['optimization_triggers'] else []
            optimization_counts.append(len(triggers))
            if len(triggers) > 0:
                trends["weeks_with_optimizations"] += 1

        if optimization_counts:
            trends["average_agents_per_week"] = sum(optimization_counts) / len(optimization_counts)

            # 简单的趋势分析
            if len(optimization_counts) >= 3:
                recent_avg = sum(optimization_counts[-3:]) / 3
                early_avg = sum(optimization_counts[:3]) / 3

                if recent_avg > early_avg * 1.2:
                    trends["performance_trend"] = "declining"
                elif recent_avg < early_avg * 0.8:
                    trends["performance_trend"] = "improving"

        return trends

    def _generate_recommendations(self,
                                optimizations_df: pd.DataFrame,
                                ab_tests_df: pd.DataFrame) -> List[str]:
        """生成优化建议"""
        recommendations = []

        if not optimizations_df.empty:
            # 分析成功率
            success_rate = (len(optimizations_df[optimizations_df['improvement_ratio'] > self.min_improvement_ratio]) /
                          max(len(optimizations_df), 1)) * 100

            if success_rate < 50:
                recommendations.append("优化成功率较低，建议调整优化策略或阈值设置")

            # 分析智能体表现
            agent_performance = optimizations_df.groupby('agent_id')['improvement_ratio'].mean()
            worst_agent = agent_performance.idxmin() if not agent_performance.empty else None

            if worst_agent and agent_performance[worst_agent] < 0:
                recommendations.append(f"智能体 {worst_agent} 优化效果持续不佳，建议重新设计提示词策略")

            # 分析优化频率
            if len(optimizations_df) > 10:
                recent_optimizations = optimizations_df.tail(5)
                if len(recent_optimizations['agent_id'].unique()) < 2:
                    recommendations.append("最近的优化集中在少数智能体上，建议关注其他智能体的性能")

        if not ab_tests_df.empty:
            # 分析A/B测试结果
            completed_tests = ab_tests_df[ab_tests_df['winning_variant'] != '']
            if len(completed_tests) > 0:
                variant_wins = len(completed_tests[completed_tests['winning_variant'] == 'variant_1'])
                variant_win_rate = variant_wins / len(completed_tests) * 100

                if variant_win_rate < 40:
                    recommendations.append("优化版本获胜率较低，建议改进提示词生成算法")

        if not recommendations:
            recommendations.append("系统运行良好，继续保持当前的优化策略")

        return recommendations

    def get_stats(self) -> Dict[str, Any]:
        """获取系统统计信息"""
        # 更新平均改进率
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT AVG(improvement_ratio) FROM prompt_optimizations WHERE improvement_ratio > 0")
                result = cursor.fetchone()
                if result and result[0]:
                    self._stats["average_improvement"] = result[0]
        except Exception:
            pass

        return self._stats.copy()

    def export_data(self, export_format: str = "json") -> Dict[str, Any]:
        """导出优化数据"""
        try:
            export_dir = os.path.join(self.data_dir, "exports")
            os.makedirs(export_dir, exist_ok=True)

            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

            with sqlite3.connect(self.db_path) as conn:
                # 导出所有表的数据
                tables = ["prompt_optimizations", "weekly_performance", "ab_test_results"]
                exported_files = []

                for table in tables:
                    df = pd.read_sql_query(f"SELECT * FROM {table}", conn)

                    if export_format.lower() == "csv":
                        file_path = os.path.join(export_dir, f"{table}_{timestamp}.csv")
                        df.to_csv(file_path, index=False, encoding='utf-8')
                    elif export_format.lower() == "excel":
                        file_path = os.path.join(export_dir, f"{table}_{timestamp}.xlsx")
                        df.to_excel(file_path, index=False)
                    else:  # json
                        file_path = os.path.join(export_dir, f"{table}_{timestamp}.json")
                        df.to_json(file_path, orient='records', indent=2, force_ascii=False)

                    exported_files.append(file_path)

            self.logger.info(f"数据导出完成: {len(exported_files)} 个文件")

            return {
                "success": True,
                "exported_files": exported_files,
                "export_format": export_format
            }

        except Exception as e:
            self.logger.error(f"导出数据失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
