#!/usr/bin/env python3
"""
数据需求分析器

根据训练参数分析数据需求，包括：
1. 训练期间的数据范围计算
2. 新闻数据提前期计算
3. 基本面数据需求分析
4. 数据下载优先级排序
"""

import os
import sys
from datetime import datetime, timedelta, date
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from dateutil.relativedelta import relativedelta
import calendar
import logging

# 添加项目根目录到路径
current_script_path = os.path.abspath(__file__)
project_root = os.path.dirname(os.path.dirname(current_script_path))
sys.path.append(project_root)

@dataclass
class DataRequirementSpec:
    """数据需求规格"""
    stock: str
    training_start_date: str
    training_end_date: str
    news_start_date: str
    news_end_date: str
    ohlcv_start_date: str
    ohlcv_end_date: str
    fundamental_required: bool
    priority: int  # 1=高, 2=中, 3=低

@dataclass
class NewsDownloadPlan:
    """新闻下载计划"""
    stock: str
    monthly_ranges: List[Tuple[str, str]]  # [(start_date, end_date), ...]
    total_months: int

class DataRequirementAnalyzer:
    """数据需求分析器"""
    
    def __init__(self, verbose: bool = True):
        """
        初始化数据需求分析器
        
        Args:
            verbose: 是否显示详细日志
        """
        self.verbose = verbose
        self.logger = self._setup_logger()
        
        # 配置参数
        self.news_advance_months = 1  # 新闻数据提前月数
        self.ohlcv_buffer_days = 30   # OHLCV数据缓冲天数
        
        self.logger.info("数据需求分析器初始化完成")
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger("DataRequirementAnalyzer")
        logger.setLevel(logging.INFO if self.verbose else logging.WARNING)
        
        if not logger.handlers:
            handler = logging.StreamHandler(sys.stderr)
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def analyze_requirements(self, 
                           stocks: List[str], 
                           training_start_date: str, 
                           training_end_date: str) -> List[DataRequirementSpec]:
        """
        分析数据需求
        
        Args:
            stocks: 股票代码列表
            training_start_date: 训练开始日期 (YYYY-MM-DD)
            training_end_date: 训练结束日期 (YYYY-MM-DD)
            
        Returns:
            数据需求规格列表
        """
        self.logger.info(f"分析数据需求: {len(stocks)}只股票")
        self.logger.info(f"训练期间: {training_start_date} 到 {training_end_date}")
        
        requirements = []
        
        for i, stock in enumerate(stocks):
            spec = self._analyze_single_stock_requirement(
                stock, training_start_date, training_end_date, i + 1
            )
            requirements.append(spec)
        
        self.logger.info(f"生成了 {len(requirements)} 个数据需求规格")
        return requirements
    
    def _analyze_single_stock_requirement(self, 
                                        stock: str, 
                                        training_start_date: str, 
                                        training_end_date: str,
                                        priority: int) -> DataRequirementSpec:
        """分析单个股票的数据需求"""
        
        # 解析训练日期
        training_start = datetime.strptime(training_start_date, '%Y-%m-%d')
        training_end = datetime.strptime(training_end_date, '%Y-%m-%d')
        
        # 计算新闻数据范围（提前1个月）
        news_start = training_start - relativedelta(months=self.news_advance_months)
        news_start_date = news_start.strftime('%Y-%m-%d')
        news_end_date = training_end_date
        
        # 计算OHLCV数据范围（增加缓冲期）
        ohlcv_start = training_start - timedelta(days=self.ohlcv_buffer_days)
        ohlcv_start_date = ohlcv_start.strftime('%Y-%m-%d')
        ohlcv_end_date = training_end_date
        
        spec = DataRequirementSpec(
            stock=stock.upper(),
            training_start_date=training_start_date,
            training_end_date=training_end_date,
            news_start_date=news_start_date,
            news_end_date=news_end_date,
            ohlcv_start_date=ohlcv_start_date,
            ohlcv_end_date=ohlcv_end_date,
            fundamental_required=True,  # 默认需要基本面数据
            priority=priority
        )
        
        self.logger.info(f"{stock} 数据需求:")
        self.logger.info(f"  训练期间: {training_start_date} 到 {training_end_date}")
        self.logger.info(f"  新闻期间: {news_start_date} 到 {news_end_date}")
        self.logger.info(f"  OHLCV期间: {ohlcv_start_date} 到 {ohlcv_end_date}")
        
        return spec
    
    def generate_news_download_plan(self, requirement: DataRequirementSpec) -> NewsDownloadPlan:
        """
        生成新闻数据下载计划（按月分批）
        
        Args:
            requirement: 数据需求规格
            
        Returns:
            新闻下载计划
        """
        stock = requirement.stock
        start_date = datetime.strptime(requirement.news_start_date, '%Y-%m-%d')
        end_date = datetime.strptime(requirement.news_end_date, '%Y-%m-%d')
        
        monthly_ranges = []
        current_date = start_date
        
        while current_date <= end_date:
            # 计算当月的开始和结束日期
            month_start = current_date.replace(day=1)
            
            # 计算当月的最后一天
            if current_date.month == 12:
                next_month = current_date.replace(year=current_date.year + 1, month=1, day=1)
            else:
                next_month = current_date.replace(month=current_date.month + 1, day=1)
            
            month_end = next_month - timedelta(days=1)
            
            # 确保不超过需求的结束日期
            if month_end > end_date:
                month_end = end_date
            
            monthly_ranges.append((
                month_start.strftime('%Y-%m-%d'),
                month_end.strftime('%Y-%m-%d')
            ))
            
            # 移动到下个月
            if current_date.month == 12:
                current_date = current_date.replace(year=current_date.year + 1, month=1, day=1)
            else:
                current_date = current_date.replace(month=current_date.month + 1, day=1)
        
        plan = NewsDownloadPlan(
            stock=stock,
            monthly_ranges=monthly_ranges,
            total_months=len(monthly_ranges)
        )
        
        self.logger.info(f"{stock} 新闻下载计划: {plan.total_months}个月")
        for i, (start, end) in enumerate(monthly_ranges, 1):
            self.logger.info(f"  月份 {i}: {start} 到 {end}")
        
        return plan
    
    def estimate_api_calls(self, requirements: List[DataRequirementSpec]) -> Dict[str, int]:
        """
        估算API调用次数
        
        Args:
            requirements: 数据需求规格列表
            
        Returns:
            API调用次数估算
        """
        estimation = {
            "ohlcv_calls": 0,
            "fundamental_calls": 0,
            "news_calls": 0,
            "total_calls": 0
        }
        
        for req in requirements:
            # OHLCV数据：每个股票1次调用（outputsize=full）
            estimation["ohlcv_calls"] += 1
            
            # 基本面数据：每个股票2次调用（年度+季度）
            if req.fundamental_required:
                estimation["fundamental_calls"] += 2
            
            # 新闻数据：按月计算
            news_plan = self.generate_news_download_plan(req)
            estimation["news_calls"] += news_plan.total_months
        
        estimation["total_calls"] = (
            estimation["ohlcv_calls"] + 
            estimation["fundamental_calls"] + 
            estimation["news_calls"]
        )
        
        self.logger.info(f"API调用次数估算:")
        self.logger.info(f"  OHLCV: {estimation['ohlcv_calls']} 次")
        self.logger.info(f"  基本面: {estimation['fundamental_calls']} 次")
        self.logger.info(f"  新闻: {estimation['news_calls']} 次")
        self.logger.info(f"  总计: {estimation['total_calls']} 次")
        
        return estimation
    
    def optimize_download_order(self, requirements: List[DataRequirementSpec]) -> List[DataRequirementSpec]:
        """
        优化下载顺序
        
        Args:
            requirements: 数据需求规格列表
            
        Returns:
            优化后的需求列表
        """
        # 按优先级排序，优先级高的先下载
        sorted_requirements = sorted(requirements, key=lambda x: x.priority)
        
        self.logger.info("优化下载顺序:")
        for i, req in enumerate(sorted_requirements, 1):
            self.logger.info(f"  {i}. {req.stock} (优先级: {req.priority})")
        
        return sorted_requirements
    
    def validate_date_ranges(self, 
                           training_start_date: str, 
                           training_end_date: str) -> Tuple[bool, Optional[str]]:
        """
        验证日期范围的合理性
        
        Args:
            training_start_date: 训练开始日期
            training_end_date: 训练结束日期
            
        Returns:
            (是否有效, 错误信息)
        """
        try:
            start_date = datetime.strptime(training_start_date, '%Y-%m-%d')
            end_date = datetime.strptime(training_end_date, '%Y-%m-%d')
            
            # 检查日期顺序
            if start_date >= end_date:
                return False, "开始日期必须早于结束日期"
            
            # 检查日期范围
            date_range = (end_date - start_date).days
            if date_range < 7:
                return False, f"日期范围太短 ({date_range}天)，建议至少7天"
            
            if date_range > 365 * 2:
                return False, f"日期范围太长 ({date_range}天)，建议不超过2年"
            
            # 检查未来日期
            if end_date > datetime.now():
                return False, "结束日期不能是未来日期"
            
            # 检查历史日期限制
            min_date = datetime(2010, 1, 1)  # Alpha Vantage数据最早到2010年
            if start_date < min_date:
                return False, f"开始日期不能早于 {min_date.strftime('%Y-%m-%d')}"
            
            return True, None
            
        except ValueError as e:
            return False, f"日期格式错误: {e}"
    
    def get_requirement_summary(self, requirements: List[DataRequirementSpec]) -> Dict[str, Any]:
        """获取需求摘要"""
        if not requirements:
            return {"error": "没有数据需求"}
        
        # 计算日期范围
        all_training_starts = [req.training_start_date for req in requirements]
        all_training_ends = [req.training_end_date for req in requirements]
        all_news_starts = [req.news_start_date for req in requirements]
        
        summary = {
            "total_stocks": len(requirements),
            "training_period": {
                "start": min(all_training_starts),
                "end": max(all_training_ends),
                "days": (datetime.strptime(max(all_training_ends), '%Y-%m-%d') - 
                        datetime.strptime(min(all_training_starts), '%Y-%m-%d')).days
            },
            "news_period": {
                "start": min(all_news_starts),
                "end": max(all_training_ends),
                "advance_months": self.news_advance_months
            },
            "api_estimation": self.estimate_api_calls(requirements),
            "stocks": [req.stock for req in requirements]
        }
        
        return summary


if __name__ == "__main__":
    # 测试代码
    import argparse
    import json
    
    parser = argparse.ArgumentParser(description="数据需求分析器")
    parser.add_argument("--stocks", nargs="+", default=["AAPL", "MSFT"], help="股票代码列表")
    parser.add_argument("--start-date", default="2025-01-01", help="训练开始日期")
    parser.add_argument("--end-date", default="2025-03-31", help="训练结束日期")
    parser.add_argument("--news-plan", action="store_true", help="显示新闻下载计划")
    
    args = parser.parse_args()
    
    analyzer = DataRequirementAnalyzer(verbose=True)
    
    # 验证日期范围
    is_valid, error_msg = analyzer.validate_date_ranges(args.start_date, args.end_date)
    if not is_valid:
        print(f"❌ 日期范围验证失败: {error_msg}")
        sys.exit(1)
    
    # 分析需求
    requirements = analyzer.analyze_requirements(args.stocks, args.start_date, args.end_date)
    
    # 显示摘要
    summary = analyzer.get_requirement_summary(requirements)
    print("\n📊 需求摘要:")
    print(json.dumps(summary, indent=2, ensure_ascii=False))
    
    # 显示新闻下载计划
    if args.news_plan:
        print("\n📰 新闻下载计划:")
        for req in requirements:
            plan = analyzer.generate_news_download_plan(req)
            print(f"\n{req.stock}:")
            for i, (start, end) in enumerate(plan.monthly_ranges, 1):
                print(f"  月份 {i}: {start} 到 {end}")
    
    # 优化下载顺序
    optimized = analyzer.optimize_download_order(requirements)
    print(f"\n🎯 优化后的下载顺序: {[req.stock for req in optimized]}")
