#!/usr/bin/env python3
"""
数据集验证和分析脚本

检查构建的数据集的完整性、质量和基本统计信息。
用法: python data/validate_dataset.py [--ticker TICKER] [--detailed]
示例: python data/validate_dataset.py --detailed
"""

import os
import sys
import argparse
import sqlite3
import pandas as pd
import json
from datetime import datetime, date
from typing import Dict, List, Any, Optional

# Add project root to sys.path
current_script_path = os.path.abspath(__file__)
project_root = os.path.dirname(os.path.dirname(current_script_path))
sys.path.append(project_root)

# Import from config
try:
    from config import DATA_DIR
except ImportError:
    print("错误: config.py 未找到或 DATA_DIR 未定义。", file=sys.stderr)
    sys.exit(1)

# 目标股票列表
TARGET_STOCKS = ["NVDA", "AAPL", "MSFT", "META"]

def get_database_path(ticker):
    """根据ticker获取对应的数据库路径"""
    ticker_dir = os.path.join(DATA_DIR, "tickers", ticker.upper())
    return os.path.join(ticker_dir, f"{ticker.upper()}_data.db")

def validate_ohlcv_data(conn: sqlite3.Connection, ticker: str, detailed: bool = False) -> Dict[str, Any]:
    """验证OHLCV数据"""
    try:
        # 基本统计
        query = """
        SELECT 
            COUNT(*) as total_records,
            MIN(trade_date) as earliest_date,
            MAX(trade_date) as latest_date,
            AVG(Volume) as avg_volume,
            MIN(Low) as min_low,
            MAX(High) as max_high
        FROM ohlcv 
        WHERE ticker = ?
        """
        
        df = pd.read_sql_query(query, conn, params=(ticker,))
        result = df.iloc[0].to_dict()
        
        # 数据质量检查
        quality_checks = {}
        
        # 检查缺失值
        missing_query = """
        SELECT 
            SUM(CASE WHEN Open IS NULL THEN 1 ELSE 0 END) as missing_open,
            SUM(CASE WHEN High IS NULL THEN 1 ELSE 0 END) as missing_high,
            SUM(CASE WHEN Low IS NULL THEN 1 ELSE 0 END) as missing_low,
            SUM(CASE WHEN Close IS NULL THEN 1 ELSE 0 END) as missing_close,
            SUM(CASE WHEN Volume IS NULL THEN 1 ELSE 0 END) as missing_volume
        FROM ohlcv 
        WHERE ticker = ?
        """
        
        missing_df = pd.read_sql_query(missing_query, conn, params=(ticker,))
        quality_checks["missing_values"] = missing_df.iloc[0].to_dict()
        
        # 检查异常值（High < Low, Close = 0 等）
        anomaly_query = """
        SELECT 
            SUM(CASE WHEN High < Low THEN 1 ELSE 0 END) as high_less_than_low,
            SUM(CASE WHEN Close <= 0 THEN 1 ELSE 0 END) as zero_or_negative_close,
            SUM(CASE WHEN Volume < 0 THEN 1 ELSE 0 END) as negative_volume
        FROM ohlcv 
        WHERE ticker = ?
        """
        
        anomaly_df = pd.read_sql_query(anomaly_query, conn, params=(ticker,))
        quality_checks["anomalies"] = anomaly_df.iloc[0].to_dict()
        
        result["quality_checks"] = quality_checks
        
        if detailed:
            # 详细统计信息
            detailed_query = """
            SELECT 
                trade_date,
                Open, High, Low, Close, Volume,
                (High - Low) as daily_range,
                (Close - Open) as daily_change,
                ((Close - Open) / Open) * 100 as daily_change_pct
            FROM ohlcv 
            WHERE ticker = ?
            ORDER BY trade_date DESC
            LIMIT 10
            """
            
            recent_df = pd.read_sql_query(detailed_query, conn, params=(ticker,))
            result["recent_data_sample"] = recent_df.to_dict('records')
        
        return result
        
    except Exception as e:
        return {"error": str(e)}

def validate_news_data(conn: sqlite3.Connection, detailed: bool = False) -> Dict[str, Any]:
    """验证新闻数据"""
    try:
        # 基本统计
        query = """
        SELECT 
            COUNT(*) as total_articles,
            MIN(time_published) as earliest_article,
            MAX(time_published) as latest_article,
            COUNT(DISTINCT source) as unique_sources
        FROM news
        """
        
        df = pd.read_sql_query(query, conn)
        result = df.iloc[0].to_dict()
        
        # 检查数据质量
        quality_checks = {}
        
        # 检查缺失的关键字段
        missing_query = """
        SELECT 
            SUM(CASE WHEN title IS NULL OR title = '' THEN 1 ELSE 0 END) as missing_title,
            SUM(CASE WHEN summary IS NULL OR summary = '' THEN 1 ELSE 0 END) as missing_summary,
            SUM(CASE WHEN source IS NULL OR source = '' THEN 1 ELSE 0 END) as missing_source
        FROM news
        """
        
        missing_df = pd.read_sql_query(missing_query, conn)
        quality_checks["missing_values"] = missing_df.iloc[0].to_dict()
        
        # 检查情感分析数据
        sentiment_query = """
        SELECT 
            AVG(overall_sentiment_score) as avg_sentiment_score,
            COUNT(DISTINCT overall_sentiment_label) as unique_sentiment_labels,
            SUM(CASE WHEN overall_sentiment_score IS NULL THEN 1 ELSE 0 END) as missing_sentiment_score
        FROM news
        """
        
        sentiment_df = pd.read_sql_query(sentiment_query, conn)
        quality_checks["sentiment_analysis"] = sentiment_df.iloc[0].to_dict()
        
        result["quality_checks"] = quality_checks
        
        if detailed:
            # 新闻来源分布
            source_query = """
            SELECT source, COUNT(*) as article_count
            FROM news
            GROUP BY source
            ORDER BY article_count DESC
            LIMIT 10
            """
            
            source_df = pd.read_sql_query(source_query, conn)
            result["top_sources"] = source_df.to_dict('records')
            
            # 情感标签分布
            sentiment_label_query = """
            SELECT overall_sentiment_label, COUNT(*) as count
            FROM news
            WHERE overall_sentiment_label IS NOT NULL
            GROUP BY overall_sentiment_label
            ORDER BY count DESC
            """
            
            sentiment_label_df = pd.read_sql_query(sentiment_label_query, conn)
            result["sentiment_distribution"] = sentiment_label_df.to_dict('records')
        
        return result
        
    except Exception as e:
        return {"error": str(e)}

def validate_financial_data(conn: sqlite3.Connection, ticker: str, detailed: bool = False) -> Dict[str, Any]:
    """验证财务数据"""
    result = {}
    
    try:
        # 年度财务数据
        annual_query = """
        SELECT 
            COUNT(*) as total_annual_reports,
            MIN(fiscal_date) as earliest_annual_date,
            MAX(fiscal_date) as latest_annual_date,
            COUNT(DISTINCT report_type) as annual_report_types
        FROM annual_financials 
        WHERE ticker = ?
        """
        
        annual_df = pd.read_sql_query(annual_query, conn, params=(ticker,))
        result["annual"] = annual_df.iloc[0].to_dict()
        
        # 季度财务数据
        quarterly_query = """
        SELECT 
            COUNT(*) as total_quarterly_reports,
            MIN(fiscal_date) as earliest_quarterly_date,
            MAX(fiscal_date) as latest_quarterly_date,
            COUNT(DISTINCT report_type) as quarterly_report_types
        FROM quarterly_financials 
        WHERE ticker = ?
        """
        
        quarterly_df = pd.read_sql_query(quarterly_query, conn, params=(ticker,))
        result["quarterly"] = quarterly_df.iloc[0].to_dict()
        
        if detailed:
            # 报告类型分布
            annual_types_query = """
            SELECT report_type, COUNT(*) as count
            FROM annual_financials
            WHERE ticker = ?
            GROUP BY report_type
            """
            
            annual_types_df = pd.read_sql_query(annual_types_query, conn, params=(ticker,))
            result["annual_report_types"] = annual_types_df.to_dict('records')
            
            quarterly_types_query = """
            SELECT report_type, COUNT(*) as count
            FROM quarterly_financials
            WHERE ticker = ?
            GROUP BY report_type
            """
            
            quarterly_types_df = pd.read_sql_query(quarterly_types_query, conn, params=(ticker,))
            result["quarterly_report_types"] = quarterly_types_df.to_dict('records')
        
        return result
        
    except Exception as e:
        return {"error": str(e)}

def validate_single_ticker(ticker: str, detailed: bool = False) -> Dict[str, Any]:
    """验证单个股票的数据"""
    db_path = get_database_path(ticker)
    
    if not os.path.exists(db_path):
        return {"exists": False, "error": f"数据库文件不存在: {db_path}"}
    
    try:
        conn = sqlite3.connect(db_path)
        
        result = {
            "ticker": ticker,
            "database_path": db_path,
            "exists": True,
            "file_size_mb": round(os.path.getsize(db_path) / (1024 * 1024), 2)
        }
        
        # 验证各类数据
        result["ohlcv"] = validate_ohlcv_data(conn, ticker, detailed)
        result["news"] = validate_news_data(conn, detailed)
        result["financials"] = validate_financial_data(conn, ticker, detailed)
        
        # 计算数据完整性得分
        completeness_score = calculate_completeness_score(result)
        result["completeness_score"] = completeness_score
        
        conn.close()
        return result
        
    except Exception as e:
        return {"exists": True, "error": str(e)}

def calculate_completeness_score(validation_result: Dict[str, Any]) -> Dict[str, Any]:
    """计算数据完整性得分"""
    score = 0
    max_score = 0
    details = {}
    
    # OHLCV数据评分
    if "ohlcv" in validation_result and "total_records" in validation_result["ohlcv"]:
        ohlcv_records = validation_result["ohlcv"]["total_records"]
        if ohlcv_records > 250:  # 一年约250个交易日
            score += 30
            details["ohlcv"] = "优秀"
        elif ohlcv_records > 200:
            score += 25
            details["ohlcv"] = "良好"
        elif ohlcv_records > 100:
            score += 15
            details["ohlcv"] = "一般"
        else:
            details["ohlcv"] = "较差"
    max_score += 30
    
    # 新闻数据评分
    if "news" in validation_result and "total_articles" in validation_result["news"]:
        news_count = validation_result["news"]["total_articles"]
        if news_count > 100:
            score += 25
            details["news"] = "优秀"
        elif news_count > 50:
            score += 20
            details["news"] = "良好"
        elif news_count > 10:
            score += 10
            details["news"] = "一般"
        else:
            details["news"] = "较差"
    max_score += 25
    
    # 财务数据评分
    if "financials" in validation_result:
        annual_count = validation_result["financials"].get("annual", {}).get("total_annual_reports", 0)
        quarterly_count = validation_result["financials"].get("quarterly", {}).get("total_quarterly_reports", 0)
        
        financial_score = 0
        if annual_count >= 3:  # 至少3年年报
            financial_score += 15
        elif annual_count >= 1:
            financial_score += 10
        
        if quarterly_count >= 8:  # 至少8个季度报告
            financial_score += 15
        elif quarterly_count >= 4:
            financial_score += 10
        
        score += financial_score
        if financial_score >= 25:
            details["financials"] = "优秀"
        elif financial_score >= 15:
            details["financials"] = "良好"
        elif financial_score >= 5:
            details["financials"] = "一般"
        else:
            details["financials"] = "较差"
    max_score += 30
    
    # 数据质量扣分
    quality_deductions = 0
    if "ohlcv" in validation_result and "quality_checks" in validation_result["ohlcv"]:
        anomalies = validation_result["ohlcv"]["quality_checks"].get("anomalies", {})
        for anomaly_type, count in anomalies.items():
            if count > 0:
                quality_deductions += min(count * 2, 10)  # 每个异常扣2分，最多扣10分
    
    score = max(0, score - quality_deductions)
    
    return {
        "score": score,
        "max_score": max_score,
        "percentage": round((score / max_score) * 100, 1) if max_score > 0 else 0,
        "details": details,
        "quality_deductions": quality_deductions
    }

def print_validation_report(validation_results: Dict[str, Any], detailed: bool = False):
    """打印验证报告"""
    print("=" * 80)
    print("数据集验证报告")
    print("=" * 80)
    
    if isinstance(validation_results, dict) and "ticker" in validation_results:
        # 单个股票报告
        results = [validation_results]
    else:
        # 多个股票报告
        results = validation_results
    
    for result in results:
        ticker = result.get("ticker", "未知")
        print(f"\n{'='*50}")
        print(f"股票代码: {ticker}")
        print(f"{'='*50}")
        
        if not result.get("exists", False):
            print("❌ 数据库不存在")
            continue
        
        if "error" in result:
            print(f"❌ 验证失败: {result['error']}")
            continue
        
        # 基本信息
        print(f"📁 数据库文件: {result['database_path']}")
        print(f"💾 文件大小: {result['file_size_mb']} MB")
        
        # 完整性评分
        if "completeness_score" in result:
            score_info = result["completeness_score"]
            print(f"📊 完整性评分: {score_info['score']}/{score_info['max_score']} ({score_info['percentage']}%)")
            
            if score_info['percentage'] >= 80:
                print("✅ 数据质量: 优秀")
            elif score_info['percentage'] >= 60:
                print("🟡 数据质量: 良好")
            elif score_info['percentage'] >= 40:
                print("🟠 数据质量: 一般")
            else:
                print("🔴 数据质量: 需要改进")
        
        # OHLCV数据
        if "ohlcv" in result and "total_records" in result["ohlcv"]:
            ohlcv = result["ohlcv"]
            print(f"\n📈 OHLCV数据:")
            print(f"   记录数: {ohlcv['total_records']}")
            print(f"   日期范围: {ohlcv['earliest_date']} 至 {ohlcv['latest_date']}")
            print(f"   平均成交量: {ohlcv['avg_volume']:,.0f}" if ohlcv['avg_volume'] else "   平均成交量: N/A")
            print(f"   价格范围: ${ohlcv['min_low']:.2f} - ${ohlcv['max_high']:.2f}")
            
            if "quality_checks" in ohlcv:
                quality = ohlcv["quality_checks"]
                anomalies = quality.get("anomalies", {})
                total_anomalies = sum(anomalies.values())
                if total_anomalies > 0:
                    print(f"   ⚠️  异常数据: {total_anomalies} 条")
                else:
                    print("   ✅ 数据质量: 正常")
        
        # 新闻数据
        if "news" in result and "total_articles" in result["news"]:
            news = result["news"]
            print(f"\n📰 新闻数据:")
            print(f"   文章数: {news['total_articles']}")
            print(f"   日期范围: {news['earliest_article']} 至 {news['latest_article']}")
            print(f"   新闻源数量: {news['unique_sources']}")
            
            if "quality_checks" in news:
                sentiment = news["quality_checks"].get("sentiment_analysis", {})
                avg_sentiment = sentiment.get("avg_sentiment_score")
                if avg_sentiment is not None:
                    print(f"   平均情感分数: {avg_sentiment:.3f}")
        
        # 财务数据
        if "financials" in result:
            financials = result["financials"]
            print(f"\n💰 财务数据:")
            
            if "annual" in financials:
                annual = financials["annual"]
                print(f"   年度报告: {annual['total_annual_reports']} 份")
                if annual['total_annual_reports'] > 0:
                    print(f"   年度范围: {annual['earliest_annual_date']} 至 {annual['latest_annual_date']}")
            
            if "quarterly" in financials:
                quarterly = financials["quarterly"]
                print(f"   季度报告: {quarterly['total_quarterly_reports']} 份")
                if quarterly['total_quarterly_reports'] > 0:
                    print(f"   季度范围: {quarterly['earliest_quarterly_date']} 至 {quarterly['latest_quarterly_date']}")
        
        # 详细信息
        if detailed:
            print(f"\n📋 详细信息:")
            
            # 最近OHLCV数据样本
            if "ohlcv" in result and "recent_data_sample" in result["ohlcv"]:
                print("   最近10天OHLCV数据:")
                for record in result["ohlcv"]["recent_data_sample"][:5]:
                    print(f"     {record['trade_date']}: 开盘${record['Open']:.2f} 收盘${record['Close']:.2f} 成交量{record['Volume']:,}")
            
            # 新闻源分布
            if "news" in result and "top_sources" in result["news"]:
                print("   主要新闻源:")
                for source in result["news"]["top_sources"][:5]:
                    print(f"     {source['source']}: {source['article_count']} 篇")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="验证数据集质量")
    parser.add_argument("--ticker", type=str, help="指定要验证的股票代码")
    parser.add_argument("--detailed", action="store_true", help="显示详细信息")
    args = parser.parse_args()
    
    if args.ticker:
        # 验证单个股票
        ticker = args.ticker.upper()
        print(f"验证股票 {ticker} 的数据...")
        result = validate_single_ticker(ticker, args.detailed)
        print_validation_report(result, args.detailed)
    else:
        # 验证所有目标股票
        print("验证所有目标股票的数据...")
        results = []
        
        for ticker in TARGET_STOCKS:
            print(f"验证 {ticker}...", end=" ")
            result = validate_single_ticker(ticker, args.detailed)
            results.append(result)
            
            if result.get("exists", False) and "error" not in result:
                score = result.get("completeness_score", {}).get("percentage", 0)
                print(f"完成 (完整性: {score}%)")
            else:
                print("失败")
        
        print_validation_report(results, args.detailed)
        
        # 总体概要
        print(f"\n{'='*80}")
        print("总体概要")
        print(f"{'='*80}")
        
        valid_count = sum(1 for r in results if r.get("exists", False) and "error" not in r)
        avg_score = sum(r.get("completeness_score", {}).get("percentage", 0) for r in results if r.get("exists", False) and "error" not in r) / max(valid_count, 1)
        
        print(f"有效数据库: {valid_count}/{len(TARGET_STOCKS)}")
        print(f"平均完整性: {avg_score:.1f}%")
        
        if avg_score >= 80:
            print("🎉 数据集质量优秀，可以开始进行分析和交易！")
        elif avg_score >= 60:
            print("👍 数据集质量良好，建议补充部分缺失数据后使用。")
        else:
            print("⚠️  数据集质量需要改进，建议重新下载部分数据。")

if __name__ == "__main__":
    main() 