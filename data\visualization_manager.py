#!/usr/bin/env python3
"""
可视化管理器 (Visualization Manager)

负责股价数据收集和可视化功能：
1. 股价数据收集和存储
2. 多种图表类型生成（线图、蜡烛图等）
3. 市场指标和交易量分析
4. 交易决策与价格走势对比
5. 交互式图表和过滤功能

作者: AI Assistant
创建时间: 2025-07-04
"""

import os
import json
import logging
import sqlite3
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.ticker import MaxNLocator
import seaborn as sns
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
import numpy as np

from .comprehensive_storage_manager import ComprehensiveStorageManager

# 设置中文字体和改进的可视化配置
def setup_chinese_fonts():
    """设置中文字体支持"""
    import platform
    import matplotlib.font_manager as fm

    # 根据操作系统选择合适的中文字体
    system = platform.system()

    if system == "Windows":
        # Windows系统字体
        chinese_fonts = ['SimHei', 'Microsoft YaHei', 'SimSun', 'KaiTi']
    elif system == "Darwin":  # macOS
        # macOS系统字体
        chinese_fonts = ['Arial Unicode MS', 'PingFang SC', 'Heiti SC', 'STHeiti']
    else:  # Linux
        # Linux系统字体
        chinese_fonts = ['WenQuanYi Micro Hei', 'DejaVu Sans', 'Liberation Sans']

    # 添加通用后备字体
    chinese_fonts.extend(['DejaVu Sans', 'Arial', 'sans-serif'])

    # 设置matplotlib参数
    plt.rcParams['font.sans-serif'] = chinese_fonts
    plt.rcParams['axes.unicode_minus'] = False

    # 改进的图表样式设置
    plt.rcParams['figure.figsize'] = (12, 8)
    plt.rcParams['figure.dpi'] = 100
    plt.rcParams['savefig.dpi'] = 300
    plt.rcParams['savefig.bbox'] = 'tight'
    plt.rcParams['savefig.pad_inches'] = 0.2

    # 字体大小设置
    plt.rcParams['font.size'] = 10
    plt.rcParams['axes.titlesize'] = 14
    plt.rcParams['axes.labelsize'] = 12
    plt.rcParams['xtick.labelsize'] = 10
    plt.rcParams['ytick.labelsize'] = 10
    plt.rcParams['legend.fontsize'] = 10

    # 布局设置
    plt.rcParams['figure.autolayout'] = True
    plt.rcParams['axes.grid'] = True
    plt.rcParams['axes.grid.alpha'] = 0.3

    # 日期格式设置
    plt.rcParams['date.autoformatter.day'] = '%m-%d'
    plt.rcParams['date.autoformatter.month'] = '%Y-%m'

    return chinese_fonts[0] if chinese_fonts else 'DejaVu Sans'

# 初始化字体设置
primary_font = setup_chinese_fonts()
sns.set_style("whitegrid")
sns.set_palette("husl")

@dataclass
class VisualizationConfig:
    """改进的可视化配置"""
    chart_width: int = 1400
    chart_height: int = 900
    color_scheme: str = "default"
    save_format: str = "png"
    interactive_charts: bool = True
    include_volume: bool = True
    include_indicators: bool = True

    # 字体配置
    title_fontsize: int = 16
    label_fontsize: int = 12
    tick_fontsize: int = 10
    legend_fontsize: int = 10

    # 布局配置
    figure_dpi: int = 100
    save_dpi: int = 300
    pad_inches: float = 0.3
    tight_layout: bool = True

    # 日期轴配置
    date_rotation: int = 45
    date_format: str = '%Y-%m-%d'
    max_date_ticks: int = 10

    # 颜色配置
    grid_alpha: float = 0.3
    line_alpha: float = 0.8
    bar_alpha: float = 0.6

    # 间距配置
    subplot_hspace: float = 0.3
    subplot_wspace: float = 0.2
    margin_left: float = 0.1
    margin_right: float = 0.95
    margin_top: float = 0.9
    margin_bottom: float = 0.15

@dataclass
class StockPriceData:
    """股价数据结构"""
    symbol: str
    date: str
    open_price: float
    high_price: float
    low_price: float
    close_price: float
    volume: int
    adj_close: float
    metadata: Dict[str, Any]

class VisualizationManager:
    """
    可视化管理器
    
    提供全面的股价数据可视化功能
    """
    
    def __init__(self, 
                 storage_manager: ComprehensiveStorageManager,
                 config: Optional[VisualizationConfig] = None,
                 logger: Optional[logging.Logger] = None):
        """
        初始化可视化管理器
        
        参数:
            storage_manager: 存储管理器实例
            config: 可视化配置
            logger: 日志记录器
        """
        self.storage_manager = storage_manager
        self.config = config or VisualizationConfig()
        self.logger = logger or self._create_default_logger()
        
        # 数据缓存
        self.price_data_cache = {}
        self.trading_decisions_cache = {}
        
        self.logger.info("可视化管理器初始化完成")
    
    def _create_default_logger(self) -> logging.Logger:
        """创建默认日志记录器"""
        logger = logging.getLogger(f"{__name__}.VisualizationManager")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def collect_stock_price_data(self, 
                               symbol: str, 
                               start_date: str, 
                               end_date: str) -> List[StockPriceData]:
        """
        收集股价数据
        
        参数:
            symbol: 股票代码
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            
        返回:
            股价数据列表
        """
        try:
            # 从现有数据库获取股价数据
            project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            db_path = os.path.join(project_root, "data", "tickers", symbol, f"{symbol}_data.db")
            
            if not os.path.exists(db_path):
                self.logger.warning(f"股票数据库不存在: {db_path}")
                return []
            
            price_data = []
            
            with sqlite3.connect(db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT ticker, trade_date, Open, High, Low, Close, Adj_Close, Volume
                    FROM ohlcv 
                    WHERE ticker = ? AND trade_date BETWEEN ? AND ?
                    ORDER BY trade_date ASC
                """, (symbol, start_date, end_date))
                
                rows = cursor.fetchall()
                
                for row in rows:
                    price_data.append(StockPriceData(
                        symbol=row[0],
                        date=row[1],
                        open_price=float(row[2]),
                        high_price=float(row[3]),
                        low_price=float(row[4]),
                        close_price=float(row[5]),
                        adj_close=float(row[6]),
                        volume=int(row[7]),
                        metadata={"source": "database"}
                    ))
            
            # 缓存数据
            cache_key = f"{symbol}_{start_date}_{end_date}"
            self.price_data_cache[cache_key] = price_data
            
            self.logger.info(f"收集股价数据完成: {symbol} ({len(price_data)} 条记录)")
            return price_data
            
        except Exception as e:
            self.logger.error(f"收集股价数据失败: {e}")
            return []
    
    def generate_price_chart(self, 
                           symbol: str, 
                           start_date: str, 
                           end_date: str,
                           chart_type: str = "line") -> Dict[str, Any]:
        """
        生成价格图表
        
        参数:
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            chart_type: 图表类型 ("line", "candlestick", "ohlc")
            
        返回:
            图表生成结果
        """
        try:
            # 获取价格数据
            price_data = self.collect_stock_price_data(symbol, start_date, end_date)
            
            if not price_data:
                return {"success": False, "error": "无价格数据"}
            
            # 转换为DataFrame
            df = pd.DataFrame([asdict(data) for data in price_data])
            df['date'] = pd.to_datetime(df['date'])
            df = df.sort_values('date')
            
            # 生成图表
            if chart_type == "line":
                chart_path = self._generate_line_chart(df, symbol)
            elif chart_type == "candlestick":
                chart_path = self._generate_candlestick_chart(df, symbol)
            elif chart_type == "ohlc":
                chart_path = self._generate_ohlc_chart(df, symbol)
            else:
                return {"success": False, "error": f"不支持的图表类型: {chart_type}"}
            
            # 保存可视化数据到存储管理器
            self._store_visualization_data(symbol, chart_type, df, chart_path)
            
            return {
                "success": True,
                "chart_path": chart_path,
                "chart_type": chart_type,
                "data_points": len(price_data),
                "date_range": f"{start_date} to {end_date}"
            }
            
        except Exception as e:
            self.logger.error(f"生成价格图表失败: {e}")
            return {"success": False, "error": str(e)}
    
    def _generate_line_chart(self, df: pd.DataFrame, symbol: str) -> str:
        """生成改进的线图"""
        # 创建图表，使用改进的配置
        fig, (ax1, ax2) = plt.subplots(
            2, 1,
            figsize=(self.config.chart_width/100, self.config.chart_height/100),
            gridspec_kw={'height_ratios': [3, 1], 'hspace': self.config.subplot_hspace}
        )

        # 设置整体布局
        fig.subplots_adjust(
            left=self.config.margin_left,
            right=self.config.margin_right,
            top=self.config.margin_top,
            bottom=self.config.margin_bottom
        )

        # 价格线图
        ax1.plot(df['date'], df['close_price'], label='收盘价',
                linewidth=2, alpha=self.config.line_alpha)
        ax1.plot(df['date'], df['adj_close'], label='调整收盘价',
                linewidth=1, alpha=self.config.line_alpha * 0.8)

        ax1.set_title(f'{symbol} 股价走势',
                     fontsize=self.config.title_fontsize, fontweight='bold')
        ax1.set_ylabel('价格 ($)', fontsize=self.config.label_fontsize)
        ax1.legend(fontsize=self.config.legend_fontsize)
        ax1.grid(True, alpha=self.config.grid_alpha)

        # 成交量图
        ax2.bar(df['date'], df['volume'],
               alpha=self.config.bar_alpha, color='orange')
        ax2.set_ylabel('成交量', fontsize=self.config.label_fontsize)
        ax2.set_xlabel('日期', fontsize=self.config.label_fontsize)

        # 改进的日期轴格式化
        self._format_date_axis(ax1, df['date'])
        self._format_date_axis(ax2, df['date'])

        # 使用改进的布局
        if self.config.tight_layout:
            plt.tight_layout(pad=self.config.pad_inches)

        # 保存图表
        chart_path = os.path.join(
            self.storage_manager.config.visualizations_path,
            f"{symbol}_line_chart_{datetime.now().strftime('%Y%m%d_%H%M%S')}.{self.config.save_format}"
        )
        os.makedirs(os.path.dirname(chart_path), exist_ok=True)

        plt.savefig(chart_path,
                   dpi=self.config.save_dpi,
                   bbox_inches='tight',
                   pad_inches=self.config.pad_inches,
                   facecolor='white',
                   edgecolor='none')
        plt.close()

        return chart_path

    def _format_date_axis(self, ax, dates):
        """改进的日期轴格式化"""
        # 设置日期格式
        ax.xaxis.set_major_formatter(mdates.DateFormatter(self.config.date_format))

        # 智能设置日期标签数量
        if len(dates) > self.config.max_date_ticks:
            # 如果数据点太多，减少标签数量
            ax.xaxis.set_major_locator(MaxNLocator(nbins=self.config.max_date_ticks))

        # 设置标签旋转和字体大小
        plt.setp(ax.xaxis.get_majorticklabels(),
                rotation=self.config.date_rotation,
                fontsize=self.config.tick_fontsize,
                ha='right')  # 右对齐避免重叠

        # 设置y轴标签字体大小
        plt.setp(ax.yaxis.get_majorticklabels(),
                fontsize=self.config.tick_fontsize)
    
    def _generate_candlestick_chart(self, df: pd.DataFrame, symbol: str) -> str:
        """生成蜡烛图"""
        if not self.config.interactive_charts:
            return self._generate_static_candlestick(df, symbol)
        
        # 使用Plotly生成交互式蜡烛图
        fig = make_subplots(
            rows=2, cols=1,
            shared_xaxes=True,
            vertical_spacing=0.03,
            subplot_titles=(f'{symbol} 股价', '成交量'),
            row_width=[0.2, 0.7]
        )
        
        # 蜡烛图
        fig.add_trace(
            go.Candlestick(
                x=df['date'],
                open=df['open_price'],
                high=df['high_price'],
                low=df['low_price'],
                close=df['close_price'],
                name="价格"
            ),
            row=1, col=1
        )
        
        # 成交量图
        fig.add_trace(
            go.Bar(
                x=df['date'],
                y=df['volume'],
                name="成交量",
                marker_color='orange',
                opacity=0.6
            ),
            row=2, col=1
        )
        
        # 更新布局
        fig.update_layout(
            title=f'{symbol} 蜡烛图分析',
            yaxis_title='价格 ($)',
            xaxis_rangeslider_visible=False,
            height=self.config.chart_height,
            width=self.config.chart_width
        )
        
        # 保存图表
        chart_path = os.path.join(
            self.storage_manager.config.visualizations_path,
            f"{symbol}_candlestick_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
        )
        os.makedirs(os.path.dirname(chart_path), exist_ok=True)
        fig.write_html(chart_path)
        
        return chart_path
    
    def _generate_static_candlestick(self, df: pd.DataFrame, symbol: str) -> str:
        """生成静态蜡烛图"""
        from matplotlib.patches import Rectangle
        
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(self.config.chart_width/100, self.config.chart_height/100),
                                      gridspec_kw={'height_ratios': [3, 1]})
        
        # 绘制蜡烛图
        for i, row in df.iterrows():
            date = mdates.date2num(row['date'])
            open_price = row['open_price']
            high_price = row['high_price']
            low_price = row['low_price']
            close_price = row['close_price']
            
            # 确定颜色
            color = 'red' if close_price >= open_price else 'green'
            
            # 绘制影线
            ax1.plot([date, date], [low_price, high_price], color='black', linewidth=1)
            
            # 绘制实体
            height = abs(close_price - open_price)
            bottom = min(open_price, close_price)
            rect = Rectangle((date - 0.3, bottom), 0.6, height, 
                           facecolor=color, alpha=0.7, edgecolor='black')
            ax1.add_patch(rect)
        
        ax1.set_title(f'{symbol} 蜡烛图', fontsize=16, fontweight='bold')
        ax1.set_ylabel('价格 ($)', fontsize=12)
        ax1.grid(True, alpha=0.3)
        
        # 成交量图
        ax2.bar(df['date'], df['volume'], alpha=0.6, color='orange')
        ax2.set_ylabel('成交量', fontsize=12)
        ax2.set_xlabel('日期', fontsize=12)
        
        # 格式化日期轴
        ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        ax2.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        plt.setp(ax1.xaxis.get_majorticklabels(), rotation=45)
        plt.setp(ax2.xaxis.get_majorticklabels(), rotation=45)
        
        plt.tight_layout()
        
        # 保存图表
        chart_path = os.path.join(
            self.storage_manager.config.visualizations_path,
            f"{symbol}_candlestick_{datetime.now().strftime('%Y%m%d_%H%M%S')}.{self.config.save_format}"
        )
        os.makedirs(os.path.dirname(chart_path), exist_ok=True)
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        return chart_path
    
    def _generate_ohlc_chart(self, df: pd.DataFrame, symbol: str) -> str:
        """生成OHLC图"""
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(self.config.chart_width/100, self.config.chart_height/100),
                                      gridspec_kw={'height_ratios': [3, 1]})
        
        # OHLC图
        for i, row in df.iterrows():
            date = mdates.date2num(row['date'])
            open_price = row['open_price']
            high_price = row['high_price']
            low_price = row['low_price']
            close_price = row['close_price']
            
            # 绘制高低线
            ax1.plot([date, date], [low_price, high_price], color='black', linewidth=1)
            
            # 绘制开盘价标记
            ax1.plot([date - 0.2, date], [open_price, open_price], color='blue', linewidth=2)
            
            # 绘制收盘价标记
            ax1.plot([date, date + 0.2], [close_price, close_price], color='red', linewidth=2)
        
        ax1.set_title(f'{symbol} OHLC图', fontsize=16, fontweight='bold')
        ax1.set_ylabel('价格 ($)', fontsize=12)
        ax1.grid(True, alpha=0.3)
        
        # 成交量图
        ax2.bar(df['date'], df['volume'], alpha=0.6, color='orange')
        ax2.set_ylabel('成交量', fontsize=12)
        ax2.set_xlabel('日期', fontsize=12)
        
        # 格式化日期轴
        ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        ax2.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        plt.setp(ax1.xaxis.get_majorticklabels(), rotation=45)
        plt.setp(ax2.xaxis.get_majorticklabels(), rotation=45)
        
        plt.tight_layout()
        
        # 保存图表
        chart_path = os.path.join(
            self.storage_manager.config.visualizations_path,
            f"{symbol}_ohlc_{datetime.now().strftime('%Y%m%d_%H%M%S')}.{self.config.save_format}"
        )
        os.makedirs(os.path.dirname(chart_path), exist_ok=True)
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        return chart_path
    
    def _store_visualization_data(self, symbol: str, chart_type: str, df: pd.DataFrame, chart_path: str):
        """存储可视化数据到数据库"""
        try:
            # 计算基本指标
            price_data = {
                "symbol": symbol,
                "start_date": df['date'].min().isoformat(),
                "end_date": df['date'].max().isoformat(),
                "data_points": len(df),
                "price_range": {
                    "min": float(df['low_price'].min()),
                    "max": float(df['high_price'].max()),
                    "avg": float(df['close_price'].mean())
                },
                "volume_stats": {
                    "total": int(df['volume'].sum()),
                    "avg": float(df['volume'].mean()),
                    "max": int(df['volume'].max())
                }
            }
            
            indicators = {
                "daily_returns": df['close_price'].pct_change().dropna().tolist(),
                "volatility": float(df['close_price'].pct_change().std()),
                "price_trend": "上涨" if df['close_price'].iloc[-1] > df['close_price'].iloc[0] else "下跌"
            }
            
            chart_config = {
                "chart_type": chart_type,
                "width": self.config.chart_width,
                "height": self.config.chart_height,
                "interactive": self.config.interactive_charts
            }
            
            # 存储到数据库（通过存储管理器的数据库连接）
            with self.storage_manager._get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO visualization_data 
                    (data_id, data_type, timestamp, stock_symbol, price_data, 
                     indicators, chart_config, file_path)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    f"{symbol}_{chart_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    chart_type,
                    datetime.now().isoformat(),
                    symbol,
                    json.dumps(price_data, ensure_ascii=False),
                    json.dumps(indicators, ensure_ascii=False),
                    json.dumps(chart_config, ensure_ascii=False),
                    chart_path
                ))
                conn.commit()
            
            self.logger.debug(f"可视化数据已存储: {symbol} {chart_type}")
            
        except Exception as e:
            self.logger.error(f"存储可视化数据失败: {e}")

    def generate_trading_decision_comparison(self,
                                           symbol: str,
                                           start_date: str,
                                           end_date: str) -> Dict[str, Any]:
        """
        生成交易决策与价格走势对比图

        参数:
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期

        返回:
            对比图生成结果
        """
        try:
            # 获取价格数据
            price_data = self.collect_stock_price_data(symbol, start_date, end_date)
            if not price_data:
                return {"success": False, "error": "无价格数据"}

            # 获取交易决策数据
            trading_sessions = self.storage_manager.get_trading_sessions(start_date, end_date)

            # 转换价格数据为DataFrame
            price_df = pd.DataFrame([asdict(data) for data in price_data])
            price_df['date'] = pd.to_datetime(price_df['date'])
            price_df = price_df.sort_values('date')

            # 处理交易决策数据
            decision_points = []
            for session in trading_sessions:
                session_date = session.get('timestamp', '')[:10]  # 提取日期部分
                trading_decisions = session.get('trading_decisions', {})

                for agent_id, decisions in trading_decisions.items():
                    if isinstance(decisions, dict):
                        decision_points.append({
                            'date': session_date,
                            'agent_id': agent_id,
                            'decision_type': decisions.get('participation_decision', 'unknown'),
                            'confidence': decisions.get('contribution_level', 0.0)
                        })

            # 生成对比图
            chart_path = self._generate_decision_comparison_chart(price_df, decision_points, symbol)

            return {
                "success": True,
                "chart_path": chart_path,
                "price_points": len(price_data),
                "decision_points": len(decision_points),
                "date_range": f"{start_date} to {end_date}"
            }

        except Exception as e:
            self.logger.error(f"生成交易决策对比图失败: {e}")
            return {"success": False, "error": str(e)}

    def _generate_decision_comparison_chart(self, price_df: pd.DataFrame, decision_points: List[Dict], symbol: str) -> str:
        """生成交易决策对比图"""
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(self.config.chart_width/100, self.config.chart_height/100),
                                      gridspec_kw={'height_ratios': [2, 1]})

        # 价格走势图
        ax1.plot(price_df['date'], price_df['close_price'], label='收盘价', linewidth=2, color='blue')

        # 添加交易决策点
        decision_df = pd.DataFrame(decision_points)
        if not decision_df.empty:
            decision_df['date'] = pd.to_datetime(decision_df['date'])

            # 按决策类型分组
            for decision_type in decision_df['decision_type'].unique():
                subset = decision_df[decision_df['decision_type'] == decision_type]

                # 为每个决策点找到对应的价格
                for _, row in subset.iterrows():
                    closest_price_idx = price_df['date'].sub(row['date']).abs().idxmin()
                    if closest_price_idx in price_df.index:
                        price = price_df.loc[closest_price_idx, 'close_price']

                        marker = '^' if decision_type == 'participate' else 'v'
                        color = 'green' if decision_type == 'participate' else 'red'

                        ax1.scatter(row['date'], price, marker=marker, color=color,
                                  s=100, alpha=0.7, label=f'{decision_type}' if decision_type not in ax1.get_legend_handles_labels()[1] else "")

        ax1.set_title(f'{symbol} 价格走势与交易决策对比', fontsize=16, fontweight='bold')
        ax1.set_ylabel('价格 ($)', fontsize=12)
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 决策统计图
        if not decision_df.empty:
            decision_counts = decision_df.groupby(['date', 'decision_type']).size().unstack(fill_value=0)
            decision_counts.plot(kind='bar', ax=ax2, alpha=0.7)
            ax2.set_title('每日交易决策统计', fontsize=12)
            ax2.set_ylabel('决策数量', fontsize=10)
            ax2.set_xlabel('日期', fontsize=10)
            plt.setp(ax2.xaxis.get_majorticklabels(), rotation=45)

        plt.tight_layout()

        # 保存图表
        chart_path = os.path.join(
            self.storage_manager.config.visualizations_path,
            f"{symbol}_decision_comparison_{datetime.now().strftime('%Y%m%d_%H%M%S')}.{self.config.save_format}"
        )
        os.makedirs(os.path.dirname(chart_path), exist_ok=True)
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        plt.close()

        return chart_path

    def generate_market_indicators_analysis(self,
                                          symbol: str,
                                          start_date: str,
                                          end_date: str) -> Dict[str, Any]:
        """
        生成市场指标分析图

        参数:
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期

        返回:
            分析图生成结果
        """
        try:
            # 获取价格数据
            price_data = self.collect_stock_price_data(symbol, start_date, end_date)
            if not price_data:
                return {"success": False, "error": "无价格数据"}

            # 转换为DataFrame并计算指标
            df = pd.DataFrame([asdict(data) for data in price_data])
            df['date'] = pd.to_datetime(df['date'])
            df = df.sort_values('date')

            # 计算技术指标
            df = self._calculate_technical_indicators(df)

            # 生成分析图
            chart_path = self._generate_indicators_chart(df, symbol)

            # 生成分析报告
            analysis_report = self._generate_market_analysis_report(df, symbol)

            return {
                "success": True,
                "chart_path": chart_path,
                "analysis_report": analysis_report,
                "data_points": len(price_data),
                "indicators_calculated": ["SMA_20", "SMA_50", "RSI", "MACD", "Bollinger_Bands"]
            }

        except Exception as e:
            self.logger.error(f"生成市场指标分析失败: {e}")
            return {"success": False, "error": str(e)}

    def _calculate_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算技术指标"""
        # 简单移动平均线
        df['SMA_20'] = df['close_price'].rolling(window=20).mean()
        df['SMA_50'] = df['close_price'].rolling(window=50).mean()

        # RSI
        delta = df['close_price'].diff()
        gain = (delta.where(delta > 0, 0.0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0.0)).rolling(window=14).mean()
        rs = gain / loss
        df['RSI'] = 100 - (100 / (1 + rs))

        # MACD
        exp1 = df['close_price'].ewm(span=12).mean()
        exp2 = df['close_price'].ewm(span=26).mean()
        df['MACD'] = exp1 - exp2
        df['MACD_signal'] = df['MACD'].ewm(span=9).mean()

        # 布林带
        df['BB_middle'] = df['close_price'].rolling(window=20).mean()
        bb_std = df['close_price'].rolling(window=20).std()
        df['BB_upper'] = df['BB_middle'] + (bb_std * 2)
        df['BB_lower'] = df['BB_middle'] - (bb_std * 2)

        # 日收益率
        df['daily_return'] = df['close_price'].pct_change()

        # 波动率（20日滚动标准差）
        df['volatility'] = df['daily_return'].rolling(window=20).std()

        return df

    def _generate_indicators_chart(self, df: pd.DataFrame, symbol: str) -> str:
        """生成技术指标图表"""
        fig, axes = plt.subplots(4, 1, figsize=(self.config.chart_width/100, self.config.chart_height/100))

        # 价格和移动平均线
        axes[0].plot(df['date'], df['close_price'], label='收盘价', linewidth=2)
        axes[0].plot(df['date'], df['SMA_20'], label='SMA 20', alpha=0.7)
        axes[0].plot(df['date'], df['SMA_50'], label='SMA 50', alpha=0.7)
        axes[0].fill_between(df['date'], df['BB_upper'], df['BB_lower'], alpha=0.2, label='布林带')
        axes[0].set_title(f'{symbol} 价格与移动平均线', fontweight='bold')
        axes[0].set_ylabel('价格 ($)')
        axes[0].legend()
        axes[0].grid(True, alpha=0.3)

        # RSI
        axes[1].plot(df['date'], df['RSI'], color='purple', linewidth=2)
        axes[1].axhline(y=70, color='r', linestyle='--', alpha=0.7, label='超买线')
        axes[1].axhline(y=30, color='g', linestyle='--', alpha=0.7, label='超卖线')
        axes[1].set_title('RSI 相对强弱指数')
        axes[1].set_ylabel('RSI')
        axes[1].set_ylim(0, 100)
        axes[1].legend()
        axes[1].grid(True, alpha=0.3)

        # MACD
        axes[2].plot(df['date'], df['MACD'], label='MACD', linewidth=2)
        axes[2].plot(df['date'], df['MACD_signal'], label='信号线', alpha=0.7)
        axes[2].bar(df['date'], df['MACD'] - df['MACD_signal'], alpha=0.3, label='MACD柱')
        axes[2].set_title('MACD 指标')
        axes[2].set_ylabel('MACD')
        axes[2].legend()
        axes[2].grid(True, alpha=0.3)

        # 成交量和波动率
        ax3_twin = axes[3].twinx()
        axes[3].bar(df['date'], df['volume'], alpha=0.6, color='orange', label='成交量')
        ax3_twin.plot(df['date'], df['volatility'], color='red', linewidth=2, label='波动率')
        axes[3].set_title('成交量与波动率')
        axes[3].set_ylabel('成交量')
        ax3_twin.set_ylabel('波动率')
        axes[3].set_xlabel('日期')

        # 格式化日期轴
        for ax in axes:
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)

        plt.tight_layout()

        # 保存图表
        chart_path = os.path.join(
            self.storage_manager.config.visualizations_path,
            f"{symbol}_indicators_{datetime.now().strftime('%Y%m%d_%H%M%S')}.{self.config.save_format}"
        )
        os.makedirs(os.path.dirname(chart_path), exist_ok=True)
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        plt.close()

        return chart_path

    def _generate_market_analysis_report(self, df: pd.DataFrame, symbol: str) -> Dict[str, Any]:
        """生成市场分析报告"""
        latest_data = df.iloc[-1]

        # 价格分析
        price_change = (latest_data['close_price'] - df.iloc[0]['close_price']) / df.iloc[0]['close_price'] * 100

        # 技术指标分析
        rsi_signal = "超买" if latest_data['RSI'] > 70 else "超卖" if latest_data['RSI'] < 30 else "中性"
        macd_signal = "看涨" if latest_data['MACD'] > latest_data['MACD_signal'] else "看跌"

        # 移动平均线分析
        sma_trend = "上升趋势" if latest_data['close_price'] > latest_data['SMA_20'] > latest_data['SMA_50'] else "下降趋势"

        # 波动率分析
        avg_volatility = df['volatility'].mean()
        current_volatility = latest_data['volatility']
        volatility_status = "高" if current_volatility > avg_volatility * 1.2 else "低" if current_volatility < avg_volatility * 0.8 else "正常"

        return {
            "symbol": symbol,
            "analysis_date": latest_data['date'].isoformat(),
            "price_analysis": {
                "current_price": float(latest_data['close_price']),
                "period_change_percent": float(price_change),
                "trend": "上涨" if price_change > 0 else "下跌"
            },
            "technical_indicators": {
                "rsi": {
                    "value": float(latest_data['RSI']),
                    "signal": rsi_signal
                },
                "macd": {
                    "value": float(latest_data['MACD']),
                    "signal": macd_signal
                },
                "moving_averages": {
                    "sma_20": float(latest_data['SMA_20']),
                    "sma_50": float(latest_data['SMA_50']),
                    "trend": sma_trend
                }
            },
            "risk_analysis": {
                "current_volatility": float(current_volatility),
                "average_volatility": float(avg_volatility),
                "volatility_status": volatility_status
            },
            "summary": f"{symbol} 当前处于{sma_trend}，RSI显示{rsi_signal}，MACD信号{macd_signal}，波动率{volatility_status}。"
        }
