"""
OPRO优化引擎 (OPRO Optimizer Engine)

本模块实现了OPRO（Optimization by PROmpting）方法，用于优化智能体的提示词。
通过分析历史Shapley值得分，生成更优的提示词候选，并评估其性能。

主要功能：
1. 元提示词生成：基于历史得分创建OPRO元提示词
2. 候选生成：生成多个优化候选提示词
3. 性能评估：评估候选提示词的潜在性能
4. 优化循环：管理完整的OPRO优化流程
"""

import json
import logging
import hashlib
import time
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import concurrent.futures
import threading
import numpy as np

class OPROOptimizer:
    """
    OPRO提示词优化器
    
    实现基于历史Shapley值的智能体提示词优化，使用LLM作为优化器
    生成更好的提示词候选来提升智能体的贡献度。
    """
    
    def __init__(self, 
                 llm_interface,
                 historical_score_manager,
                 logger: Optional[logging.Logger] = None,
                 config: Optional[Dict[str, Any]] = None):
        """
        初始化OPRO优化器
        
        参数:
            llm_interface: LLM接口实例
            historical_score_manager: 历史得分管理器实例
            logger: 日志记录器
            config: 优化器配置
        """
        self.llm_interface = llm_interface
        self.score_manager = historical_score_manager
        self.logger = logger or self._create_default_logger()
        
        # 合并默认配置和用户配置
        default_config = self._get_default_config()
        if config:
            default_config.update(config)
        self.config = default_config
        
        # 优化统计信息
        self._stats = {
            "total_optimizations": 0,
            "successful_optimizations": 0,
            "failed_optimizations": 0,
            "total_candidates_generated": 0,
            "total_candidates_evaluated": 0,
            "best_improvements": {},  # {agent_id: best_improvement}
            "optimization_history": []
        }
        
        # 缓存机制
        self._prompt_cache = {}
        self._evaluation_cache = {}
        self._cache_lock = threading.Lock()
        
        # 元提示词模板
        self.meta_prompt_template = self._load_meta_prompt_template()
        
        self.logger.info("OPRO优化器初始化完成")
    
    def _create_default_logger(self) -> logging.Logger:
        """创建默认日志记录器"""
        logger = logging.getLogger(f"{__name__}.OPROOptimizer")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "candidates_per_generation": 8,
            "historical_weeks_to_consider": 10,
            "temperature": 1.0,
            "max_optimization_iterations": 50,
            "convergence_threshold": 0.001,
            "prompt_length_limit": 500,
            "evaluation_timeout": 300,  # 5分钟超时
            "enable_cache": True,
            "cache_ttl": 3600,  # 缓存1小时
            "parallel_evaluation": True,
            "max_workers": 4
        }
    
    def _load_meta_prompt_template(self) -> str:
        """加载元提示词模板"""
        return """你是一个专业的提示词优化专家，专门为金融交易智能体优化提示词以提升其Shapley贡献度。

当前优化目标：{agent_type} ({agent_id})
智能体职责：{agent_role_description}

历史提示词与Shapley得分（按得分从低到高排序）：
{historical_prompts_and_scores}

性能分析：
- 当前最佳得分：{best_score:.6f}
- 最差得分：{worst_score:.6f}
- 平均得分：{avg_score:.6f}
- 得分趋势：{score_trend}

优化要求：
1. 设计一个新的提示词，必须与上述所有历史提示词显著不同
2. 专注于提升该智能体在多智能体协作中的核心价值贡献
3. 考虑与其他智能体（{other_agents}）的协作关系
4. 提示词应包含角色定义、任务描述、输出要求
5. 长度控制在{prompt_length_limit}字符以内
6. 使用清晰、专业的金融分析语言

目标：最大化Shapley贡献度（目标分数 > {target_score:.6f}）

请输出一个新的提示词，格式如下：
[新的提示词内容]

注意：只输出提示词内容，不要添加额外说明。"""

    def optimize_agent_prompt(self, 
                            agent_id: str, 
                            current_prompt: str,
                            k: Optional[int] = None) -> Dict[str, Any]:
        """
        为指定智能体优化提示词
        
        参数:
            agent_id: 智能体ID
            current_prompt: 当前提示词
            k: 生成候选数量，如果为None则使用配置值
            
        返回:
            优化结果字典
        """
        start_time = time.time()
        k = k or self.config["candidates_per_generation"]
        
        self.logger.info(f"开始为智能体 {agent_id} 优化提示词")
        self.logger.info(f"生成 {k} 个候选提示词...")
        
        try:
            # 1. 获取历史得分数据
            historical_data = self.score_manager.get_agent_optimization_history(
                agent_id, 
                weeks=self.config["historical_weeks_to_consider"]
            )
            
            if not historical_data or len(historical_data) < 2:
                self.logger.warning(f"智能体 {agent_id} 历史数据不足，无法进行优化")
                return self._create_failed_result("历史数据不足", start_time)
            
            # 2. 创建元提示词
            meta_prompt = self._create_meta_prompt(agent_id, historical_data)
            
            # 3. 生成候选提示词
            candidates = self._generate_prompt_candidates(
                meta_prompt, k, temperature=self.config["temperature"]
            )
            
            if not candidates:
                return self._create_failed_result("候选生成失败", start_time)
            
            # 4. 快速评估候选提示词
            evaluation_results = self._evaluate_candidates_batch(
                agent_id, candidates
            )
            
            # 5. 选择最佳候选
            best_candidate = self._select_best_candidate(
                candidates, evaluation_results, historical_data
            )
            
            # 6. 更新统计信息
            optimization_time = time.time() - start_time
            self._update_optimization_stats(agent_id, best_candidate, optimization_time)
            
            self.logger.info(f"智能体 {agent_id} 优化完成，"
                           f"最佳候选预期得分: {best_candidate['estimated_score']:.6f}")
            
            return {
                "success": True,
                "agent_id": agent_id,
                "original_prompt": current_prompt,
                "optimized_prompt": best_candidate["prompt"],
                "estimated_score": best_candidate["estimated_score"],
                "improvement": best_candidate["estimated_improvement"],
                "candidates_generated": len(candidates),
                "candidates_evaluated": len(evaluation_results),
                "optimization_time": optimization_time,
                "meta_prompt": meta_prompt,
                "evaluation_details": evaluation_results
            }
            
        except Exception as e:
            self.logger.error(f"优化智能体 {agent_id} 失败: {e}")
            return self._create_failed_result(str(e), start_time)
    
    def _create_meta_prompt(self, agent_id: str, historical_data: List[Dict]) -> str:
        """创建元提示词"""
        
        # 获取智能体角色描述
        agent_info = self._get_agent_info(agent_id)
        
        # 按得分排序历史数据
        sorted_data = sorted(historical_data, key=lambda x: x.get("score", 0))
        
        # 格式化历史提示词和得分
        formatted_history = []
        for i, data in enumerate(sorted_data):
            prompt = data.get("prompt", "").strip()
            if len(prompt) > 100:
                prompt = prompt[:100] + "..."
            score = data.get("score", 0)
            formatted_history.append(f"[提示词 {i+1}] {prompt} 得分: {score:.6f}")
        
        # 计算统计信息
        scores = [data.get("score", 0) for data in historical_data]
        best_score = max(scores)
        worst_score = min(scores)
        avg_score = np.mean(scores)
        
        # 分析得分趋势
        if len(scores) >= 3:
            recent_avg = np.mean(scores[-3:])
            earlier_avg = np.mean(scores[:-3]) if len(scores) > 3 else avg_score
            if recent_avg > earlier_avg * 1.05:
                trend = "上升趋势"
            elif recent_avg < earlier_avg * 0.95:
                trend = "下降趋势"
            else:
                trend = "稳定"
        else:
            trend = "数据不足"
        
        # 设置目标得分
        target_score = best_score * 1.1  # 目标是当前最佳得分的110%
        
        # 获取其他智能体列表
        other_agents = ["NAA", "TAA", "FAA", "BOA", "BeOA", "NOA", "TRA"]
        if agent_id in other_agents:
            other_agents.remove(agent_id)
        
        # 填充模板
        return self.meta_prompt_template.format(
            agent_type=agent_info["type"],
            agent_id=agent_id,
            agent_role_description=agent_info["role_description"],
            historical_prompts_and_scores="\n".join(formatted_history),
            best_score=best_score,
            worst_score=worst_score,
            avg_score=avg_score,
            score_trend=trend,
            other_agents=", ".join(other_agents),
            prompt_length_limit=self.config["prompt_length_limit"],
            target_score=target_score
        )
    
    def _get_agent_info(self, agent_id: str) -> Dict[str, str]:
        """获取智能体信息"""
        agent_info_map = {
            "NAA": {
                "type": "新闻分析智能体",
                "role_description": "分析市场新闻和舆论情绪，评估新闻对股票价格的影响"
            },
            "TAA": {
                "type": "技术分析智能体", 
                "role_description": "通过图表模式和技术指标分析股票趋势，识别支撑阻力位"
            },
            "FAA": {
                "type": "基本面分析智能体",
                "role_description": "分析公司财务健康状况和内在价值，评估长期投资价值"
            },
            "BOA": {
                "type": "看涨展望智能体",
                "role_description": "构建乐观的市场展望，寻找和强调积极因素"
            },
            "BeOA": {
                "type": "看跌展望智能体",
                "role_description": "构建谨慎的市场展望，识别和强调风险因素"
            },
            "NOA": {
                "type": "中性观察智能体",
                "role_description": "提供平衡的市场分析，客观评估看涨和看跌因素"
            },
            "TRA": {
                "type": "交易决策智能体",
                "role_description": "综合所有分析做出最终交易决策，管理风险和仓位"
            }
        }
        
        return agent_info_map.get(agent_id, {
            "type": "未知智能体",
            "role_description": "智能体角色描述不可用"
        })
    
    def _generate_prompt_candidates(self, 
                                  meta_prompt: str, 
                                  k: int, 
                                  temperature: float = 1.0) -> List[str]:
        """生成候选提示词"""
        
        candidates = []
        max_attempts = k * 2  # 最多尝试生成2倍数量的候选
        
        for attempt in range(max_attempts):
            if len(candidates) >= k:
                break
                
            try:
                self.logger.debug(f"生成候选提示词 {attempt + 1}/{max_attempts}")
                
                # 调用LLM生成候选
                response = self.llm_interface.analyze(
                    prompt=meta_prompt,
                    model="glm-4-flash",
                    temperature=temperature
                )
                
                if response and isinstance(response, dict):
                    candidate_prompt = response.get("content", "").strip()
                elif response:
                    candidate_prompt = str(response).strip()
                else:
                    continue
                
                # 清理候选提示词
                candidate_prompt = self._clean_candidate_prompt(candidate_prompt)
                
                # 验证候选质量
                if self._validate_candidate_prompt(candidate_prompt, candidates):
                    candidates.append(candidate_prompt)
                    self.logger.debug(f"生成有效候选 {len(candidates)}/{k}")
                
            except Exception as e:
                self.logger.warning(f"生成候选提示词失败 (尝试 {attempt + 1}): {e}")
                continue
        
        self.logger.info(f"成功生成 {len(candidates)} 个候选提示词")
        self._stats["total_candidates_generated"] += len(candidates)
        
        return candidates
    
    def _clean_candidate_prompt(self, prompt: str) -> str:
        """清理候选提示词"""
        # 移除可能的格式标记
        prompt = prompt.replace("```", "").replace("[", "").replace("]", "")
        
        # 移除多余的空白字符
        prompt = " ".join(prompt.split())
        
        # 限制长度
        max_length = self.config["prompt_length_limit"]
        if len(prompt) > max_length:
            prompt = prompt[:max_length].rsplit("。", 1)[0] + "。"
        
        return prompt.strip()
    
    def _validate_candidate_prompt(self, candidate: str, existing_candidates: List[str]) -> bool:
        """验证候选提示词质量"""
        
        # 检查长度
        if len(candidate) < 50:
            return False
        
        # 检查是否包含关键词
        required_keywords = ["分析", "智能体", "市场", "股票"]
        if not any(keyword in candidate for keyword in required_keywords):
            return False
        
        # 检查重复性
        for existing in existing_candidates:
            similarity = self._calculate_text_similarity(candidate, existing)
            if similarity > 0.8:  # 80%相似度阈值
                return False
        
        return True
    
    def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """计算文本相似度（简单的Jaccard相似度）"""
        words1 = set(text1.split())
        words2 = set(text2.split())
        
        intersection = words1 & words2
        union = words1 | words2
        
        if not union:
            return 0.0
        
        return len(intersection) / len(union)
    
    def _evaluate_candidates_batch(self, 
                                 agent_id: str, 
                                 candidates: List[str]) -> List[Dict[str, Any]]:
        """批量评估候选提示词"""
        
        if not self.config["parallel_evaluation"] or len(candidates) <= 2:
            # 串行评估
            return [self._evaluate_single_candidate(agent_id, candidate, i) 
                   for i, candidate in enumerate(candidates)]
        
        # 并行评估
        evaluation_results = []
        with concurrent.futures.ThreadPoolExecutor(
            max_workers=self.config["max_workers"]
        ) as executor:
            
            # 提交评估任务
            future_to_candidate = {
                executor.submit(self._evaluate_single_candidate, agent_id, candidate, i): 
                (candidate, i) for i, candidate in enumerate(candidates)
            }
            
            # 收集结果
            for future in concurrent.futures.as_completed(
                future_to_candidate, 
                timeout=self.config["evaluation_timeout"]
            ):
                candidate, index = future_to_candidate[future]
                try:
                    result = future.result()
                    result["index"] = index
                    evaluation_results.append(result)
                except Exception as e:
                    self.logger.error(f"评估候选 {index} 失败: {e}")
                    evaluation_results.append({
                        "index": index,
                        "candidate": candidate,
                        "estimated_score": 0.0,
                        "confidence": 0.0,
                        "error": str(e)
                    })
        
        # 按索引排序
        evaluation_results.sort(key=lambda x: x["index"])
        
        self._stats["total_candidates_evaluated"] += len(evaluation_results)
        return evaluation_results
    
    def _evaluate_single_candidate(self, 
                                 agent_id: str, 
                                 candidate: str, 
                                 index: int) -> Dict[str, Any]:
        """评估单个候选提示词"""
        
        # 检查缓存
        candidate_hash = self._hash_prompt(candidate)
        if self.config["enable_cache"]:
            cached_result = self._get_cached_evaluation(candidate_hash)
            if cached_result:
                self.logger.debug(f"使用缓存结果评估候选 {index}")
                return cached_result
        
        try:
            # 这里实现快速评估逻辑
            # 可以使用启发式方法或小规模测试
            estimated_score = self._quick_estimate_prompt_performance(
                agent_id, candidate
            )
            
            confidence = self._calculate_estimation_confidence(candidate)
            
            result = {
                "candidate": candidate,
                "estimated_score": estimated_score,
                "confidence": confidence,
                "evaluation_method": "heuristic",
                "timestamp": datetime.now().isoformat()
            }
            
            # 缓存结果
            if self.config["enable_cache"]:
                self._cache_evaluation(candidate_hash, result)
            
            return result
            
        except Exception as e:
            self.logger.error(f"评估候选 {index} 时出错: {e}")
            return {
                "candidate": candidate,
                "estimated_score": 0.0,
                "confidence": 0.0,
                "error": str(e)
            }
    
    def _quick_estimate_prompt_performance(self, agent_id: str, prompt: str) -> float:
        """快速估算提示词性能（启发式方法）"""
        
        # 基础分数
        base_score = 0.0
        
        # 长度评分（150-300字符为最佳）
        length = len(prompt)
        if 150 <= length <= 300:
            length_score = 1.0
        elif 100 <= length < 150 or 300 < length <= 400:
            length_score = 0.8
        else:
            length_score = 0.5
        
        # 关键词评分
        keyword_score = self._calculate_keyword_score(agent_id, prompt)
        
        # 结构化评分
        structure_score = self._calculate_structure_score(prompt)
        
        # 专业性评分
        professional_score = self._calculate_professional_score(prompt)
        
        # 综合评分
        estimated_score = (
            base_score + 
            length_score * 0.2 + 
            keyword_score * 0.3 + 
            structure_score * 0.3 + 
            professional_score * 0.2
        )
        
        # 添加一些随机性来模拟真实评估的不确定性
        noise = np.random.normal(0, 0.1)
        estimated_score += noise
        
        return max(0.0, estimated_score)
    
    def _calculate_keyword_score(self, agent_id: str, prompt: str) -> float:
        """计算关键词得分"""
        
        # 通用关键词
        general_keywords = [
            "分析", "市场", "股票", "数据", "决策", "风险", "收益", 
            "评估", "预测", "判断", "建议", "策略", "投资"
        ]
        
        # 智能体特定关键词
        specific_keywords = {
            "NAA": ["新闻", "舆论", "情绪", "事件", "媒体", "消息"],
            "TAA": ["技术", "图表", "指标", "趋势", "支撑", "阻力", "K线"],
            "FAA": ["基本面", "财务", "估值", "盈利", "资产", "负债", "现金流"],
            "BOA": ["看涨", "乐观", "积极", "上涨", "买入", "机会"],
            "BeOA": ["看跌", "悲观", "风险", "下跌", "卖出", "警惕"],
            "NOA": ["中性", "平衡", "客观", "理性", "观望", "等待"],
            "TRA": ["交易", "买卖", "仓位", "止损", "止盈", "执行"]
        }
        
        prompt_lower = prompt.lower()
        
        # 计算通用关键词得分
        general_score = sum(1 for kw in general_keywords if kw in prompt_lower)
        general_score = min(general_score / len(general_keywords), 1.0)
        
        # 计算特定关键词得分
        agent_specific = specific_keywords.get(agent_id, [])
        if agent_specific:
            specific_score = sum(1 for kw in agent_specific if kw in prompt_lower)
            specific_score = min(specific_score / len(agent_specific), 1.0)
        else:
            specific_score = 0.5  # 默认分数
        
        return (general_score + specific_score) / 2
    
    def _calculate_structure_score(self, prompt: str) -> float:
        """计算结构化得分"""
        score = 0.0
        
        # 检查是否包含角色定义
        if any(phrase in prompt for phrase in ["你是", "作为", "角色", "智能体"]):
            score += 0.3
        
        # 检查是否包含任务描述
        if any(phrase in prompt for phrase in ["任务", "目标", "需要", "要求"]):
            score += 0.3
        
        # 检查是否包含输出要求
        if any(phrase in prompt for phrase in ["返回", "输出", "格式", "JSON"]):
            score += 0.3
        
        # 检查句子完整性
        sentences = prompt.split("。")
        complete_sentences = [s for s in sentences if len(s.strip()) > 10]
        if len(complete_sentences) >= 3:
            score += 0.1
        
        return min(score, 1.0)
    
    def _calculate_professional_score(self, prompt: str) -> float:
        """计算专业性得分"""
        
        professional_terms = [
            "分析师", "专业", "专家", "系统", "方法", "流程", "标准",
            "准确", "客观", "科学", "量化", "模型", "算法", "指标"
        ]
        
        prompt_lower = prompt.lower()
        professional_count = sum(1 for term in professional_terms if term in prompt_lower)
        
        return min(professional_count / len(professional_terms), 1.0)
    
    def _calculate_estimation_confidence(self, prompt: str) -> float:
        """计算估算置信度"""
        
        # 基于提示词质量指标计算置信度
        length_factor = min(len(prompt) / 200, 1.0)
        
        # 检查完整性
        completeness = 0.0
        if "你是" in prompt:
            completeness += 0.33
        if any(word in prompt for word in ["任务", "目标", "分析"]):
            completeness += 0.33
        if any(word in prompt for word in ["返回", "输出", "格式"]):
            completeness += 0.34
        
        confidence = (length_factor + completeness) / 2
        return min(max(confidence, 0.1), 0.9)  # 限制在0.1-0.9之间
    
    def _select_best_candidate(self, 
                             candidates: List[str], 
                             evaluation_results: List[Dict], 
                             historical_data: List[Dict]) -> Dict[str, Any]:
        """选择最佳候选提示词"""
        
        if not evaluation_results:
            # 如果没有评估结果，返回第一个候选
            return {
                "prompt": candidates[0] if candidates else "",
                "estimated_score": 0.0,
                "estimated_improvement": 0.0,
                "selection_reason": "无评估结果，返回第一个候选"
            }
        
        # 获取历史最佳得分
        historical_scores = [data.get("score", 0) for data in historical_data]
        baseline_score = max(historical_scores) if historical_scores else 0.0
        
        # 按估算得分排序
        sorted_results = sorted(
            evaluation_results, 
            key=lambda x: x.get("estimated_score", 0), 
            reverse=True
        )
        
        best_result = sorted_results[0]
        estimated_improvement = best_result.get("estimated_score", 0) - baseline_score
        
        return {
            "prompt": best_result.get("candidate", ""),
            "estimated_score": best_result.get("estimated_score", 0.0),
            "estimated_improvement": estimated_improvement,
            "confidence": best_result.get("confidence", 0.0),
            "selection_reason": f"估算得分最高: {best_result.get('estimated_score', 0):.6f}",
            "baseline_score": baseline_score
        }
    
    def _hash_prompt(self, prompt: str) -> str:
        """生成提示词哈希值"""
        return hashlib.md5(prompt.encode('utf-8')).hexdigest()
    
    def _get_cached_evaluation(self, prompt_hash: str) -> Optional[Dict[str, Any]]:
        """获取缓存的评估结果"""
        with self._cache_lock:
            cached = self._evaluation_cache.get(prompt_hash)
            if cached and time.time() - cached["timestamp"] < self.config["cache_ttl"]:
                return cached["result"]
        return None
    
    def _cache_evaluation(self, prompt_hash: str, result: Dict[str, Any]) -> None:
        """缓存评估结果"""
        with self._cache_lock:
            self._evaluation_cache[prompt_hash] = {
                "result": result,
                "timestamp": time.time()
            }
    
    def _update_optimization_stats(self, 
                                 agent_id: str, 
                                 best_candidate: Dict[str, Any], 
                                 optimization_time: float) -> None:
        """更新优化统计信息"""
        
        self._stats["total_optimizations"] += 1
        
        if best_candidate.get("estimated_improvement", 0) > 0:
            self._stats["successful_optimizations"] += 1
            
            # 更新最佳改进记录
            current_improvement = best_candidate.get("estimated_improvement", 0)
            if (agent_id not in self._stats["best_improvements"] or 
                current_improvement > self._stats["best_improvements"][agent_id]):
                self._stats["best_improvements"][agent_id] = current_improvement
        else:
            self._stats["failed_optimizations"] += 1
        
        # 记录优化历史
        self._stats["optimization_history"].append({
            "agent_id": agent_id,
            "timestamp": datetime.now().isoformat(),
            "estimated_improvement": best_candidate.get("estimated_improvement", 0),
            "estimated_score": best_candidate.get("estimated_score", 0),
            "optimization_time": optimization_time
        })
        
        # 保持历史记录在合理大小
        if len(self._stats["optimization_history"]) > 1000:
            self._stats["optimization_history"] = self._stats["optimization_history"][-500:]
    
    def _create_failed_result(self, error_message: str, start_time: float) -> Dict[str, Any]:
        """创建失败结果"""
        return {
            "success": False,
            "error": error_message,
            "optimization_time": time.time() - start_time,
            "agent_id": None,
            "optimized_prompt": None,
            "estimated_score": 0.0,
            "improvement": 0.0
        }
    
    def optimize_all_agents(self, 
                          agent_ids: List[str], 
                          current_prompts: Dict[str, str]) -> Dict[str, Any]:
        """优化所有智能体的提示词"""
        
        self.logger.info(f"开始批量优化 {len(agent_ids)} 个智能体")
        
        results = {}
        start_time = time.time()
        
        for agent_id in agent_ids:
            current_prompt = current_prompts.get(agent_id, "")
            
            try:
                result = self.optimize_agent_prompt(agent_id, current_prompt)
                results[agent_id] = result
                
                if result["success"]:
                    self.logger.info(f"✅ {agent_id} 优化成功，"
                                   f"预期改进: {result['improvement']:.6f}")
                else:
                    self.logger.warning(f"❌ {agent_id} 优化失败: {result.get('error', '未知错误')}")
                    
            except Exception as e:
                self.logger.error(f"❌ {agent_id} 优化异常: {e}")
                results[agent_id] = self._create_failed_result(str(e), time.time())
        
        total_time = time.time() - start_time
        
        # 统计成功率
        successful_count = sum(1 for r in results.values() if r.get("success", False))
        success_rate = successful_count / len(agent_ids) if agent_ids else 0
        
        self.logger.info(f"批量优化完成: {successful_count}/{len(agent_ids)} 成功，"
                        f"成功率: {success_rate:.1%}，总耗时: {total_time:.2f}s")
        
        return {
            "success": True,
            "total_agents": len(agent_ids),
            "successful_optimizations": successful_count,
            "success_rate": success_rate,
            "total_time": total_time,
            "results": results,
            "summary": self._create_optimization_summary(results)
        }
    
    def _create_optimization_summary(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """创建优化结果汇总"""
        
        successful_results = [r for r in results.values() if r.get("success", False)]
        
        if not successful_results:
            return {
                "total_improvements": 0,
                "average_improvement": 0.0,
                "best_improvement": 0.0,
                "worst_improvement": 0.0
            }
        
        improvements = [r.get("improvement", 0) for r in successful_results]
        
        return {
            "total_improvements": len([i for i in improvements if i > 0]),
            "average_improvement": np.mean(improvements),
            "best_improvement": max(improvements),
            "worst_improvement": min(improvements),
            "std_improvement": np.std(improvements)
        }
    
    def get_stats(self) -> Dict[str, Any]:
        """获取优化统计信息"""
        stats = self._stats.copy()
        
        if stats["total_optimizations"] > 0:
            stats["success_rate"] = (
                stats["successful_optimizations"] / stats["total_optimizations"]
            ) * 100
        else:
            stats["success_rate"] = 0.0
        
        return stats
    
    def clear_cache(self) -> None:
        """清理缓存"""
        with self._cache_lock:
            self._evaluation_cache.clear()
            self._prompt_cache.clear()
        
        self.logger.info("OPRO优化器缓存已清理")