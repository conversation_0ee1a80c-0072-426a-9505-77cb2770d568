# 数据存储系统快速参考

## 常用命令

### 基本运行
```bash
# 启用数据存储的评估模式
python run_opro_system.py --mode evaluation --enable-data-storage

# 禁用数据存储
python run_opro_system.py --mode evaluation --disable-data-storage
```

### 数据分析报告
```bash
# 交易性能分析
python run_opro_system.py --analysis-report trading --export-format excel

# 提示词优化分析
python run_opro_system.py --analysis-report optimization --export-format csv

# 综合分析报告
python run_opro_system.py --analysis-report comprehensive
```

### 数据导出
```bash
# 导出所有数据
python run_opro_system.py --export-all-data --export-format excel

# 生成系统报告
python run_opro_system.py --data-report
```

### 备份管理
```bash
# 创建完整备份
python run_opro_system.py --create-backup --backup-type full

# 创建增量备份
python run_opro_system.py --create-backup --backup-type incremental

# 查看备份状态
python run_opro_system.py --backup-status

# 列出所有备份
python run_opro_system.py --list-backups
```

## 配置参数

### 主要配置项
```json
{
  "storage": {
    "comprehensive_storage": {
      "enabled": true,
      "base_path": "data",
      "auto_backup_interval_hours": 24,
      "data_validation_enabled": true,
      "compression_enabled": true
    }
  }
}
```

## 数据目录结构

```
data/
├── trading/           # 交易数据
│   └── 2024-01-01/   # 按日期组织
├── prompts/           # 提示词数据
│   └── NAA/          # 按智能体组织
├── visualizations/    # 可视化图表
├── exports/          # 导出文件
├── backups/          # 备份文件
│   ├── full/         # 完整备份
│   ├── incremental/  # 增量备份
│   └── metadata/     # 备份元数据
└── comprehensive_storage.db  # 主数据库
```

## 支持的导出格式

- **Excel** (.xlsx) - 推荐用于报告
- **CSV** (.csv) - 适合数据分析
- **JSON** (.json) - 适合程序处理

## 备份类型

- **full** - 完整备份，包含所有数据
- **incremental** - 增量备份，仅备份变更
- **auto** - 自动选择（推荐）

## 快速故障排除

### 数据存储未启用
```bash
# 检查配置
cat config/opro_config.json | grep -A 10 comprehensive_storage

# 强制启用
python run_opro_system.py --enable-data-storage
```

### 备份失败
```bash
# 检查磁盘空间
df -h

# 检查权限
ls -la data/backups/

# 查看详细日志
python run_opro_system.py --log-file debug.log
```

### 导出失败
```bash
# 尝试不同格式
python run_opro_system.py --export-all-data --export-format json

# 检查导出目录
ls -la data/exports/
```

## 性能提示

1. **使用增量备份**减少备份时间
2. **启用压缩**节省存储空间
3. **定期清理**旧的导出文件
4. **监控磁盘空间**避免存储不足

## 常用查询

### 查看系统状态
```bash
python run_opro_system.py --data-report --backup-status
```

### 完整工作流程
```bash
# 1. 运行评估
python run_opro_system.py --mode evaluation --stocks AAPL

# 2. 生成报告
python run_opro_system.py --analysis-report comprehensive

# 3. 创建备份
python run_opro_system.py --create-backup
```

---
*快速参考 v1.0 | 2025-07-04*
