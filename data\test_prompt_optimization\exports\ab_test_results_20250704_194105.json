[{"test_id": "ab_test_TRA_1_20250704_194105", "agent_id": "TRA", "optimization_id": "opt_TRA_1_20250704_194105", "test_start_date": "2025-07-04", "test_end_date": "2025-07-11", "control_prompt": "你是一个交易智能体，负责根据分析结果做出交易决策。", "variant_prompts": "{\"control\": \"\\u4f60\\u662f\\u4e00\\u4e2a\\u4ea4\\u6613\\u667a\\u80fd\\u4f53\\uff0c\\u8d1f\\u8d23\\u6839\\u636e\\u5206\\u6790\\u7ed3\\u679c\\u505a\\u51fa\\u4ea4\\u6613\\u51b3\\u7b56\\u3002\", \"variant_1\": \"\\u4f60\\u662f\\u4e00\\u4e2a\\u4ea4\\u6613\\u667a\\u80fd\\u4f53\\uff0c\\u8d1f\\u8d23\\u6839\\u636e\\u5206\\u6790\\u7ed3\\u679c\\u505a\\u51fa\\u4ea4\\u6613\\u51b3\\u7b56\\u3002\\n\\n\\u4f18\\u5316\\u6307\\u5bfc\\uff1a\\u8bf7\\u66f4\\u52a0\\u8c28\\u614e\\u5730\\u8bc4\\u4f30\\u98ce\\u9669\\u6536\\u76ca\\u6bd4\\uff0c\\u4f18\\u5316\\u4ed3\\u4f4d\\u7ba1\\u7406\\u7b56\\u7565\\u3002\\n\\n\\u8bf7\\u7279\\u522b\\u6ce8\\u610f\\u63d0\\u9ad8\\u5206\\u6790\\u8d28\\u91cf\\u548c\\u51b3\\u7b56\\u51c6\\u786e\\u6027\\u3002\"}", "test_results": "{}", "winning_variant": "", "confidence_level": 0.0, "created_at": "2025-07-04 11:41:05"}, {"test_id": "ab_test_BOA_1_20250704_194105", "agent_id": "BOA", "optimization_id": "opt_BOA_1_20250704_194105", "test_start_date": "2025-07-04", "test_end_date": "2025-07-11", "control_prompt": "你是智能体 BOA，负责协助交易决策。", "variant_prompts": "{\"control\": \"\\u4f60\\u662f\\u667a\\u80fd\\u4f53 BOA\\uff0c\\u8d1f\\u8d23\\u534f\\u52a9\\u4ea4\\u6613\\u51b3\\u7b56\\u3002\", \"variant_1\": \"\\u4f60\\u662f\\u667a\\u80fd\\u4f53 BOA\\uff0c\\u8d1f\\u8d23\\u534f\\u52a9\\u4ea4\\u6613\\u51b3\\u7b56\\u3002\\n\\n\\u4f18\\u5316\\u6307\\u5bfc\\uff1a\\u8bf7\\u63d0\\u9ad8\\u5206\\u6790\\u7684\\u51c6\\u786e\\u6027\\u548c\\u5b9e\\u7528\\u6027\\u3002\\n\\n\\u8bf7\\u7279\\u522b\\u6ce8\\u610f\\u63d0\\u9ad8\\u5206\\u6790\\u8d28\\u91cf\\u548c\\u51b3\\u7b56\\u51c6\\u786e\\u6027\\u3002\"}", "test_results": "{}", "winning_variant": "", "confidence_level": 0.0, "created_at": "2025-07-04 11:41:05"}, {"test_id": "ab_test_TRA_2_20250704_194105", "agent_id": "TRA", "optimization_id": "opt_TRA_2_20250704_194105", "test_start_date": "2025-07-04", "test_end_date": "2025-07-11", "control_prompt": "你是一个交易智能体，负责根据分析结果做出交易决策。", "variant_prompts": "{\"control\": \"\\u4f60\\u662f\\u4e00\\u4e2a\\u4ea4\\u6613\\u667a\\u80fd\\u4f53\\uff0c\\u8d1f\\u8d23\\u6839\\u636e\\u5206\\u6790\\u7ed3\\u679c\\u505a\\u51fa\\u4ea4\\u6613\\u51b3\\u7b56\\u3002\", \"variant_1\": \"\\u4f60\\u662f\\u4e00\\u4e2a\\u4ea4\\u6613\\u667a\\u80fd\\u4f53\\uff0c\\u8d1f\\u8d23\\u6839\\u636e\\u5206\\u6790\\u7ed3\\u679c\\u505a\\u51fa\\u4ea4\\u6613\\u51b3\\u7b56\\u3002\\n\\n\\u4f18\\u5316\\u6307\\u5bfc\\uff1a\\u8bf7\\u66f4\\u52a0\\u8c28\\u614e\\u5730\\u8bc4\\u4f30\\u98ce\\u9669\\u6536\\u76ca\\u6bd4\\uff0c\\u4f18\\u5316\\u4ed3\\u4f4d\\u7ba1\\u7406\\u7b56\\u7565\\u3002\\n\\n\\u8bf7\\u7279\\u522b\\u6ce8\\u610f\\u63d0\\u9ad8\\u5206\\u6790\\u8d28\\u91cf\\u548c\\u51b3\\u7b56\\u51c6\\u786e\\u6027\\u3002\"}", "test_results": "{}", "winning_variant": "", "confidence_level": 0.0, "created_at": "2025-07-04 11:41:05"}, {"test_id": "ab_test_BOA_2_20250704_194105", "agent_id": "BOA", "optimization_id": "opt_BOA_2_20250704_194105", "test_start_date": "2025-07-04", "test_end_date": "2025-07-11", "control_prompt": "你是智能体 BOA，负责协助交易决策。", "variant_prompts": "{\"control\": \"\\u4f60\\u662f\\u667a\\u80fd\\u4f53 BOA\\uff0c\\u8d1f\\u8d23\\u534f\\u52a9\\u4ea4\\u6613\\u51b3\\u7b56\\u3002\", \"variant_1\": \"\\u4f60\\u662f\\u667a\\u80fd\\u4f53 BOA\\uff0c\\u8d1f\\u8d23\\u534f\\u52a9\\u4ea4\\u6613\\u51b3\\u7b56\\u3002\\n\\n\\u4f18\\u5316\\u6307\\u5bfc\\uff1a\\u8bf7\\u63d0\\u9ad8\\u5206\\u6790\\u7684\\u51c6\\u786e\\u6027\\u548c\\u5b9e\\u7528\\u6027\\u3002\\n\\n\\u8bf7\\u7279\\u522b\\u6ce8\\u610f\\u63d0\\u9ad8\\u5206\\u6790\\u8d28\\u91cf\\u548c\\u51b3\\u7b56\\u51c6\\u786e\\u6027\\u3002\"}", "test_results": "{}", "winning_variant": "", "confidence_level": 0.0, "created_at": "2025-07-04 11:41:05"}, {"test_id": "ab_test_BOA_3_20250704_194105", "agent_id": "BOA", "optimization_id": "opt_BOA_3_20250704_194105", "test_start_date": "2025-07-04", "test_end_date": "2025-07-11", "control_prompt": "你是智能体 BOA，负责协助交易决策。", "variant_prompts": "{\"control\": \"\\u4f60\\u662f\\u667a\\u80fd\\u4f53 BOA\\uff0c\\u8d1f\\u8d23\\u534f\\u52a9\\u4ea4\\u6613\\u51b3\\u7b56\\u3002\", \"variant_1\": \"\\u4f60\\u662f\\u667a\\u80fd\\u4f53 BOA\\uff0c\\u8d1f\\u8d23\\u534f\\u52a9\\u4ea4\\u6613\\u51b3\\u7b56\\u3002\\n\\n\\u4f18\\u5316\\u6307\\u5bfc\\uff1a\\u8bf7\\u63d0\\u9ad8\\u5206\\u6790\\u7684\\u51c6\\u786e\\u6027\\u548c\\u5b9e\\u7528\\u6027\\u3002\\n\\n\\u8bf7\\u7279\\u522b\\u6ce8\\u610f\\u63d0\\u9ad8\\u5206\\u6790\\u8d28\\u91cf\\u548c\\u51b3\\u7b56\\u51c6\\u786e\\u6027\\u3002\"}", "test_results": "{}", "winning_variant": "", "confidence_level": 0.0, "created_at": "2025-07-04 11:41:05"}]