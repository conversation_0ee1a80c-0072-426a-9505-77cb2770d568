#!/usr/bin/env python3
"""
综合数据存储系统演示脚本

本脚本演示如何使用综合数据存储系统的各种功能，包括：
1. 数据收集和存储
2. 分析报告生成
3. 可视化图表创建
4. 备份管理
5. 数据导出

作者: AI Assistant
创建时间: 2025-07-04
"""

import os
import sys
import json
import logging
from datetime import datetime, timedelta

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from data.integrated_data_manager import IntegratedDataManager
from data.trading_data_collector import AgentInputData, AgentOutputData, MarketConditions

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('demo.log')
        ]
    )
    return logging.getLogger(__name__)

def load_demo_config():
    """加载演示配置"""
    config = {
        "storage": {
            "comprehensive_storage": {
                "enabled": True,
                "base_path": "demo_data",
                "trading_data_path": "demo_data/trading",
                "prompts_data_path": "demo_data/prompts",
                "visualizations_path": "demo_data/visualizations",
                "exports_path": "demo_data/exports",
                "backups_path": "demo_data/backups",
                "database_path": "demo_data/demo_storage.db",
                "auto_backup_interval_hours": 1,  # 演示用短间隔
                "data_validation_enabled": True,
                "compression_enabled": True,
                "max_file_size_mb": 50
            }
        }
    }
    return config

def create_sample_trading_data(data_manager, logger):
    """创建示例交易数据"""
    logger.info("创建示例交易数据...")
    
    # 模拟3天的交易数据
    for day in range(3):
        date = datetime.now() - timedelta(days=day)
        
        # 开始交易会话
        session_id = data_manager.trading_collector.start_trading_session(
            f"demo_session_{date.strftime('%Y%m%d')}"
        )
        
        # 为每个智能体创建输入数据
        agents = ["NAA", "TAA", "FAA", "BOA", "TRA"]
        for agent_id in agents:
            # 智能体输入数据
            input_data = AgentInputData(
                agent_id=agent_id,
                market_signals={
                    "price_trend": "upward" if day % 2 == 0 else "downward",
                    "volume_trend": "high",
                    "volatility": 0.15 + day * 0.05
                },
                analysis_inputs={
                    "technical_indicators": {
                        "rsi": 45 + day * 5,
                        "macd": 0.1 + day * 0.05
                    },
                    "fundamental_data": {
                        "pe_ratio": 15.5,
                        "market_cap": 1000000000
                    }
                },
                decision_factors={
                    "risk_tolerance": 0.7,
                    "market_sentiment": "neutral",
                    "portfolio_balance": 0.3
                },
                timestamp=date.isoformat(),
                metadata={"demo": True, "day": day}
            )
            
            data_manager.trading_collector.collect_agent_input(input_data)
            
            # 智能体输出数据
            output_data = AgentOutputData(
                agent_id=agent_id,
                trading_decisions={
                    "action": "buy" if day % 2 == 0 else "sell",
                    "quantity": 100 + day * 50,
                    "price_target": 150.0 + day * 10
                },
                recommendations=[
                    {
                        "type": "position",
                        "recommendation": "increase" if day % 2 == 0 else "decrease",
                        "confidence": 0.8 - day * 0.1
                    }
                ],
                confidence_scores={
                    "overall": 0.75 + day * 0.05,
                    "technical": 0.8,
                    "fundamental": 0.7
                },
                reasoning=f"基于第{day+1}天的市场分析，建议{'买入' if day % 2 == 0 else '卖出'}",
                timestamp=date.isoformat(),
                metadata={"demo": True, "day": day}
            )
            
            data_manager.trading_collector.collect_agent_output(output_data)
            
            # 记录盈亏
            pnl = (100 - day * 20) * (1 if day % 2 == 0 else -1)
            data_manager.trading_collector.record_profit_loss(
                agent_id, pnl, {"demo": True, "calculation": "simulated"}
            )
        
        # 市场条件数据
        market_data = MarketConditions(
            timestamp=date.isoformat(),
            stock_prices={
                "AAPL": {
                    "open": 150.0 + day,
                    "high": 155.0 + day,
                    "low": 148.0 + day,
                    "close": 152.0 + day,
                    "volume": 1000000 + day * 100000
                }
            },
            market_indicators={
                "vix": 20.0 + day,
                "sp500": 4000 + day * 10
            },
            volatility_metrics={
                "daily_volatility": 0.02 + day * 0.005
            },
            news_sentiment={
                "overall_sentiment": 0.1 - day * 0.05,
                "news_count": 10 + day
            },
            trading_volume={
                "AAPL": 1000000 + day * 100000
            },
            metadata={"demo": True, "day": day}
        )
        
        data_manager.trading_collector.collect_market_conditions(market_data)
        
        # 结束交易会话
        session_summary = {
            "demo": True,
            "day": day,
            "total_agents": len(agents),
            "market_condition": "volatile" if day == 1 else "stable"
        }
        
        data_manager.trading_collector.end_trading_session(session_summary)
        
        logger.info(f"第{day+1}天交易数据创建完成")

def create_sample_optimization_data(data_manager, logger):
    """创建示例提示词优化数据"""
    logger.info("创建示例提示词优化数据...")
    
    agents = ["NAA", "TAA", "FAA"]
    
    for i, agent_id in enumerate(agents):
        original_prompt = f"原始提示词 for {agent_id}: 请分析市场数据并给出交易建议。"
        optimized_prompt = f"优化提示词 for {agent_id}: 请基于技术指标、基本面分析和市场情绪，综合评估风险收益比，给出具体的交易建议，包括买卖方向、数量和目标价位。"
        
        optimization_result = data_manager.track_prompt_optimization(
            agent_id=agent_id,
            original_prompt=original_prompt,
            optimized_prompt=optimized_prompt,
            optimization_reason=f"基于{agent_id}的历史性能分析，增加了更详细的分析维度",
            performance_metrics={
                "improvement": 0.15 + i * 0.05,
                "confidence": 0.85 + i * 0.02,
                "accuracy": 0.78 + i * 0.03
            }
        )
        
        if optimization_result.get("success"):
            logger.info(f"{agent_id} 提示词优化记录创建成功")
        else:
            logger.error(f"{agent_id} 提示词优化记录创建失败")

def demonstrate_analysis_reports(data_manager, logger):
    """演示分析报告生成"""
    logger.info("生成分析报告...")
    
    # 生成交易性能分析报告
    trading_report = data_manager.generate_analysis_report("trading")
    if trading_report.get("success"):
        logger.info(f"交易性能分析报告已生成: {trading_report.get('export_directory')}")
    else:
        logger.error(f"交易性能分析报告生成失败: {trading_report.get('error')}")
    
    # 生成提示词优化分析报告
    optimization_report = data_manager.generate_analysis_report("optimization")
    if optimization_report.get("success"):
        logger.info(f"提示词优化分析报告已生成: {optimization_report.get('export_directory')}")
    else:
        logger.error(f"提示词优化分析报告生成失败: {optimization_report.get('error')}")
    
    # 生成综合分析报告
    comprehensive_report = data_manager.generate_analysis_report("comprehensive")
    if comprehensive_report.get("success"):
        logger.info(f"综合分析报告已生成: {comprehensive_report.get('dashboard_directory')}")
    else:
        logger.error(f"综合分析报告生成失败: {comprehensive_report.get('error')}")

def demonstrate_backup_management(data_manager, logger):
    """演示备份管理"""
    logger.info("演示备份管理...")
    
    # 创建完整备份
    backup_result = data_manager.create_backup("full")
    if backup_result.get("success"):
        logger.info(f"完整备份创建成功: {backup_result.get('backup_path')} ({backup_result.get('backup_size_mb'):.2f} MB)")
    else:
        logger.error(f"完整备份创建失败: {backup_result.get('error')}")
    
    # 查看备份状态
    backup_status = data_manager.get_backup_status()
    if backup_status.get("enabled"):
        logger.info("备份系统状态:")
        logger.info(f"  总备份数: {backup_status.get('total_backups', 0)}")
        logger.info(f"  完整备份数: {backup_status.get('full_backups', 0)}")
        logger.info(f"  增量备份数: {backup_status.get('incremental_backups', 0)}")
        logger.info(f"  总大小: {backup_status.get('total_size_mb', 0):.2f} MB")
    
    # 列出备份
    backups = data_manager.list_backups()
    logger.info(f"当前有 {len(backups)} 个备份")

def demonstrate_data_export(data_manager, logger):
    """演示数据导出"""
    logger.info("演示数据导出...")
    
    # 导出所有数据
    export_result = data_manager.export_all_data("excel")
    if export_result.get("success"):
        logger.info(f"数据导出成功: {export_result.get('export_directory')}")
    else:
        logger.error(f"数据导出失败: {export_result.get('error')}")

def main():
    """主演示函数"""
    logger = setup_logging()
    logger.info("开始综合数据存储系统演示")
    
    try:
        # 加载配置
        config = load_demo_config()
        
        # 初始化数据管理器
        data_manager = IntegratedDataManager(config=config, logger=logger)
        
        if not data_manager.enabled:
            logger.error("数据存储功能未启用")
            return
        
        # 演示各项功能
        logger.info("=" * 60)
        logger.info("1. 创建示例交易数据")
        logger.info("=" * 60)
        create_sample_trading_data(data_manager, logger)
        
        logger.info("=" * 60)
        logger.info("2. 创建示例提示词优化数据")
        logger.info("=" * 60)
        create_sample_optimization_data(data_manager, logger)
        
        logger.info("=" * 60)
        logger.info("3. 生成分析报告")
        logger.info("=" * 60)
        demonstrate_analysis_reports(data_manager, logger)
        
        logger.info("=" * 60)
        logger.info("4. 备份管理演示")
        logger.info("=" * 60)
        demonstrate_backup_management(data_manager, logger)
        
        logger.info("=" * 60)
        logger.info("5. 数据导出演示")
        logger.info("=" * 60)
        demonstrate_data_export(data_manager, logger)
        
        # 生成综合报告
        logger.info("=" * 60)
        logger.info("6. 系统状态报告")
        logger.info("=" * 60)
        comprehensive_report = data_manager.generate_comprehensive_report()
        
        if comprehensive_report.get("enabled"):
            logger.info("系统状态摘要:")
            storage_stats = comprehensive_report.get("storage_statistics", {})
            logger.info(f"  交易会话数: {storage_stats.get('total_trading_sessions', 0)}")
            logger.info(f"  提示词优化数: {storage_stats.get('total_prompt_optimizations', 0)}")
            logger.info(f"  数据库大小: {storage_stats.get('database_size_mb', 0):.2f} MB")
            
            recommendations = comprehensive_report.get("recommendations", [])
            if recommendations:
                logger.info("系统建议:")
                for rec in recommendations:
                    logger.info(f"  - {rec}")
        
        logger.info("=" * 60)
        logger.info("演示完成！")
        logger.info("=" * 60)
        logger.info("生成的文件位于 demo_data/ 目录中")
        logger.info("您可以查看以下内容：")
        logger.info("  - demo_data/trading/ - 交易数据")
        logger.info("  - demo_data/prompts/ - 提示词数据")
        logger.info("  - demo_data/exports/ - 导出文件")
        logger.info("  - demo_data/backups/ - 备份文件")
        logger.info("  - demo_data/demo_storage.db - 数据库文件")
        
    except Exception as e:
        logger.error(f"演示过程中发生错误: {e}")
        raise
    
    finally:
        # 清理资源
        if 'data_manager' in locals():
            data_manager.cleanup()

if __name__ == "__main__":
    main()
