#!/usr/bin/env python3
"""
准备数据脚本

初始化数据库结构，并提供下载数据的指引。
支持为每个股票代码(ticker)创建独立的数据库。
实际数据下载逻辑已迁移到 get_OHLCV_data.py 和 get_news_data.py
"""
import os
import sys
import sqlite3

# Add project root to sys.path
# 获取当前脚本的父目录的父目录 (即项目根目录)
current_script_path = os.path.abspath(__file__)
project_root = os.path.dirname(os.path.dirname(current_script_path))
sys.path.append(project_root)

from config import DATA_DIR, ALPHAVANTAGE_API_KEY # Keep API key import for the check

def get_database_path(ticker=None):
    """
    根据ticker获取对应的数据库路径
    如果ticker为None，则返回默认数据库路径（兼容旧版本）
    """
    if ticker:
        # 确保ticker文件夹存在
        ticker_dir = os.path.join(DATA_DIR, "tickers", ticker.upper())
        os.makedirs(ticker_dir, exist_ok=True)
        # 返回特定ticker的数据库路径
        return os.path.join(ticker_dir, f"{ticker.upper()}_data.db")
    else:
        # 返回旧版本路径（兼容旧代码）
        return os.path.join(DATA_DIR, "trading_data.db")

def create_connection(ticker=None):
    """
    创建一个 SQLite 数据库连接
    如果提供了ticker，则连接到该ticker的专用数据库
    """
    database_path = get_database_path(ticker)
    conn = None
    try:
        conn = sqlite3.connect(database_path)
        print(f"成功连接到 SQLite 数据库: {database_path}", file=sys.stderr)
        return conn
    except sqlite3.Error as e:
        print(f"连接数据库时发生错误: {e}", file=sys.stderr)
        return None

def create_tables(conn):
    """在数据库中创建 OHLCV, news 和 fundamental 表"""
    ohlcv_table_sql = """
    CREATE TABLE IF NOT EXISTS ohlcv (
        ticker TEXT NOT NULL,
        trade_date DATE NOT NULL,
        Open REAL,
        High REAL,
        Low REAL,
        Close REAL,
        Adj_Close REAL,
        Volume INTEGER,
        PRIMARY KEY (ticker, trade_date)
    );
    """

    news_table_sql = """
    CREATE TABLE IF NOT EXISTS news (
        article_id TEXT PRIMARY KEY,
        ticker TEXT NOT NULL,
        title TEXT,
        url TEXT,
        time_published DATETIME,
        authors TEXT, -- Stored as JSON string
        summary TEXT,
        banner_image TEXT,
        source TEXT,
        category_within_source TEXT,
        source_domain TEXT,
        topics TEXT, -- Stored as JSON string
        overall_sentiment_score REAL,
        overall_sentiment_label TEXT,
        ticker_sentiment JSON -- Stored as JSON string
    );
    """

    # Create news table if it doesn't exist (preserve existing data)
    cursor = conn.cursor()
    cursor.execute(news_table_sql)
    print("news 表已检查/创建。", file=sys.stderr)

    # --- NEW TABLES FOR FINANCIAL STATEMENTS ---

    # Create annual_financials table
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS annual_financials (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            ticker TEXT NOT NULL,
            fiscal_date DATE NOT NULL,
            report_type TEXT NOT NULL CHECK (report_type IN ('income', 'balance', 'cashflow')),
            reported_currency TEXT,
            data_json TEXT NOT NULL, -- Store the full JSON object
            retrieved_at DATETIME NOT NULL,
            UNIQUE (ticker, fiscal_date, report_type) -- Ensure no duplicate reports for same ticker/date/type
        );
    """)

    # Create quarterly_financials table
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS quarterly_financials (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            ticker TEXT NOT NULL,
            fiscal_date DATE NOT NULL,
            report_type TEXT NOT NULL CHECK (report_type IN ('income', 'balance', 'cashflow')),
            reported_currency TEXT,
            data_json TEXT NOT NULL, -- Store the full JSON object
            retrieved_at DATETIME NOT NULL,
            UNIQUE (ticker, fiscal_date, report_type) -- Ensure no duplicate reports for same ticker/date/type
        );
    """)

    try:
        cursor.execute(ohlcv_table_sql)
        print("OHLCV 表已检查/创建。", file=sys.stderr)
        # cursor.execute(fundamental_data_table_sql) # Use new fundamental_data table
        # print("fundamental 表已检查/创建。", file=sys.stderr) # Updated print message
        conn.commit()
    except sqlite3.Error as e:
        print(f"创建表时发生错误: {e}", file=sys.stderr)

def ensure_database_structure(ticker=None):
    """
    确保特定ticker或默认数据库的结构已创建
    """
    # 确保数据目录存在
    os.makedirs(DATA_DIR, exist_ok=True)
    
    if ticker:
        # 确保ticker专用目录存在
        ticker_dir = os.path.join(DATA_DIR, "tickers", ticker.upper())
        os.makedirs(ticker_dir, exist_ok=True)
    
    # 创建数据库连接并初始化表
    conn = create_connection(ticker)
    if conn is None:
        print("无法连接到数据库，无法初始化。", file=sys.stderr)
        return False

    create_tables(conn)  # 创建表

    # 关闭数据库连接
    if conn:
        conn.close()
        print("数据库连接已关闭。", file=sys.stderr)
    
    return True

def main():
    """Main function"""
    print("Initializing data directory and database structure...")

    # 解析命令行参数，检查是否提供了ticker
    ticker = None
    if len(sys.argv) > 1:
        ticker = sys.argv[1].upper()
        print(f"为 {ticker} 初始化数据库结构...")

    # 确保数据目录存在
    os.makedirs(DATA_DIR, exist_ok=True)
    # 创建子目录
    os.makedirs(os.path.join(DATA_DIR, "fundamental"), exist_ok=True)
    os.makedirs(os.path.join(DATA_DIR, "tickers"), exist_ok=True)

    success = ensure_database_structure(ticker)
    if not success:
        print("初始化数据库结构失败", file=sys.stderr)
        sys.exit(1)

    database_path = get_database_path(ticker)
    print("\n数据库结构已初始化。")
    print(f"数据库文件: {database_path}")
    print("\n要下载数据，请运行特定脚本:")
    print("获取OHLCV数据: python data/get_OHLCV_data.py <ticker> <start_date> <end_date>")
    print("示例: python data/get_OHLCV_data.py AAPL 2023-01-01 2023-12-31")
    print("\n获取新闻数据: python data/get_news_data.py <ticker> <start_date> <end_date>")
    print("示例: python data/get_news_data.py AAPL 2023-01-01 2023-12-31")
    print("\n获取基本面数据: python data/get_fundamental_data.py <ticker>")
    print("示例: python data/get_fundamental_data.py AAPL")

    # Optional: Add a check for API key configuration
    if not ALPHAVANTAGE_API_KEY or ALPHAVANTAGE_API_KEY == "YOUR_ALPHA_VANTAGE_API_KEY":
         print("\nWarning: Alpha Vantage API key is not configured in config.py. Data download scripts may fail.", file=sys.stderr)

if __name__ == "__main__":
    main() 