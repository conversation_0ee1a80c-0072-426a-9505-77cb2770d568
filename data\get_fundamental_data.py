import requests
import sys
import json
import os
import sqlite3
import subprocess
from datetime import datetime

# Add project root to sys.path
current_script_path = os.path.abspath(__file__)
project_root = os.path.dirname(os.path.dirname(current_script_path))
sys.path.append(project_root)

# Using Alpha Vantage API Key
try:
    from config import ALPHAVANTAGE_API_KEY, DATA_DIR
except ImportError:
    print("Error: config.py not found or ALPHAVANTAGE_API_KEY/DATA_DIR not defined.")
    sys.exit(1)

def get_database_path(ticker):
    """
    根据ticker获取对应的数据库路径
    """
    ticker_dir = os.path.join(DATA_DIR, "tickers", ticker.upper())
    os.makedirs(ticker_dir, exist_ok=True)
    return os.path.join(ticker_dir, f"{ticker.upper()}_data.db")

# Alpha Vantage API Base URL
ALPHA_VANTAGE_BASE_URL = "https://www.alphavantage.co/query"

def create_connection(ticker):
    """Creates a SQLite database connection for the specified ticker"""
    conn = None
    try:
        database_path = get_database_path(ticker)
        conn = sqlite3.connect(database_path)
        conn.row_factory = sqlite3.Row # To access columns by name
        return conn
    except sqlite3.Error as e:
        print(f"Database connection error: {e}", file=sys.stderr)
        return None

def fetch_alpha_vantage_data(params):
    """Helper function to fetch data from Alpha Vantage API."""
    url = ALPHA_VANTAGE_BASE_URL
    params["apikey"] = ALPHAVANTAGE_API_KEY
    print(f"Fetching data from Alpha Vantage for {params.get('function')} with symbol {params.get('symbol')}...", file=sys.stderr)

    try:
        response = requests.get(url, params=params)
        response.raise_for_status()  # Raise an exception for HTTP errors
        data = response.json()

        if not data or "Error Message" in data:
            error_msg = data.get("Error Message", "Unknown error")
            print(f"API error for function {params.get('function')}, symbol {params.get('symbol')}: {error_msg}", file=sys.stderr)
            return None
        if "Note" in data: # Often indicates API limit
             print(f"Alpha Vantage API Note: {data['Note']}", file=sys.stderr)
             # Depending on the note, data might still be available.
             # If the main data payload is missing, treat as an error.
             expected_keys = {
                 "OVERVIEW": ["Symbol"],
                 "INCOME_STATEMENT": ["annualReports", "quarterlyReports"],
                 "BALANCE_SHEET": ["annualReports", "quarterlyReports"],
                 "CASH_FLOW": ["annualReports", "quarterlyReports"]
             }
             if params["function"] in expected_keys:
                 # Check if at least one expected data key is present
                 if not any(key in data for key in expected_keys[params["function"]]):
                     print(f"Missing expected data keys for {params['function']}. Returning None.", file=sys.stderr)
                     return None


        print(f"Successfully fetched data for {params.get('function')} for {params.get('symbol')}.", file=sys.stderr)
        return data

    except requests.exceptions.RequestException as e:
        print(f"API request failed for {params.get('function')} ({params.get('symbol')}): {e}", file=sys.stderr)
        return None
    except json.JSONDecodeError:
        print(f"Could not parse API response as JSON for {params.get('function')} ({params.get('symbol')}).", file=sys.stderr)
        return None
    except Exception as e:
        print(f"An unexpected error occurred while fetching data for {params.get('function')} ({params.get('symbol')}): {e}", file=sys.stderr)
        return None


def insert_financial_report_data(conn, ticker, report_type, report_list, period_type):
    """
    Inserts financial report data into the database.
    report_type can be 'income', 'balance', or 'cashflow'.
    period_type can be 'annual' or 'quarterly'.
    report_list is a list of dictionaries from Alpha Vantage API response.
    """
    table_name = f"{period_type}_financials"
    sql = f"""
    INSERT OR REPLACE INTO {table_name} (
        ticker, fiscal_date, report_type, reported_currency, data_json, retrieved_at
    ) VALUES (?, ?, ?, ?, ?, ?);
    """

    cursor = conn.cursor()
    retrieved_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    for report in report_list:
        fiscal_date = report.get("fiscalDateEnding")
        reported_currency = report.get("reportedCurrency")
        # Store the whole report dictionary as a JSON string
        data_json = json.dumps(report)

        if not fiscal_date:
            print(f"Skipping report for {ticker} due to missing fiscal date: {report}", file=sys.stderr)
            continue

        data_tuple = (
            ticker.upper(),
            fiscal_date,
            report_type,
            reported_currency,
            data_json,
            retrieved_time
        )
        try:
            cursor.execute(sql, data_tuple)
        except sqlite3.Error as e:
            print(f"Error inserting {period_type} {report_type} data for {ticker} on {fiscal_date}: {e}", file=sys.stderr)

    conn.commit()
    print(f"Successfully inserted/replaced {len(report_list)} {period_type} {report_type} reports for {ticker}.")


import time

def check_if_data_exists(conn, ticker, report_type, period_type):
    """Checks if financial data for a given ticker, report type, and period already exists."""
    table_name = f"{period_type}_financials"
    cursor = conn.cursor()
    sql = f"SELECT 1 FROM {table_name} WHERE ticker = ? AND report_type = ? LIMIT 1"
    try:
        cursor.execute(sql, (ticker.upper(), report_type.lower()))
        if cursor.fetchone():
            return True
    except sqlite3.Error:
        # This can happen if the table doesn't exist yet, which means no data exists.
        return False
    return False

def get_and_store_financials(conn, ticker, report_type, period_type):
    """
    Fetches and stores financial statement data (income, balance, cashflow)
    only if it doesn't already exist in the database.
    """
    # First, check if data for this specific period already exists.
    if check_if_data_exists(conn, ticker, report_type, period_type):
        print(f"Data for {ticker} {period_type} {report_type} already exists. Skipping download.", file=sys.stderr)
        return

    print(f"Data for {ticker} {period_type} {report_type} not found. Proceeding with download.", file=sys.stderr)

    function_map = {
        "income": "INCOME_STATEMENT",
        "balance": "BALANCE_SHEET",
        "cashflow": "CASH_FLOW"
    }
    function = function_map.get(report_type.lower())
    if not function:
        print(f"Invalid report type: {report_type}", file=sys.stderr)
        return

    # Add a delay to avoid hitting API rate limits, just in case.
    time.sleep(15)

    params = {
        "function": function,
        "symbol": ticker,
    }
    financial_data = fetch_alpha_vantage_data(params)

    if financial_data:
        annual_reports = financial_data.get("annualReports", [])
        quarterly_reports = financial_data.get("quarterlyReports", [])

        # Since one API call fetches both annual and quarterly,
        # we can be efficient and insert both if they are returned.
        if annual_reports:
            insert_financial_report_data(conn, ticker, report_type, annual_reports, 'annual')
        if quarterly_reports:
            insert_financial_report_data(conn, ticker, report_type, quarterly_reports, 'quarterly')

        # Verify if the originally requested data was inserted.
        if not check_if_data_exists(conn, ticker, report_type, period_type):
             print(f"Warning: API call for {report_type} was made, but no {period_type} data was inserted for {ticker}.", file=sys.stderr)

    else:
        print(f"No financial data retrieved from API for {ticker} for {report_type.replace('flow', ' flow')}.", file=sys.stderr)

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("Usage: python data/get_fundamental_data.py <ticker>", file=sys.stderr)
        print("Example: python data/get_fundamental_data.py AAPL", file=sys.stderr)
        sys.exit(1)

    ticker = sys.argv[1].upper()

    # 确保数据目录存在
    os.makedirs(DATA_DIR, exist_ok=True)
    os.makedirs(os.path.join(DATA_DIR, "tickers"), exist_ok=True)

    print("Ensuring database structure exists by running data/prepare_data.py...", file=sys.stderr)
    try:
        prepare_script_path = os.path.join(os.path.dirname(__file__), 'prepare_data.py')
        result = subprocess.run([sys.executable, prepare_script_path, ticker], check=True, capture_output=True, text=True, encoding='utf-8')
        if result.stdout.strip():
            print("prepare_data.py stdout:", result.stdout, file=sys.stderr)
        if result.stderr.strip():
             print("prepare_data.py stderr:", result.stderr, file=sys.stderr)
    except FileNotFoundError:
        print("Error: prepare_data.py not found. Make sure it exists in the data/ directory.", file=sys.stderr)
        sys.exit(1)
    except subprocess.CalledProcessError as e:
        print(f"Error running prepare_data.py: {e}", file=sys.stderr)
        if e.stdout: print("prepare_data.py stdout:", e.stdout, file=sys.stderr)
        if e.stderr: print("prepare_data.py stderr:", e.stderr, file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        print(f"An unexpected error occurred while running prepare_data.py: {e}", file=sys.stderr)
        sys.exit(1)


    if not ALPHAVANTAGE_API_KEY or ALPHAVANTAGE_API_KEY == "YOUR_ALPHA_VANTAGE_API_KEY" or ALPHAVANTAGE_API_KEY == "demo":
        print("Error: Please configure your Alpha Vantage API key in config.py.", file=sys.stderr)
        print("The 'demo' key has very limited access and will likely fail for most symbols.", file=sys.stderr)
        sys.exit(1)

    conn = create_connection(ticker)
    if conn is None:
        print("Failed to connect to database, cannot proceed.", file=sys.stderr)
        sys.exit(1)

    try:
        # Fetch and store Annual Financial Statements
        get_and_store_financials(conn, ticker, 'income', 'annual')
        get_and_store_financials(conn, ticker, 'balance', 'annual')
        get_and_store_financials(conn, ticker, 'cashflow', 'annual')

        # Fetch and store Quarterly Financial Statements
        get_and_store_financials(conn, ticker, 'income', 'quarterly')
        get_and_store_financials(conn, ticker, 'balance', 'quarterly')
        get_and_store_financials(conn, ticker, 'cashflow', 'quarterly')

        conn.commit() # Commit all changes at the end
        print(f"All financial data collection for {ticker} complete and committed to database.")

    except Exception as e:
        print(f"An error occurred during data collection for {ticker}: {e}", file=sys.stderr)
        if conn:
            conn.rollback() # Rollback any partial changes
            print("Database transaction rolled back.", file=sys.stderr)
    finally:
        if conn:
            conn.close()
            print("Database connection closed.", file=sys.stderr)


if __name__ == "__main__":
    main() 