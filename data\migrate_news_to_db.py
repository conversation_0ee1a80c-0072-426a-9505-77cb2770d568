#!/usr/bin/env python3
"""
新闻数据迁移脚本

将 data/news/ 目录下的所有新闻 JSON 文件导入到 AAPL_data.db 的 news 表中
"""

import os
import json
import sqlite3
import hashlib
from datetime import datetime
from typing import Dict, List, Any

def convert_time_format(time_str: str) -> str:
    """转换时间格式从 20250101T180019 到 2025-01-01 18:00:19"""
    if not time_str or len(time_str) < 15:
        return time_str
    
    try:
        # 解析格式: 20250101T180019
        date_part = time_str[:8]  # 20250101
        time_part = time_str[9:]  # 180019
        
        # 转换日期部分
        year = date_part[:4]
        month = date_part[4:6]
        day = date_part[6:8]
        
        # 转换时间部分
        hour = time_part[:2]
        minute = time_part[2:4]
        second = time_part[4:6]
        
        return f"{year}-{month}-{day} {hour}:{minute}:{second}"
    except:
        return time_str

def generate_article_id(title: str, time_published: str, ticker: str) -> str:
    """生成唯一的文章ID"""
    content = f"{ticker}_{title}_{time_published}"
    return hashlib.md5(content.encode()).hexdigest()

def parse_news_json(file_path: str, ticker: str) -> List[Dict[str, Any]]:
    """解析新闻JSON文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            news_data = json.load(f)
        
        # 检查数据结构，可能是嵌套在"feed"中
        if isinstance(news_data, dict) and 'feed' in news_data:
            news_items = news_data['feed']
        elif isinstance(news_data, list):
            news_items = news_data
        else:
            print(f"未知的JSON结构: {type(news_data)}")
            return []
        
        parsed_news = []
        for item in news_items:
            if not isinstance(item, dict):
                continue
                
            # 映射JSON字段到数据库字段
            article_data = {
                'article_id': generate_article_id(
                    item.get('title', ''), 
                    item.get('time_published', ''), 
                    ticker
                ),
                'ticker': ticker,
                'title': item.get('title', ''),
                'url': item.get('url', ''),
                'time_published': convert_time_format(item.get('time_published', '')),
                'authors': json.dumps(item.get('authors', [])),
                'summary': item.get('summary', ''),
                'banner_image': item.get('banner_image', ''),
                'source': item.get('source', ''),
                'category_within_source': item.get('category_within_source', ''),
                'source_domain': item.get('source_domain', ''),
                'topics': json.dumps(item.get('topics', [])),
                'overall_sentiment_score': item.get('overall_sentiment_score', 0.0),
                'overall_sentiment_label': item.get('overall_sentiment_label', 'neutral'),
                'ticker_sentiment': json.dumps(item.get('ticker_sentiment', []))
            }
            parsed_news.append(article_data)
        
        return parsed_news
    
    except Exception as e:
        print(f"解析文件 {file_path} 时出错: {e}")
        return []

def migrate_news_to_database(ticker: str = "AAPL") -> bool:
    """迁移新闻数据到数据库"""
    
    # 数据库路径
    db_path = f"tickers/{ticker}/{ticker}_data.db"
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return False
    
    # 新闻文件目录
    news_dir = "news"
    if not os.path.exists(news_dir):
        print(f"新闻目录不存在: {news_dir}")
        return False
    
    # 获取所有新闻文件
    news_files = [f for f in os.listdir(news_dir) 
                  if f.startswith(f"{ticker}_news_") and f.endswith(".json")]
    
    if not news_files:
        print(f"没有找到 {ticker} 的新闻文件")
        return False
    
    print(f"找到 {len(news_files)} 个新闻文件")
    
    # 连接数据库
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查news表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='news'")
        if not cursor.fetchone():
            print("news表不存在")
            conn.close()
            return False
        
        total_inserted = 0
        total_skipped = 0
        
        for news_file in sorted(news_files):
            file_path = os.path.join(news_dir, news_file)
            print(f"处理文件: {news_file}")
            
            # 解析JSON数据
            news_items = parse_news_json(file_path, ticker)
            
            if not news_items:
                print(f"  跳过空文件: {news_file}")
                continue
            
            # 批量插入数据
            inserted_count = 0
            for item in news_items:
                try:
                    cursor.execute("""
                        INSERT OR IGNORE INTO news (
                            article_id, ticker, title, url, time_published,
                            authors, summary, banner_image, source, category_within_source,
                            source_domain, topics, overall_sentiment_score,
                            overall_sentiment_label, ticker_sentiment
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        item['article_id'], item['ticker'], item['title'], item['url'],
                        item['time_published'], item['authors'], item['summary'],
                        item['banner_image'], item['source'], item['category_within_source'],
                        item['source_domain'], item['topics'], item['overall_sentiment_score'],
                        item['overall_sentiment_label'], item['ticker_sentiment']
                    ))
                    
                    if cursor.rowcount > 0:
                        inserted_count += 1
                    else:
                        total_skipped += 1
                        
                except sqlite3.Error as e:
                    print(f"  插入数据时出错: {e}")
                    total_skipped += 1
            
            total_inserted += inserted_count
            print(f"  插入 {inserted_count} 条新闻")
        
        # 提交事务
        conn.commit()
        
        # 验证结果
        cursor.execute("SELECT COUNT(*) FROM news WHERE ticker = ?", (ticker,))
        total_count = cursor.fetchone()[0]
        
        print(f"\n迁移完成:")
        print(f"  总插入: {total_inserted} 条")
        print(f"  总跳过: {total_skipped} 条")
        print(f"  数据库中总计: {total_count} 条新闻")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"数据库操作出错: {e}")
        if 'conn' in locals():
            conn.close()
        return False

def main():
    """主函数"""
    print("🚀 开始新闻数据迁移...")
    
    success = migrate_news_to_database("AAPL")
    
    if success:
        print("✅ 新闻数据迁移成功完成！")
    else:
        print("❌ 新闻数据迁移失败")
        return 1
    
    return 0

if __name__ == "__main__":
    import sys
    sys.exit(main()) 