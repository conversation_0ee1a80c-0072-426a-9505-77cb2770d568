# 多智能体交易系统核心框架

## 系统概述

基于大型语言模型的多层次智能体美股交易系统，通过多个专业化智能体协作进行交易决策，并使用Shapley值评估各智能体贡献度。

## 核心架构

### 1. 智能体层次结构

```
┌─────────────────────────────────────────────────────────────┐
│                    多智能体交易系统                          │
├─────────────────────────────────────────────────────────────┤
│  初始分析层                                                 │
│  ├── NAA (News Analyst Agent) - 新闻分析                   │
│  ├── TAA (Technical Analyst Agent) - 技术分析              │
│  └── FAA (Fundamental Analyst Agent) - 基本面分析          │
├─────────────────────────────────────────────────────────────┤
│  风险评估与展望层                                           │
│  ├── BOA (Bullish Outlook Agent) - 看多展望                │
│  ├── BeOA (Bearish Outlook Agent) - 看空展望               │
│  └── NOA (Neutral/Observational Agent) - 中立观察          │
├─────────────────────────────────────────────────────────────┤
│  交易执行层                                                 │
│  └── TRA (Trader Agent) - 交易执行                         │
└─────────────────────────────────────────────────────────────┘
```

### 2. 数据流架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   数据源层      │    │   处理层        │    │   智能体层      │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ • OHLCV数据     │───▶│ • 数据清洗      │───▶│ • 分析智能体    │
│ • 新闻数据      │    │ • 特征工程      │    │ • 决策智能体    │
│ • 基本面数据    │    │ • 时间序列处理  │    │ • 交易智能体    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                      │
┌─────────────────┐    ┌─────────────────┐           │
│   评估层        │    │   优化层        │           │
├─────────────────┤    ├─────────────────┤           │
│ • Shapley值计算 │◀───│ • 提示优化      │◀──────────┘
│ • 贡献度分析    │    │ • 策略调整      │
│ • 性能评估      │    │ • 参数优化      │
└─────────────────┘    └─────────────────┘
```

## 核心组件

### 1. 数据管理层

**TradingEnvironment** - 交易环境
- 数据加载和预处理
- 交易状态管理
- 环境状态更新

**DataCoordinator** - 数据协调器
- 按交易周组织数据
- 计算收益率和统计指标
- 数据完整性验证

### 2. 智能体协调层

**AgentCoordinator** - 智能体协调器
- 智能体生命周期管理
- 依赖关系处理
- 信息流控制

### 3. 交易执行层

**TradingEngine** - 交易执行引擎
- 交易会话管理
- 周级交易执行
- 性能监控

### 4. 评估优化层

**ContributionCalculator** - 贡献度计算器
- Shapley值计算
- 联盟生成
- 统计分析

## 关键数据结构

### 1. 智能体状态
```python
{
    "current_day": int,
    "stock_data": Dict[str, pd.DataFrame],
    "news_data": Dict[str, List[Dict]],
    "fundamental_data": Dict[str, Dict],
    "portfolio": Dict[str, float],
    "cash": float,
    "previous_outputs": Dict[str, Any]
}
```

### 2. 交易决策
```python
{
    "actions": Dict[str, float],  # 股票代码 -> 交易强度
    "reasoning": str,
    "confidence": float,
    "risk_assessment": Dict[str, Any]
}
```

## 执行流程

### 1. 初始化阶段
1. 加载配置和数据
2. 创建智能体实例
3. 初始化协调器
4. 设置交易环境

### 2. 交易执行阶段
1. **数据准备**: 获取当日市场数据
2. **分层执行**:
   - 初始分析层: NAA, TAA, FAA 并行分析
   - 风险评估层: BOA, BeOA, NOA 基于初始分析结果
   - 交易执行层: TRA 基于所有分析结果做决策
3. **交易执行**: 执行TRA的交易决策
4. **状态更新**: 更新组合和环境状态

### 3. 评估优化阶段
1. **贡献度计算**: 使用Shapley值评估各智能体贡献
2. **性能分析**: 计算收益率、风险指标等
3. **提示优化**: 基于表现优化智能体提示词
4. **策略调整**: 调整智能体参数和策略

## 关键算法

### 1. Shapley值计算
```python
def calculate_shapley_values(agents, coalition_function):
    """计算各智能体的Shapley值"""
    n = len(agents)
    shapley_values = {}
    
    for agent in agents:
        marginal_contributions = []
        for coalition in generate_coalitions(agents - {agent}):
            v_with = coalition_function(coalition | {agent})
            v_without = coalition_function(coalition)
            marginal_contributions.append(v_with - v_without)
        
        shapley_values[agent] = sum(marginal_contributions) / len(marginal_contributions)
    
    return shapley_values
```

### 2. 智能体依赖图
```python
AGENT_GRAPH = {
    "NAA": [],  # 无依赖
    "TAA": [],  # 无依赖
    "FAA": [],  # 无依赖
    "BOA": ["NAA", "TAA", "FAA"],  # 依赖初始分析
    "BeOA": ["NAA", "TAA", "FAA"], # 依赖初始分析
    "NOA": ["NAA", "TAA", "FAA"],  # 依赖初始分析
    "TRA": ["BOA", "BeOA", "NOA"]  # 依赖风险评估
}
```

## 扩展点

### 1. 新增智能体
1. 继承BaseAgent类
2. 实现process()和reflect()方法
3. 在配置中注册
4. 更新依赖图

### 2. 新增优化器
1. 继承基础优化器接口
2. 实现优化算法
3. 在协调器中集成

### 3. 新增数据源
1. 实现数据获取接口
2. 添加数据预处理逻辑
3. 更新环境配置

这个框架提供了一个清晰、可扩展的多智能体交易系统架构，支持复杂的智能体协作和优化策略。

论文描述
\section{System Architecture}

This paper introduces a collaborative multi-agent system (MAS) where specialized agents perform distinct tasks within a coordinated workflow to reach a final trading decision. The architecture mirrors the division of labor in human financial analysis teams, where experts with specialized knowledge contribute to a collective decision-making process.

\subsection{Agent Roles and Descriptions}
The system's architecture is organized into three distinct hierarchical layers: the Analyst Layer, which processes raw data; the Outlook Layer, which synthesizes analyses into narratives; and the Decision Layer, which makes the final trade execution.

\subsubsection*{The Analyst Layer}
Agents in this foundational layer are specialized in processing diverse, raw information sources and transforming them into structured, analytical outputs.
\begin{itemize}
    \item \textbf{News Analyst Agent (NAA):} This agent identifies significant market events and distills sentiment by analyzing vast streams of unstructured text data. It processes real-time feeds from news APIs, press releases, and social media, using Natural Language Processing (NLP) to perform sentiment analysis and event categorization. The agent's output is a structured JSON object for each asset, containing a sentiment score, a news summary, and categorized events.

    \item \textbf{Technical Analyst Agent (TAA):} This agent acts as a quantitative chartist to identify predictive patterns and trends in market data. By analyzing historical and real-time price and volume data (OHLCV), it calculates a suite of technical indicators (e.g., RSI, MACD), recognizes classical chart patterns, and determines key support and resistance levels. Its output is a structured JSON object detailing the asset's trend, active patterns, and key price levels.

    \item \textbf{Fundamental Analyst Agent (FAA):} This agent assesses an asset's intrinsic value by analyzing its financial health and the broader economic context. It ingests corporate financial statements, industry reports, and macroeconomic data to calculate key financial ratios, perform valuation analyses such as Discounted Cash Flow (DCF), and evaluate qualitative factors like competitive advantages. The output is a structured JSON object providing an estimated intrinsic value, a financial health summary, and a long-term qualitative assessment.
\end{itemize}

\subsubsection*{The Outlook Layer}
This intermediate layer is responsible for synthesizing the structured outputs from the various analyst agents. Each agent in this layer constructs a specific, coherent market narrative.
\begin{itemize}
    \item \textbf{Bullish Outlook Agent (BOA):} This agent synthesizes evidence from the analyst agents to construct a bullish market narrative, focusing on positive news, strong technical uptrends, and favorable fundamental valuations.

    \item \textbf{Bearish Outlook Agent (BeOA):} This agent integrates analytical data to build a bearish case, emphasizing negative sentiment, technical downtrends, and overvaluation signals.

    \item \textbf{Neutral Outlook Agent (NOA):} This agent constructs a neutral or sideways market narrative by balancing conflicting signals from the news, technical, and fundamental analyses.
\end{itemize}


\subsubsection*{The Decision Layer}
Positioned at the top of the hierarchy, this final layer makes the ultimate trading decision based on the competing narratives provided by the Outlook Layer.
\begin{itemize}
    \item \textbf{Trader Agent (TRA):} As the final decision-maker, this agent aggregates the competing narratives from the three Outlook Agents. It then makes the final trade decision (buy, sell, or hold) and determines the position size based on the conviction of the aggregated outlooks.
    
\end{itemize}
\begin{figure}
    \centering
    \includegraphics[width=1.0\linewidth]{LaTeX/framework.drawio.png}
    \caption{Framework}
    \label{fig:enter-label}
\end{figure}

\section{Contribution Assignment}
To optimize the multi-agent framework, we require a method to accurately quantify each agent's contribution to the system's overall performance. We address this by first modeling the agent collaboration as a Directed Acyclic Graph (DAG). This structure explicitly defines the information flow and dependencies, which is essential for fair credit assignment. The DAG formulation provides several key advantages: it enforces a logical, forward-moving workflow, reveals opportunities for parallel execution to improve efficiency, and makes contribution pathways explicit.

This graph-based representation, denoted as $G=(V,E)$, is foundational to our approach. It enables the application of the Shapely value—a generalization of the Shapley value from cooperative game theory for networked structures. The Shapely value allocates the system's collective payoff (e.g., portfolio returns) by evaluating how each agent's participation enhances the performance of feasible team coalitions, thereby capturing their nuanced, interaction-based contributions within the defined communication pathways.

The agent interaction graph is defined as follows: NAA, TAA, and FAA provide inputs to BOA, BeOA, and NOA, which in turn provide inputs to the final TRA.

\subsection{Characteristic Function}

The foundation of the Shapley value is the characteristic function, $v(S)$, which quantifies the ``worth'' or total value generated by a specific coalition of agents, $S$. In our framework, we define $v(S)$ as the performance of a trading simulation where only the agents belonging to coalition $S$ are active.

As the performance metric, we utilize the Sharpe Ratio, a standard measure of risk-adjusted return. The Sharpe Ratio assesses the return of an investment compared to a risk-free asset, adjusted for its volatility. It is calculated by dividing the average return in excess of the risk-free rate by the standard deviation of the investment's excess returns. The formula is expressed as:

\begin{equation}
\text{Sharpe Ratio} = \frac{E[R_p - R_f]}{\sigma_p}
\end{equation}

where $R_p$ is the asset's return, $R_f$ is the risk-free rate of return, $E[R_p - R_f]$ is the expected excess return, and $\sigma_p$ is the standard deviation of the asset's excess returns (i.e., its volatility).

Accordingly, we define our characteristic function for a coalition $S$ as:
\begin{equation}
v(S) = \text{SharpeRatio}(\text{Returns}(S))
\end{equation}
Here, $\text{Returns}(S)$ are the daily portfolio returns generated by the active agents in coalition $S$. A critical condition of our model is that a coalition must include the Trader Agent (TRA) to execute trades and generate non-zero returns. If the TRA is not part of coalition $S$, its characteristic value is inherently zero, i.e., $v(S) = 0$.


\subsection{Shapley Value Formulation}

The Shapley value, $\mu_i$, for an agent $i$ is its contribution computed on a modified game constrained by the communication graph $G$. A coalition $S$ is considered feasible only if its members form a connected subgraph. The value represents the agent's weighted average marginal contribution across all feasible coalitions it can join. The formula for the Shapley value of an agent $i$ is:
\begin{equation}
    \mu_i(v, G)=\sum_{S \subseteq V \setminus \{i\}} \frac{|S|!(|V| - |S| - 1)!}{|V|!} [v(S \cup \{i\})-v(S)]
\end{equation}
where the sum is over all coalitions $S$ not containing agent $i$. The term $v(S \cup \{i\}) - v(S)$ is the marginal contribution of agent $i$ to coalition $S$. This formulation ensures that an agent's contribution is evaluated based on its ability to enhance team performance within the graph's structural constraints.

However, evaluating the characteristic function $v(S)$ for every feasible coalition via discrete simulations is computationally prohibitive. To overcome this, we implement a cache-aware workflow that decouples expensive analysis generation from the numerous trading simulations, reducing redundant computations and ensuring evaluation consistency. The workflow (Figure \ref{fig:workflow}) proceeds in three phases:

\begin{figure}[t]
\centering
% Note: The following line is a placeholder.
% Please generate your diagram from the Mermaid code,
% save it as an image file (e.g., workflow.pdf),
% and uncomment the line below.
\includegraphics[width=0.9\columnwidth]{figure1.png}
\caption{The cache-aware workflow for calculating Shapley values. Phase 1 generates and caches all required analyses. Phase 2 runs parallel simulations for each feasible coalition, fetching cached data. Phase 3 calculates the final contribution values.}
\label{fig:workflow}
\end{figure}

\begin{enumerate}
    \item \textbf{Phase 1: Analysis Generation \& Caching.} At the start of an evaluation period, the NAA, TAA, and FAA run only once. Their outputs are stored in a centralized Analysis Cache.

    \item \textbf{Phase 2: Parallel Coalition Evaluation.} The system launches parallel trading simulations for each feasible coalition. Agents within each simulation fetch the required, pre-computed analyses from the cache. The resulting Sharpe Ratio for each coalition, $v(S)$, is stored.

    \item \textbf{Phase 3: Final Calculation.} Once all coalition values are computed, the Shapley value $\mu_i$ for each agent is calculated using the stored $v(S)$ values.
\end{enumerate}

This optimized workflow makes calculating the Shapley value practical, providing reliable and efficient credit assignment.


\section{Prompt Optimization via OPRO}
Having established a robust credit assignment method, we now turn to improving individual agent performance. To this end, we employ a variant of the Optimization by PROmpting (OPRO) methodology. Our implementation, termed Advanced ORPO (Offline Reinforcement Prompt Optimization), adapts this concept for the multi-agent trading domain. The process is triggered periodically or when system performance degrades below a set threshold.

\subsection{The OPRO Meta-Prompt}
The core of this process is a "meta-prompt" that instructs a powerful optimizer LLM to generate an improved prompt for a target agent. The meta-prompt includes the agent's task description, its current prompt, and quantitative performance feedback. An example is shown in Listing \ref{lst:meta_prompt}.

\begin{listing}[tb]
\caption{Example meta-prompt for the News Analyst Agent.}
\label{lst:meta_prompt}
\begin{lstlisting}[basicstyle={\footnotesize\ttfamily}, breaklines=true]
As an AI prompt engineer, your task is to optimize the prompt for a News Analyst Agent.

**Agent's Current Prompt:**
"{current_naa_prompt}"

**Recent Performance Feedback:**
- Shapely Value: -0.05 (Negative contribution)
- Contribution Change: -0.02 (Declining performance)
- System Sharpe Ratio: 0.3 (Below target)

**Optimization Goal:**
Generate a new prompt that improves the agent's ability to distinguish market-moving news from noise and to quantify sentiment more accurately. The new prompt should lead to a positive contribution to the team.
\end{lstlisting}
\end{listing}

\begin{algorithm}[tb]
\caption{The Agent Optimization Loop}
\label{alg:optimization_loop}
\textbf{Input}: A set of agents $A = \{a_1, a_2, ..., a_n\}$ \\
\textbf{Parameter}: Performance threshold $\theta$, Meta-Prompt $P_M$ \\
\textbf{Output}: Optimized agents
\begin{algorithmic}[1] %[1] enables line numbers
\WHILE{System is operational}
    \STATE Evaluate System Performance
    \STATE $Perf \leftarrow \text{MeasureSystemPerformance}(A)$
    
    \STATE Assign Contribution
    \STATE Let $V$ be a map for agent contributions
    \FOR{each agent $a_i \in A$}
        \STATE $V[a_i] \leftarrow \text{CalculateShapleyValue}(a_i, Perf)$
    \ENDFOR
    
    \STATE Target Agents for Optimization
    \STATE $A_{target} \leftarrow \emptyset$
    \FOR{each agent $a_i \in A$}
        \IF{$V[a_i] < \theta$ \OR $V[a_i]$ is declining}
            \STATE Add $a_i$ to $A_{target}$
        \ENDIF
    \ENDFOR
    
    \STATE Generate New Prompts
    \FOR{each agent $a_j \in A_{target}$}
        \STATE $prompt_{new} \leftarrow \text{GenerateNewPrompt}(P_M, a_j)$
        
        \STATE Deploy Updated Prompts
        \STATE $\text{UpdateAgent}(a_j, prompt_{new})$
    \ENDFOR
    
    \STATE Iterate
    \STATE Wait for the next evaluation cycle...
\ENDWHILE
\end{algorithmic}
\end{algorithm}


This iterative loop establishes a self-improving system where agent capabilities are continuously refined based on quantitative, contribution-aware feedback, driving the system's evolution towards higher performance.
