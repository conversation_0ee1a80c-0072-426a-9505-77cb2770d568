# 综合数据存储系统使用指南

## 概述

本文档介绍如何使用OPRO系统的综合数据存储功能。该系统提供了全面的数据收集、存储、分析和备份功能，帮助您更好地管理和分析交易数据、提示词优化记录以及系统性能指标。

## 系统架构

综合数据存储系统包含以下核心组件：

1. **综合存储管理器** - 统一的数据存储接口
2. **交易数据收集器** - 收集每日交易数据
3. **提示词优化跟踪器** - 跟踪提示词优化历史
4. **可视化管理器** - 生成股价图表和分析
5. **A/B测试框架** - 验证优化效果
6. **数据分析工具** - 生成分析报告
7. **备份管理器** - 自动化数据备份

## 快速开始

### 1. 启用数据存储功能

默认情况下，数据存储功能是启用的。您可以通过以下方式控制：

```bash
# 启用数据存储（默认）
python run_opro_system.py --mode evaluation --enable-data-storage

# 禁用数据存储
python run_opro_system.py --mode evaluation --disable-data-storage
```

### 2. 基本配置

在 `config/opro_config.json` 中配置数据存储参数：

```json
{
  "storage": {
    "comprehensive_storage": {
      "enabled": true,
      "base_path": "data",
      "trading_data_path": "data/trading",
      "prompts_data_path": "data/prompts",
      "visualizations_path": "data/visualizations",
      "exports_path": "data/exports",
      "backups_path": "data/backups",
      "database_path": "data/comprehensive_storage.db",
      "auto_backup_interval_hours": 24,
      "data_validation_enabled": true,
      "compression_enabled": true,
      "max_file_size_mb": 100
    }
  }
}
```

## 主要功能

### 1. 每日交易数据存储

系统自动收集和存储以下交易数据：

- **智能体输入数据**：市场信号、分析输入、决策因子
- **智能体输出数据**：交易决策、推荐、置信度分数
- **每日盈亏记录**：每个智能体和整体系统的盈亏
- **市场条件**：股价、成交量、市场指标
- **时间戳和元数据**：完整的数据追踪信息

#### 使用示例

```bash
# 运行评估并自动收集交易数据
python run_opro_system.py --mode evaluation --stocks AAPL MSFT --start-date 2024-01-01 --end-date 2024-01-31
```

### 2. 提示词优化跟踪

跟踪提示词优化的完整历史：

- **原始提示词保存**：优化前的提示词版本
- **优化历史记录**：每次优化的详细记录
- **性能指标对比**：优化前后的性能变化
- **优化原因分析**：记录优化决策的依据

#### 使用示例

```bash
# 运行优化模式并跟踪提示词变化
python run_opro_system.py --mode optimization --enable-opro
```

### 3. 股价可视化

生成多种类型的股价图表：

- **线图**：基本价格走势
- **蜡烛图**：OHLC数据展示
- **技术指标图**：RSI、MACD、布林带等
- **交易决策对比图**：价格走势与交易决策的对比

#### 使用示例

```bash
# 生成可视化图表
python run_opro_system.py --mode evaluation --stocks AAPL --start-date 2024-01-01 --end-date 2024-01-31
```

### 4. A/B测试框架

验证提示词优化效果：

- **对比实验设计**：原始vs优化提示词
- **统计显著性测试**：t检验、ANOVA等
- **性能指标对比**：量化优化效果
- **置信度评估**：结果可靠性分析

### 5. 数据分析和报告

生成详细的分析报告：

#### 交易性能分析报告

```bash
# 生成交易性能分析报告
python run_opro_system.py --analysis-report trading --export-format excel
```

#### 提示词优化分析报告

```bash
# 生成提示词优化分析报告
python run_opro_system.py --analysis-report optimization --export-format excel
```

#### 综合分析报告

```bash
# 生成综合分析报告
python run_opro_system.py --analysis-report comprehensive
```

### 6. 数据备份和恢复

自动化数据备份功能：

#### 创建备份

```bash
# 创建完整备份
python run_opro_system.py --create-backup --backup-type full

# 创建增量备份
python run_opro_system.py --create-backup --backup-type incremental

# 自动选择备份类型
python run_opro_system.py --create-backup --backup-type auto
```

#### 查看备份状态

```bash
# 显示备份系统状态
python run_opro_system.py --backup-status

# 列出所有备份
python run_opro_system.py --list-backups
```

## 数据导出

### 导出所有数据

```bash
# 导出为Excel格式
python run_opro_system.py --export-all-data --export-format excel

# 导出为CSV格式
python run_opro_system.py --export-all-data --export-format csv

# 导出为JSON格式
python run_opro_system.py --export-all-data --export-format json
```

### 查看数据报告

```bash
# 生成数据存储系统报告
python run_opro_system.py --data-report
```

## 使用案例

### 案例1：完整的交易实验流程

```bash
# 1. 运行初始评估
python run_opro_system.py --mode evaluation --stocks AAPL MSFT GOOGL \
  --start-date 2024-01-01 --end-date 2024-01-31 --enable-data-storage

# 2. 进行提示词优化
python run_opro_system.py --mode optimization --enable-opro

# 3. 生成分析报告
python run_opro_system.py --analysis-report comprehensive

# 4. 创建备份
python run_opro_system.py --create-backup --backup-type full
```

### 案例2：定期数据分析

```bash
# 每周生成交易性能报告
python run_opro_system.py --analysis-report trading --export-format excel

# 每月生成提示词优化报告
python run_opro_system.py --analysis-report optimization --export-format excel

# 查看系统状态
python run_opro_system.py --data-report --backup-status
```

### 案例3：数据管理和维护

```bash
# 导出所有数据进行外部分析
python run_opro_system.py --export-all-data --export-format csv

# 创建定期备份
python run_opro_system.py --create-backup --backup-type incremental

# 检查备份状态
python run_opro_system.py --list-backups
```

## 数据结构说明

### 交易会话数据

```json
{
  "session_id": "session_20240101_120000_abc123",
  "timestamp": "2024-01-01T12:00:00",
  "agent_inputs": {
    "NAA": {
      "market_signals": {...},
      "analysis_inputs": {...},
      "decision_factors": {...}
    }
  },
  "agent_outputs": {
    "NAA": {
      "trading_decisions": {...},
      "recommendations": [...],
      "confidence_scores": {...}
    }
  },
  "profit_loss": {
    "NAA": 150.25,
    "TAA": -50.10,
    "system_total": 100.15
  },
  "market_conditions": {
    "stock_prices": {...},
    "market_indicators": {...},
    "volatility_metrics": {...}
  }
}
```

### 提示词优化记录

```json
{
  "optimization_id": "opt_NAA_20240101_120000_def456",
  "agent_id": "NAA",
  "timestamp": "2024-01-01T12:00:00",
  "original_prompt": "原始提示词内容...",
  "optimized_prompt": "优化后提示词内容...",
  "optimization_reason": "基于性能指标改进...",
  "performance_metrics": {
    "improvement": 0.15,
    "confidence": 0.85
  },
  "version_info": {
    "original_version": "hash123",
    "optimized_version": "hash456"
  }
}
```

## 配置选项详解

### 存储配置

- `enabled`: 是否启用数据存储功能
- `base_path`: 数据存储根目录
- `auto_backup_interval_hours`: 自动备份间隔（小时）
- `data_validation_enabled`: 是否启用数据验证
- `compression_enabled`: 是否启用备份压缩
- `max_file_size_mb`: 单个文件最大大小限制

### 备份配置

- `backup_interval_hours`: 备份间隔
- `max_backups_to_keep`: 保留的最大备份数量
- `incremental_backup`: 是否启用增量备份
- `backup_validation`: 是否验证备份完整性

## 故障排除

### 常见问题

1. **数据存储功能未启用**
   - 检查配置文件中的 `enabled` 设置
   - 确认命令行参数没有使用 `--disable-data-storage`

2. **备份创建失败**
   - 检查磁盘空间是否充足
   - 确认备份目录的写入权限
   - 查看日志文件获取详细错误信息

3. **数据导出失败**
   - 确认导出目录存在且可写
   - 检查是否有足够的内存处理大量数据
   - 尝试使用不同的导出格式

### 日志查看

```bash
# 查看详细日志
python run_opro_system.py --mode evaluation --log-file system.log

# 查看日志文件
tail -f system.log
```

## 性能优化建议

1. **定期清理旧数据**：设置合适的数据保留策略
2. **使用增量备份**：减少备份时间和存储空间
3. **启用数据压缩**：节省存储空间
4. **合理设置备份间隔**：平衡数据安全和系统性能
5. **监控磁盘空间**：确保有足够的存储空间

## 扩展功能

系统设计为模块化架构，支持以下扩展：

1. **自定义数据收集器**：添加新的数据源
2. **自定义分析工具**：实现特定的分析需求
3. **自定义可视化**：创建专门的图表类型
4. **自定义备份策略**：实现特殊的备份需求

## 技术支持

如需技术支持或有任何问题，请：

1. 查看日志文件获取详细错误信息
2. 检查配置文件设置
3. 参考本文档的故障排除部分
4. 联系技术支持团队

---

*本文档版本：1.0*  
*最后更新：2025-07-04*
