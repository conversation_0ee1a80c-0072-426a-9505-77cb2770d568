#!/usr/bin/env python3
"""
增强提示词优化系统测试脚本

测试完整的周期性提示词优化工作流：
1. 周级回测和贡献分析
2. 提示词优化和A/B测试
3. 综合数据存储
4. 性能跟踪和报告生成
"""

import os
import sys
import json
import logging
import numpy as np
from datetime import datetime, timedelta

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data.enhanced_prompt_optimizer import EnhancedPromptOptimizer

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(f'test_prompt_optimization_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log', encoding='utf-8')
        ]
    )
    return logging.getLogger(__name__)

def create_test_config():
    """创建测试配置"""
    return {
        "data_dir": "data/test_prompt_optimization",
        "optimization_threshold": 0.8,
        "ab_test_duration_days": 7,
        "min_improvement_ratio": 0.05
    }

def generate_mock_weekly_data(week_number: int):
    """生成模拟的周级数据"""
    # 模拟智能体贡献度（有些智能体表现不佳）
    agent_contributions = {
        "NAA": np.random.normal(0.85, 0.1),  # 新闻分析智能体
        "TAA": np.random.normal(0.75, 0.15),  # 技术分析智能体
        "FAA": np.random.normal(0.90, 0.08),  # 基本面分析智能体
        "TRA": np.random.normal(0.70, 0.12),  # 交易智能体
        "BOA": np.random.normal(0.65, 0.20),  # 行为分析智能体（表现较差）
    }
    
    # 确保值在合理范围内
    for agent_id in agent_contributions:
        agent_contributions[agent_id] = max(0.1, min(1.0, agent_contributions[agent_id]))
    
    # 模拟联盟性能
    coalition_performances = {}
    for i in range(10):  # 生成10个联盟的性能数据
        coalition_key = f"coalition_{i}"
        # 联盟性能基于包含的智能体
        base_performance = np.random.normal(0.75, 0.15)
        coalition_performances[coalition_key] = max(0.0, min(1.0, base_performance))
    
    # Shapley值（与贡献度相关但有所不同）
    shapley_values = {}
    for agent_id, contribution in agent_contributions.items():
        # Shapley值通常与贡献度相关但有一定变化
        shapley_values[agent_id] = contribution + np.random.normal(0, 0.05)
        shapley_values[agent_id] = max(0.0, min(1.0, shapley_values[agent_id]))
    
    # 计算日期
    start_date = (datetime(2025, 1, 1) + timedelta(weeks=week_number-1)).strftime('%Y-%m-%d')
    end_date = (datetime(2025, 1, 1) + timedelta(weeks=week_number) - timedelta(days=1)).strftime('%Y-%m-%d')
    
    return {
        "week_number": week_number,
        "start_date": start_date,
        "end_date": end_date,
        "agent_contributions": agent_contributions,
        "coalition_performances": coalition_performances,
        "shapley_values": shapley_values
    }

def simulate_ab_test_results(agent_id: str, original_performance: float):
    """模拟A/B测试结果"""
    # 模拟优化后的性能（有一定概率改进）
    improvement_chance = 0.7  # 70%的概率有改进
    
    if np.random.random() < improvement_chance:
        # 有改进，改进幅度在5%-25%之间
        improvement_factor = 1 + np.random.uniform(0.05, 0.25)
        optimized_performance = min(1.0, original_performance * improvement_factor)
    else:
        # 无改进或轻微下降
        change_factor = np.random.uniform(0.95, 1.02)
        optimized_performance = max(0.1, original_performance * change_factor)
    
    return {
        "control": {
            "performance": original_performance,
            "sample_size": 100,
            "variance": 0.02
        },
        "variant_1": {
            "performance": optimized_performance,
            "sample_size": 100,
            "variance": 0.02
        }
    }

def test_weekly_optimization_workflow(optimizer: EnhancedPromptOptimizer, logger: logging.Logger):
    """测试周期性优化工作流"""
    logger.info("=" * 60)
    logger.info("测试周期性优化工作流")
    logger.info("=" * 60)
    
    # 模拟4周的数据
    weeks_to_simulate = 4
    ab_tests_to_complete = []
    
    for week in range(1, weeks_to_simulate + 1):
        logger.info(f"\n--- 第 {week} 周 ---")
        
        # 生成本周的模拟数据
        weekly_data = generate_mock_weekly_data(week)
        
        logger.info(f"智能体贡献度: {weekly_data['agent_contributions']}")
        
        # 处理周级性能数据
        result = optimizer.process_weekly_performance(
            week_number=weekly_data["week_number"],
            start_date=weekly_data["start_date"],
            end_date=weekly_data["end_date"],
            agent_contributions=weekly_data["agent_contributions"],
            coalition_performances=weekly_data["coalition_performances"],
            shapley_values=weekly_data["shapley_values"]
        )
        
        if result["success"]:
            logger.info(f"第 {week} 周处理成功")
            logger.info(f"需要优化的智能体: {result['optimization_triggers']}")
            
            # 收集A/B测试信息
            for opt_result in result["optimization_results"]:
                if opt_result["success"] and opt_result.get("ab_test_started"):
                    ab_tests_to_complete.append({
                        "agent_id": opt_result["agent_id"],
                        "optimization_id": opt_result["optimization_id"],
                        "original_performance": weekly_data["agent_contributions"][opt_result["agent_id"]]
                    })
        else:
            logger.error(f"第 {week} 周处理失败: {result.get('error')}")
    
    # 模拟完成A/B测试
    logger.info(f"\n--- 完成A/B测试 ---")
    logger.info(f"需要完成 {len(ab_tests_to_complete)} 个A/B测试")
    
    for test_info in ab_tests_to_complete:
        # 模拟A/B测试结果
        test_results = simulate_ab_test_results(
            test_info["agent_id"], 
            test_info["original_performance"]
        )
        
        # 这里应该有实际的test_id，暂时使用模拟的
        test_id = f"test_{test_info['agent_id']}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        completion_result = optimizer.complete_ab_test(test_id, test_results)
        
        if completion_result["success"]:
            logger.info(f"A/B测试完成: {test_info['agent_id']}, "
                       f"获胜变体: {completion_result['winning_variant']}, "
                       f"改进率: {completion_result['improvement_ratio']:.2%}")
        else:
            logger.warning(f"A/B测试完成失败: {test_info['agent_id']}")

def test_reporting_and_analysis(optimizer: EnhancedPromptOptimizer, logger: logging.Logger):
    """测试报告和分析功能"""
    logger.info("=" * 60)
    logger.info("测试报告和分析功能")
    logger.info("=" * 60)
    
    # 生成优化报告
    report_result = optimizer.generate_optimization_report(start_week=1, end_week=4)
    
    if report_result["success"]:
        logger.info("优化报告生成成功")
        
        report = report_result["report"]
        summary = report["summary"]
        
        logger.info(f"报告摘要:")
        logger.info(f"  总优化次数: {summary['total_optimizations']}")
        logger.info(f"  成功优化次数: {summary['successful_optimizations']}")
        logger.info(f"  平均改进率: {summary['average_improvement']:.2%}")
        logger.info(f"  成功率: {summary['success_rate']:.1f}%")
        
        # 显示智能体性能分析
        agent_performance = report.get("agent_performance", {})
        if agent_performance and isinstance(agent_performance, dict):
            logger.info(f"智能体性能分析:")
            for agent_id, stats in agent_performance.items():
                if isinstance(stats, dict):
                    logger.info(f"  {agent_id}: 优化次数 {stats.get('total_optimizations', 0)}, "
                               f"成功率 {stats.get('success_rate', 0):.1f}%")
        
        # 显示建议
        recommendations = report.get("recommendations", [])
        if recommendations:
            logger.info(f"优化建议:")
            for i, rec in enumerate(recommendations, 1):
                logger.info(f"  {i}. {rec}")
        
        logger.info(f"详细报告已保存至: {report_result['report_path']}")
    else:
        logger.error(f"生成报告失败: {report_result.get('error')}")
    
    # 获取系统统计
    stats = optimizer.get_stats()
    logger.info(f"\n系统统计:")
    for key, value in stats.items():
        logger.info(f"  {key}: {value}")

def test_data_export(optimizer: EnhancedPromptOptimizer, logger: logging.Logger):
    """测试数据导出功能"""
    logger.info("=" * 60)
    logger.info("测试数据导出功能")
    logger.info("=" * 60)
    
    # 测试不同格式的导出
    formats = ["json", "csv"]
    
    for export_format in formats:
        logger.info(f"测试 {export_format.upper()} 格式导出...")
        
        export_result = optimizer.export_data(export_format=export_format)
        
        if export_result["success"]:
            logger.info(f"{export_format.upper()} 导出成功:")
            for file_path in export_result["exported_files"]:
                logger.info(f"  - {file_path}")
        else:
            logger.error(f"{export_format.upper()} 导出失败: {export_result.get('error')}")

def main():
    """主函数"""
    logger = setup_logging()
    
    logger.info("=" * 80)
    logger.info("增强提示词优化系统测试")
    logger.info("=" * 80)
    
    # 创建配置
    config = create_test_config()
    logger.info(f"测试配置: {config}")
    
    # 初始化优化器
    optimizer = EnhancedPromptOptimizer(config=config, logger=logger)
    
    try:
        # 测试周期性优化工作流
        test_weekly_optimization_workflow(optimizer, logger)
        
        # 测试报告和分析功能
        test_reporting_and_analysis(optimizer, logger)
        
        # 测试数据导出功能
        test_data_export(optimizer, logger)
        
        logger.info("=" * 80)
        logger.info("所有测试完成!")
        logger.info("=" * 80)
        
        # 显示最终统计
        final_stats = optimizer.get_stats()
        logger.info("最终系统统计:")
        for key, value in final_stats.items():
            logger.info(f"  {key}: {value}")
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
